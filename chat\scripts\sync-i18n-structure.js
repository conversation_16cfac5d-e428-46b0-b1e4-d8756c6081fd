const fs = require('fs');
const path = require('path');

// 语言映射配置
const languageTranslations = {
  'en': {
    "app": {
      "noDifyConfig": "No Dify app configuration available"
    },
    "chat": {
      "aiGenerated": "Content generated by AI, for reference only",
      "newConversation": "New Conversation",
      "newSession": "New Session",
      "conversationList": "Conversation List",
      "noConversations": "No conversations"
    },
    "common": {
      "logout": "Logout",
      "login": "Login",
      "backToHome": "Back to Home"
    },
    "subscription": {
      "upgrade": "Upgrade Subscription",
      "modify": "Modify Subscription",
      "subscribe": "Subscribe",
      "free": "Free",
      "monthly": "Monthly",
      "yearly": "Yearly"
    },
    "error": {
      "getAppListFailed": "Failed to get app list",
      "unauthorized": "Unauthorized, please check your configuration",
      "waitingUpload": "Please wait for all files to upload",
      "fieldsRequired": "cannot be empty",
      "emptyMessage": "Message content is empty",
      "emptyMessageTip": "The user may have clicked the stop response button during content generation"
    }
  },
  'tw': {
    "app": {
      "noDifyConfig": "暫無 Dify 應用配置"
    },
    "chat": {
      "aiGenerated": "內容由 AI 生成，僅供參考",
      "newConversation": "新增對話",
      "newSession": "新會話",
      "conversationList": "對話列表",
      "noConversations": "暫無會話"
    },
    "common": {
      "logout": "退出",
      "login": "登錄",
      "backToHome": "返回首頁"
    },
    "subscription": {
      "upgrade": "升級訂閱",
      "modify": "修改訂閱",
      "subscribe": "訂閱",
      "free": "免費",
      "monthly": "連續包月",
      "yearly": "連續包年"
    },
    "error": {
      "getAppListFailed": "獲取應用列表失敗",
      "unauthorized": "未授權，請檢查您的配置",
      "waitingUpload": "請等待所有文件上傳完成",
      "fieldsRequired": "不能為空",
      "emptyMessage": "消息內容為空",
      "emptyMessageTip": "可能是用戶在生成內容的過程中點擊了停止響應按鈕"
    }
  },
  'vi': {
    "app": {
      "noDifyConfig": "Không có cấu hình ứng dụng Dify"
    },
    "chat": {
      "aiGenerated": "Nội dung được tạo bởi AI, chỉ để tham khảo",
      "newConversation": "Cuộc trò chuyện mới",
      "newSession": "Phiên mới",
      "conversationList": "Danh sách cuộc trò chuyện",
      "noConversations": "Không có cuộc trò chuyện"
    },
    "common": {
      "logout": "Đăng xuất",
      "login": "Đăng nhập",
      "backToHome": "Về trang chủ"
    },
    "subscription": {
      "upgrade": "Nâng cấp đăng ký",
      "modify": "Sửa đổi đăng ký",
      "subscribe": "Đăng ký",
      "free": "Miễn phí",
      "monthly": "Hàng tháng",
      "yearly": "Hàng năm"
    },
    "error": {
      "getAppListFailed": "Không thể lấy danh sách ứng dụng",
      "unauthorized": "Không được phép, vui lòng kiểm tra cấu hình của bạn",
      "waitingUpload": "Vui lòng đợi tất cả tệp tải lên",
      "fieldsRequired": "không thể để trống",
      "emptyMessage": "Nội dung tin nhắn trống",
      "emptyMessageTip": "Người dùng có thể đã nhấp vào nút dừng phản hồi trong quá trình tạo nội dung"
    }
  },
  'es': {
    "app": {
      "noDifyConfig": "No hay configuración de aplicación Dify disponible"
    },
    "chat": {
      "aiGenerated": "Contenido generado por IA, solo para referencia",
      "newConversation": "Nueva conversación",
      "newSession": "Nueva sesión",
      "conversationList": "Lista de conversaciones",
      "noConversations": "No hay conversaciones"
    },
    "common": {
      "logout": "Cerrar sesión",
      "login": "Iniciar sesión",
      "backToHome": "Volver al inicio"
    },
    "subscription": {
      "upgrade": "Actualizar suscripción",
      "modify": "Modificar suscripción",
      "subscribe": "Suscribirse",
      "free": "Gratis",
      "monthly": "Mensual",
      "yearly": "Anual"
    },
    "error": {
      "getAppListFailed": "Error al obtener la lista de aplicaciones",
      "unauthorized": "No autorizado, verifique su configuración",
      "waitingUpload": "Espere a que se suban todos los archivos",
      "fieldsRequired": "no puede estar vacío",
      "emptyMessage": "El contenido del mensaje está vacío",
      "emptyMessageTip": "El usuario puede haber hecho clic en el botón de detener respuesta durante la generación de contenido"
    }
  },
  'ar': {
    "app": {
      "noDifyConfig": "لا يوجد تكوين تطبيق Dify متاح"
    },
    "chat": {
      "aiGenerated": "المحتوى مُولد بواسطة الذكاء الاصطناعي، للمرجع فقط",
      "newConversation": "محادثة جديدة",
      "newSession": "جلسة جديدة",
      "conversationList": "قائمة المحادثات",
      "noConversations": "لا توجد محادثات"
    },
    "common": {
      "logout": "تسجيل الخروج",
      "login": "تسجيل الدخول",
      "backToHome": "العودة للرئيسية"
    },
    "subscription": {
      "upgrade": "ترقية الاشتراك",
      "modify": "تعديل الاشتراك",
      "subscribe": "اشتراك",
      "free": "مجاني",
      "monthly": "شهري",
      "yearly": "سنوي"
    },
    "error": {
      "getAppListFailed": "فشل في الحصول على قائمة التطبيقات",
      "unauthorized": "غير مصرح، يرجى التحقق من التكوين الخاص بك",
      "waitingUpload": "يرجى انتظار رفع جميع الملفات",
      "fieldsRequired": "لا يمكن أن يكون فارغًا",
      "emptyMessage": "محتوى الرسالة فارغ",
      "emptyMessageTip": "قد يكون المستخدم قد نقر على زر إيقاف الاستجابة أثناء توليد المحتوى"
    }
  }
};

// 继续添加其他语言...
const moreLanguages = {
  'id': {
    "app": {
      "noDifyConfig": "Tidak ada konfigurasi aplikasi Dify yang tersedia"
    },
    "chat": {
      "aiGenerated": "Konten dibuat oleh AI, hanya untuk referensi",
      "newConversation": "Percakapan Baru",
      "newSession": "Sesi Baru",
      "conversationList": "Daftar Percakapan",
      "noConversations": "Tidak ada percakapan"
    },
    "common": {
      "logout": "Keluar",
      "login": "Masuk",
      "backToHome": "Kembali ke Beranda"
    },
    "subscription": {
      "upgrade": "Upgrade Langganan",
      "modify": "Ubah Langganan",
      "subscribe": "Berlangganan",
      "free": "Gratis",
      "monthly": "Bulanan",
      "yearly": "Tahunan"
    },
    "error": {
      "getAppListFailed": "Gagal mendapatkan daftar aplikasi",
      "unauthorized": "Tidak diotorisasi, periksa konfigurasi Anda",
      "waitingUpload": "Harap tunggu semua file diunggah",
      "fieldsRequired": "tidak boleh kosong",
      "emptyMessage": "Konten pesan kosong",
      "emptyMessageTip": "Pengguna mungkin telah mengklik tombol hentikan respons selama pembuatan konten"
    }
  },
  'pt': {
    "app": {
      "noDifyConfig": "Nenhuma configuração de aplicativo Dify disponível"
    },
    "chat": {
      "aiGenerated": "Conteúdo gerado por IA, apenas para referência",
      "newConversation": "Nova Conversa",
      "newSession": "Nova Sessão",
      "conversationList": "Lista de Conversas",
      "noConversations": "Nenhuma conversa"
    },
    "common": {
      "logout": "Sair",
      "login": "Entrar",
      "backToHome": "Voltar ao Início"
    },
    "subscription": {
      "upgrade": "Atualizar Assinatura",
      "modify": "Modificar Assinatura",
      "subscribe": "Assinar",
      "free": "Grátis",
      "monthly": "Mensal",
      "yearly": "Anual"
    },
    "error": {
      "getAppListFailed": "Falha ao obter lista de aplicativos",
      "unauthorized": "Não autorizado, verifique sua configuração",
      "waitingUpload": "Aguarde o upload de todos os arquivos",
      "fieldsRequired": "não pode estar vazio",
      "emptyMessage": "Conteúdo da mensagem está vazio",
      "emptyMessageTip": "O usuário pode ter clicado no botão parar resposta durante a geração de conteúdo"
    }
  },
  'ja': {
    "app": {
      "noDifyConfig": "利用可能なDifyアプリケーション設定がありません"
    },
    "chat": {
      "aiGenerated": "AIによって生成されたコンテンツ、参考用のみ",
      "newConversation": "新しい会話",
      "newSession": "新しいセッション",
      "conversationList": "会話リスト",
      "noConversations": "会話がありません"
    },
    "common": {
      "logout": "ログアウト",
      "login": "ログイン",
      "backToHome": "ホームに戻る"
    },
    "subscription": {
      "upgrade": "サブスクリプションをアップグレード",
      "modify": "サブスクリプションを変更",
      "subscribe": "サブスクライブ",
      "free": "無料",
      "monthly": "月額",
      "yearly": "年額"
    },
    "error": {
      "getAppListFailed": "アプリリストの取得に失敗しました",
      "unauthorized": "認証されていません。設定を確認してください",
      "waitingUpload": "すべてのファイルのアップロードをお待ちください",
      "fieldsRequired": "は空にできません",
      "emptyMessage": "メッセージの内容が空です",
      "emptyMessageTip": "ユーザーがコンテンツ生成中に応答停止ボタンをクリックした可能性があります"
    }
  },
  'ko': {
    "app": {
      "noDifyConfig": "사용 가능한 Dify 애플리케이션 구성이 없습니다"
    },
    "chat": {
      "aiGenerated": "AI가 생성한 콘텐츠, 참고용입니다",
      "newConversation": "새 대화",
      "newSession": "새 세션",
      "conversationList": "대화 목록",
      "noConversations": "대화가 없습니다"
    },
    "common": {
      "logout": "로그아웃",
      "login": "로그인",
      "backToHome": "홈으로 돌아가기"
    },
    "subscription": {
      "upgrade": "구독 업그레이드",
      "modify": "구독 수정",
      "subscribe": "구독",
      "free": "무료",
      "monthly": "월간",
      "yearly": "연간"
    },
    "error": {
      "getAppListFailed": "앱 목록을 가져오는데 실패했습니다",
      "unauthorized": "인증되지 않음, 구성을 확인하세요",
      "waitingUpload": "모든 파일 업로드를 기다려주세요",
      "fieldsRequired": "는 비워둘 수 없습니다",
      "emptyMessage": "메시지 내용이 비어있습니다",
      "emptyMessageTip": "사용자가 콘텐츠 생성 중에 응답 중지 버튼을 클릭했을 수 있습니다"
    }
  },
  'ms': {
    "app": {
      "noDifyConfig": "Tiada konfigurasi aplikasi Dify tersedia"
    },
    "chat": {
      "aiGenerated": "Kandungan dijana oleh AI, hanya untuk rujukan",
      "newConversation": "Perbualan Baru",
      "newSession": "Sesi Baru",
      "conversationList": "Senarai Perbualan",
      "noConversations": "Tiada perbualan"
    },
    "common": {
      "logout": "Log Keluar",
      "login": "Log Masuk",
      "backToHome": "Kembali ke Laman Utama"
    },
    "subscription": {
      "upgrade": "Naik Taraf Langganan",
      "modify": "Ubah Langganan",
      "subscribe": "Langgan",
      "free": "Percuma",
      "monthly": "Bulanan",
      "yearly": "Tahunan"
    },
    "error": {
      "getAppListFailed": "Gagal mendapatkan senarai aplikasi",
      "unauthorized": "Tidak dibenarkan, sila semak konfigurasi anda",
      "waitingUpload": "Sila tunggu semua fail dimuat naik",
      "fieldsRequired": "tidak boleh kosong",
      "emptyMessage": "Kandungan mesej kosong",
      "emptyMessageTip": "Pengguna mungkin telah mengklik butang henti respons semasa penjanaan kandungan"
    }
  }
};

// 合并所有语言配置
Object.assign(languageTranslations, moreLanguages);

// 更新语言文件的函数
function updateLanguageFile(langCode, translations) {
  const filePath = path.join(__dirname, '..', 'src', 'i18n', 'locales', `${langCode}.json`);
  
  try {
    // 写入文件
    fs.writeFileSync(filePath, JSON.stringify(translations, null, 2) + '\n', 'utf8');
    console.log(`✅ Updated ${langCode}.json`);
  } catch (error) {
    console.error(`❌ Failed to update ${langCode}.json:`, error.message);
  }
}

// 主函数
function syncI18nStructure() {
  console.log('🚀 开始同步国际化文件结构...\n');
  
  // 更新所有语言文件
  Object.entries(languageTranslations).forEach(([langCode, translations]) => {
    updateLanguageFile(langCode, translations);
  });
  
  console.log('\n✨ 国际化文件结构同步完成！');
}

// 如果直接运行此脚本
if (require.main === module) {
  syncI18nStructure();
}

module.exports = { syncI18nStructure, languageTranslations };
