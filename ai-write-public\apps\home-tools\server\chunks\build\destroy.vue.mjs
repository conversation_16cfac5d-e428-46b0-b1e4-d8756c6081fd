import { mergeProps, useSSRContext } from 'vue';
import { ssrRenderAttrs } from 'vue/server-renderer';
import { _ as _export_sfc } from './_plugin-vue_export-helper.mjs';

const _sfc_main = {
  __name: "destroy",
  __ssrInlineRender: true,
  setup(__props) {
    return (_ctx, _push, _parent, _attrs) => {
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "clear-data-warning" }, _attrs))} data-v-f65dcef3><h1 data-v-f65dcef3>Warning: Clear Site Data</h1><p data-v-f65dcef3>This action will delete all site data displayed on your device.</p><button data-v-f65dcef3>Are you sure you want to continue?</button></div>`);
    };
  }
};
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/tool/destroy.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const destroy = /* @__PURE__ */ _export_sfc(_sfc_main, [["__scopeId", "data-v-f65dcef3"]]);

export { destroy as default };
//# sourceMappingURL=destroy.vue.mjs.map
