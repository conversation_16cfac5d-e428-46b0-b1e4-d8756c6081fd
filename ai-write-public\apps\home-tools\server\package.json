{"name": "nuxt-app-prod", "version": "0.0.0", "type": "module", "private": true, "dependencies": {"@babel/parser": "7.27.0", "@ctrl/tinycolor": "3.6.1", "@element-plus/icons-vue": "2.3.1", "@floating-ui/core": "1.6.9", "@floating-ui/dom": "1.6.13", "@floating-ui/utils": "0.2.9", "@microsoft/fetch-event-source": "2.0.1", "@popperjs/core": "2.11.7", "@vant/popperjs": "1.3.0", "@vant/use": "1.6.0", "@vue/compiler-core": "3.5.13", "@vue/compiler-dom": "3.5.13", "@vue/compiler-ssr": "3.5.13", "@vue/devtools-api": "6.6.4", "@vue/reactivity": "3.5.13", "@vue/runtime-core": "3.5.13", "@vue/runtime-dom": "3.5.13", "@vue/server-renderer": "3.5.13", "@vue/shared": "3.5.13", "@vueuse/core": "9.13.0", "@vueuse/shared": "9.13.0", "async-validator": "4.2.5", "asynckit": "0.4.0", "axios": "1.8.4", "call-bind-apply-helpers": "1.0.2", "combined-stream": "1.0.8", "consola": "3.4.2", "dayjs": "1.11.13", "debug": "4.4.0", "delayed-stream": "1.0.0", "devalue": "5.1.1", "dunder-proto": "1.0.1", "element-plus": "2.9.7", "entities": "4.5.0", "es-define-property": "1.0.1", "es-errors": "1.3.0", "es-object-atoms": "1.1.1", "es-set-tostringtag": "2.1.0", "estree-walker": "2.0.2", "follow-redirects": "1.15.9", "form-data": "4.0.2", "function-bind": "1.1.2", "get-intrinsic": "1.3.0", "get-proto": "1.0.1", "gopd": "1.2.0", "has-symbols": "1.1.0", "has-tostringtag": "1.0.2", "hasown": "2.0.2", "hookable": "5.5.3", "js-cookie": "3.0.5", "lodash-es": "4.17.21", "lodash-unified": "1.0.3", "math-intrinsics": "1.1.0", "memoize-one": "6.0.0", "mime-db": "1.52.0", "mime-types": "2.1.35", "ms": "2.1.3", "normalize-wheel-es": "1.2.0", "proxy-from-env": "1.1.0", "source-map-js": "1.2.1", "supports-color": "10.0.0", "ufo": "1.5.4", "unhead": "2.0.4", "vant": "4.9.18", "vue": "3.5.13", "vue-bundle-renderer": "2.1.1", "vue-demi": "0.14.10", "vue-router": "4.5.0"}}