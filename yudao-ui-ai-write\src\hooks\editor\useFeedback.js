import { feedback } from '@/api/dify'
import ajax from "@/utils/request"

export function useFeedback() {
    const fetchFeedback = async (params) => {
        try {
            const response = await ajax.post(feedback,{
                message_id: params.message_id,
                rating: params.rating,
                appId: params.app_id,
            })
            if (response?.data?.result === 'success') {
                return response.data;
            }
        
        } catch (error) {
            console.log(error)
        }
    }
    return {
        fetchFeedback
    }
}