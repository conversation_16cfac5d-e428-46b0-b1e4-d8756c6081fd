import { useDifyChat } from '@dify-chat/core'
import { Link, useHistory } from 'pure-react-router'
import { UserOutlined,RobotFilled,DownCircleTwoTone } from '@ant-design/icons'
import {Cookies} from '../../utils/cookieHandler'
import { LogoIcon } from '@/components/logo'
import {  Popover,Dropdown } from 'antd'
import { useState } from 'react'
import { useTranslation } from 'react-i18next'
import CenterTitleWrapper from '../../layout/components/center-title-wrapper'
interface IMobileHeaderProps {
	/**
	 * 自定义中间部分内容
	 */
	centerChildren: JSX.Element,
	// 打开订阅
	showSubscribe: () => void,
	subStatusDetail: any,
	selectedAppId:any,
	appList:any,
	appConfig:any
}
/**
 * 移动端共用头部
 */
export const MobileHeader = (props: IMobileHeaderProps) => {
	const { centerChildren,showSubscribe,subStatusDetail,selectedAppId,appList,appConfig } = props
	const { t } = useTranslation()
	const { mode } = useDifyChat()
	const userInfoString = Cookies.get('userInfo')
	const avatar = userInfoString&&JSON.parse(userInfoString)?.avatar?JSON.parse(userInfoString).avatar:'https://img.medsci.cn/web/img/user_icon.png'
	const history = useHistory()
	const match = history.location.pathname.match(/^\/ai-chat\/([^/]+)$/)
	const appNameEn = match ? match[1] : ''
	const [arrow,setArrow] = useState(false)
	const url = location.origin.includes('medsci.cn')?'https://ai.medsci.cn':location.origin.includes('medon.com.cn')?'https://ai.medsci.cn':"http://localhost:3000"
	const errorImg = (e:any) => {
		e.target.src = 'https://img.medsci.cn/web/img/user_icon.png';
	}
	const login = () => {
		const languages =  language();
		if (!languages ||  languages === 'zh') {
		  // 为了解决 TypeScript 类型错误，需要先声明 window.addLoginDom 方法
		  (window as any).addLoginDom?.()
		} else {
		(window as any).top.location.href =  location.origin + '/' + languages + "/login"
		}
		// Replace with actual login logic
	  };
	const language = () => {
		return Cookies.get('ai_apps_lang')
			? Cookies.get('ai_apps_lang')
			: navigator.browserLanguage || navigator.language
	}
	const logout =async () => {
		setArrow(false)
		Cookies.remove("userInfo", { domain: ".medon.com.cn" });
		Cookies.remove("userInfo", { domain: ".medsci.cn" });
		Cookies.remove("userInfo", { domain: "localhost" });
		Cookies.remove("yudaoToken", { domain: "ai.medon.com.cn" });
		Cookies.remove("yudaoToken", { domain: "ai.medsci.cn" });
		Cookies.remove("yudaoToken", { domain: ".medon.com.cn" });
		Cookies.remove("yudaoToken", { domain: ".medsci.cn" });
		Cookies.remove("yudaoToken", { domain: "localhost" });
		localStorage.removeItem("conversation")
		if (window.location.origin.includes("medsci.cn")) {
		  window.top.location.href =
			"https://www.medsci.cn/sso_logout?redirectUrl=" + window.top.location.href;
		} else {
		  window.top.location.href =
			`https://portal-test.medon.com.cn/sso_logout?redirectUrl=` +
			window.top.location.href;
		}
	  };
	return (
		<div className="h-12 !leading-[3rem] px-4 text-base top-0 z-20 bg-white w-full shadow-sm font-semibold justify-between flex items-center box-border">
			{/* {mode === 'multiApp' ? (
				<Link
					to={url}
					className="flex items-center"
				>
					<LogoIcon />
				</Link>
			) : (
				<div className="flex items-center">
					<LogoIcon />
				</div>
			)} */}

			<div className="flex-1 overflow-hidden flex items-center justify-center">
				{centerChildren}
			</div>
			{ language() == 'zh'&&(!appNameEn.includes("novax")&&!appNameEn.includes("elavax"))&&<div onClick={()=>showSubscribe()}  className='px-[15px] py-[4px] flex items-center h-[28px] rounded border-none mr-[8px] text-xs text-[#614018]' style={{  backgroundImage: "linear-gradient(270deg, #FDE39B 0%, #F9D07A 88%)"}}>{  subStatusDetail.packageType == "免费"?"升级订阅":subStatusDetail.packageType == "连续包月"||subStatusDetail.packageType == "连续包年"?"修改订阅":"订阅" }</div> }
			{(appNameEn.includes("novax")||appNameEn.includes("elavax"))?<CenterTitleWrapper>
				{selectedAppId ? (
								<>
									{/* <div className="mx-2 font-normal text-desc">/</div> */}
									<Dropdown
										className="mr-2 ml-2" 
										arrow
										placement="bottom"
										trigger={['click']}
										menu={{
											style: {
												// boxShadow: 'none',
											},
											selectedKeys: [selectedAppId],
											items: [
												...(appList?.map(item => {
													const isSelected = selectedAppId === item.id
													return {
														key: item.id,
														label: (
															<div className={isSelected ? 'text-primary' : 'text-default'}>
																{item?.info.name}
															</div>
														),
														onClick: () => {
															if(appNameEn.includes("novax")||appNameEn.includes("elavax")){
																(window as any).top.location.href = location.origin + ((window as any).top.location.href.includes('ai-chat')?'/ai-chat/':'/chat/') + `${item?.info.appNameEn}`
																return
															}
														},
														icon: <RobotFilled />,
													}
												}) || []),
											],
										}}
									>
										<div className="cursor-pointer flex items-center">
											<span className="cursor-pointer w-[75px] inline-block whitespace-nowrap overflow-hidden text-ellipsis">{appConfig?.info?.name}</span>
											<DownCircleTwoTone className="ml-1" />
										</div>
									</Dropdown>
								</>
							) : null}
			</CenterTitleWrapper>:null}
			<div className='px-[15px] py-[4px] mr-[8px] h-[28px] flex items-center' style={{background:'#f1f5f9'}}>
			
			<a  style={{borderRadius:'4px',fontSize:'12px',color:"#666",lineHeight:'1'}} className='backImg '   href={location.origin.includes(".medon.com.cn")?('https://ai.medon.com.cn'+'/'+language()):location.origin.includes(".medsci.cn")?('https://ai.medsci.cn'+'/'+language()):'/'+language()} target='_top'>{t('common.backToHome')}</a>
			</div>
			{!userInfoString&&<div className="hover:cursor-pointer" onClick={login}>{t('common.login')}</div>}
			<Popover
								placement="bottomLeft"
								trigger="hover"
								arrow={arrow}
								overlayStyle={{
									width: 300,
									height:163,
								}}
								content={
									<div style={{
									display: 'flex',
									flexDirection: 'column',
									alignItems: 'center',
									position: 'relative',
									paddingBottom:'40px'
									}}>
											<a className="exit text-right w-full text-[#333333]" onClick={logout}>
										{t('common.logout')}
										</a>
									<div style={{
									display: 'flex',
									flexDirection: 'column',
									alignItems: 'center'
									}} className="iconHeader bg-write">
									
									{userInfoString&&JSON.parse(userInfoString||'').userId&&<img src={avatar} onMouseEnter={()=>setArrow(true)} onMouseLeave={()=>setArrow(false)} style={{ width: '60px',height: "60px" }} onError={errorImg} alt="avatar" />}
										<span className="account">{userInfoString&&JSON.parse(userInfoString||'').userName}</span>
									</div>
									</div>
								}
								>
								<a href="#">
									<div className="img-area  leading-none">
									{userInfoString&&JSON.parse(userInfoString||'').userId&&(avatar?(<img src={avatar} onError={errorImg} onMouseEnter={()=>setArrow(true)} onMouseLeave={()=>setArrow(false)}  style={{ width: '32px',height: "32px" }} alt="avatar" />):(<UserOutlined />))}
									</div>
								</a>
								</Popover>
		</div>
	)
}
