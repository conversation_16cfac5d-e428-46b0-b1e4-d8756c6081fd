<template>
    <umo-editor v-bind="options" ref="editorRef" @changed="change" @changed:activeCategory="onChangedActiveCategory"
        @changed:selection="onChangedSelection" @changed:getMessage="onChangedAi" @changed:retry="onChangedRetry"
        @changed:stopMessage="onChangedStopMessage" @changed:FeedbackDislike="onChangedFeedbackDislike"
        @changed:FeedbackLike="onChangedFeedbackLike" @changed:newConversation="onChangedNewConversation"
        @changed:showLog="onChangedShowLog" @changed:collect="onChangedCollect"
        @changed:leadQuestionOrRecord="onChangedLeadQuestionOrRecord"
        @changed:dislikeReason="onChangedDislikeReason" @save="onSave" @areaFocus="focusSubscribe"   ></umo-editor>
    <MsMessages v-model="dialogVisible" :classInfo="classInfo" />
    <el-dialog
      v-model="payShow"
      v-if="payShow"
      class="payPC"
      :show-close="false"
    >
      <pay
        :userInfo="userInfo"
        :appTypes="appTypes"
        :currentItem="currentItem"
        :subStatusDetail="subStatusDetail"
        @toAgreement="toAgreement"
        @close="close"
        @subscribe="subscribe"
      />
    </el-dialog>
</template>
<script setup>
import pay from "../pay/index.vue";
// import payMobile from "/components/payMobile/index.vue";
import { useRoute, useRouter } from 'vue-router';
import { useQuery } from "@vue/apollo-composable"
import { UmoEditor } from '@umoteam/editor';
import cookie from "js-cookie";
import { ref, watch, reactive, onMounted, nextTick, onBeforeMount, onBeforeUnmount } from 'vue'
import { useCategory } from '@/hooks/editor/useCategory' //获取目录
import { useType } from '@/hooks/editor/useType' // 获取简易操作目录
import { useLeadingQuestion } from '@/hooks/editor/useLeadingQuestion'  //引导提问
import { useFeedback } from '@/hooks/editor/useFeedback'  //踩/点赞
import { useConversations } from '@/hooks/editor/useConversations' //历史会话
import { useAnswer } from '@/hooks/editor/useAnswer' //获取答案
import { useStopMessage } from '@/hooks/editor/useStopMessage' //停止会话
import { useCollectionList } from '@/hooks/editor/useCollectionList' //获取会话收藏记录
import { useCollection } from '@/hooks/editor/useCollection' //收藏/取消收藏
import { useDialikeReason } from '@/hooks/editor/useDialikeReason' //提交吐槽原因
import { getAppByUuid, createSubscription,getPackageByKey} from "@/api/dify";
import ajax from "@/utils/request";
import { usePrompt } from '@/hooks/editor/usePrompt' //获取当前模板实例的语料
import { useTemplatePrompt } from '@/hooks/editor/useTemplatePrompt' //获取当前模板实例的语料
import { removeSession, getSession, setSession } from "@/utils/auth"
import { useEditorStore } from '@/stores/editor'  
import { useLoginStore } from '@/stores/login'
import { getAppDetail  } from '@/api/graphql'
import {
  getDefaultLanguageCode
} from "@/common/commonJs";
import langs from "../../langs/language"
import {Cookies} from "@/utils/cookieHandler"
const lang = cookie.get('ai_apps_lang') || (navigator.browserLanguage || navigator.language) || 'en'
const loginStore = useLoginStore()
const route = useRoute();
const router = useRouter()
const editorStore = useEditorStore()
const editorRef = ref(null);
let dialogVisible = ref(false)
let spsPrompt = ref('')
const userInfo = ref()
const currentItem = ref()
const props = defineProps({
    appUuid: {
        type: String,
        default: ''
    },
    dAppUuid: {
        type: String,
        default: ''
    },
    subStatusDetail: {
        type: Object,
        default: () => {
            return {}
        },
    },
})
const subStatusDetail = ref(
   props.subStatusDetail
)
const subScript = async ()=>{ 
    if(lang == "zh"){
    const res = await ajax.get(getPackageByKey) 
    subStatusDetail.value = res.data
    payShow.value = true;
  }else{
    payShow.value = true;
  }
}
defineExpose({
    subScript
})
const classInfo = reactive({
    conversation_id: '',
    message_id: '',
    app_id: '',
    dAppUuid: '',
    underlineText: '',
    task_id: ''
})
// 订阅弹窗
const payShow = ref(false)
const options = reactive({
    theme: 'light',
    height: 'calc(100vh - 60px)',
    page: {
        ai: {
            list: [],
            operateTag: [
                { label: langs[lang]['替换'], value: 'replace', theme: '' },
                { label: langs[lang]['追加'], value: 'add', theme: '' },
                { label: langs[lang]['放弃'], value: 'cancel', theme: 'danger' },
                { label: langs[lang]['重试'], value: 'again', theme: '' },
            ],
            operateSvg: [
                { label: langs[lang]['全部对话'], value: 'log', svg: 'log', svgActive: 'log', isActive: false },
                { label: langs[lang]['复制'], value: 'copy', svg: 'double', svgActive: 'double', isActive: false },
                { label: langs[lang]['收藏'], value: 'collect', svg: 'star', svgActive: 'starActive', isActive: false },
                { label: langs[lang]['有用'], value: 'like', svg: 'thump-up', svgActive: 'up', isActive: false },
                { label: langs[lang]['吐槽'], value: 'dislike', svg: 'thump-down', svgActive: 'down', isActive: false },
            ],
            fixedOperate: [
                // { tip: '提问引导', svg: 'compass', label: '提问引导', value: 'guide', data: [], title: '可以这样提问：' },
                // { tip: '历史提问', svg: 'log', label: '历史提问', value: 'history', data: [] , title: '历史提问'},
                { tip: langs[lang]['执行提问'], svg: 'start', label: '执行提问', value: 'query' },
                { tip: langs[lang]['隐藏AI'], svg: 'close1', label: '隐藏AI', value: 'close' },
            ],
            leadingQuestionList: [], //引导问题
            recordList: [] //历史记录
        },
        aiAnswer: '',
        isAnswerFinished: false
    }
});
// 缓存内容
const change = (e)=>{
    localStorage.setItem("writeContent"+'-'+props.appUuid,JSON.stringify(editorRef.value?.getContent()))
}
// 获取目录
const { defaultCategotry, refetcCategory } = useCategory()

// 获取历史会话
const { conversationList, fetchConversations } = useConversations()

// 获取引导问题
const { leadingQuestions, fetchLeadingQuestions } = useLeadingQuestion()
// 点赞/踩
const { fetchFeedback } = useFeedback()
// ai会话
const { getAnswer } = useAnswer(props.appUuid)
// 停止会话
const { stopMessage } = useStopMessage()
//获取会话收藏记录
const { refetchCollection, collectionList } = useCollectionList()
//收藏/取消收藏
const { insertCollection, updateCollection } = useCollection()
// 提交吐槽原因
const { dislikeReasonRecord, insertDislikeReason, updateDislikeReason, getDislikeReasonRecord } = useDialikeReason()

// const { fetchPrompt, prompt } = usePrompt()
// const { fetchTemplatePrompt, prompts } = useTemplatePrompt()
const close = () => {
  payShow.value = false;
};
// 支付
const subscribe = async (item, appUuid) => {
  let language = getDefaultLanguageCode();
  if (!userInfo.value?.userId) {
    // country='xxxx' // 模拟国外
    // 判断是否是国内用户访问
    if (!language || language == "zh") {
      window.addLoginDom();
    } else {
      // 跳转到获取授权页
      window.top.location.href = location.origin + '/' + language + "/login";
    }
  } else {
    const subscriptionParams = {
      appUuid: appUuid,
      priceId: item.priceId,
      monthNum: item.monthNum,
    };
    let res = await ajax.post(createSubscription,subscriptionParams);
    if (res.data) {
      ElMessage({
        type: "success",
        message: t("tool.sS"),
      });
      setTimeout(() => {
        top.location.href = res.data;
      }, 1000);
    }
  }
};
// 获取应用list
const  getAppListData =async ()=> {
    const res = await ajax.get(getAppByUuid+"?appUuid="+ route.params.app_uuid)
    currentItem.value = res.data
    // classInfo.app_id = res.data.dAppUuid
    classInfo.dAppUuid = res.data.dAppUuid
    props.appUuid = res.data.appUuid
}

const checkCookie = () => {
  const newVal = cookie.get('userInfo');
  if (newVal !== userInfo.value) {
    userInfo.value = newVal? JSON.parse(newVal):''
    if(Cookies.get('yudaoToken')){
        getAppListData()
        clearInterval(intervalId);
    }
  }
};


onBeforeUnmount(() => clearInterval(intervalId));
const intervalId = setInterval(checkCookie, 1000);

onMounted(async() => {
     const useinfo= cookie.get("userInfo")
     userInfo.value = useinfo? JSON.parse(useinfo):''
    // console.log(sessionStorage.getItem('md'))
//   nextTick(() =>{
//   editorRef?.value?.setContent(convertMarkdownToHtml(sessionStorage.getItem('md') || ''))
//   })
    // const arr = [
    //     {
    //         prompt_name: "29",
    //         uuid: '4a46fe9c-369a-420e-b8b7-e6a8e88f740b',
    //         directory_md: "# 医学论文题目\n  \n# 摘要  \n\n# 关键词  \n  \n# 引言  \n## 背景  \n## 研究问题  \n## 重要性  \n  \n# 方法  \n## 研究设计  \n## 数据收集与分析  \n  \n# 结果  \n## 主要发现  \n  \n# 讨论  \n## 解释与比较  \n## 局限性  \n## 未来方向  \n  \n# 结论  \n## 研究总结  \n## 临床/科研意义  \n  \n# 参考文献  \n"
    //     }
    // ]
    // console.log(route.params)
    // if (route.params.uuid) {
    //     const obj = arr.find(item => item.uuid === route.params.uuid)
    //     if (obj.directory_md) {
    //         editorRef?.value?.setContent(convertMarkdownToHtml(obj.directory_md))
    //     } else {
    //         editorRef?.value?.setContent('')
    //     }
    // }
    // refetcCategory({app_uuid: route.params.app_uuid})
    // // fetchPrompt()
    const appData = JSON.parse(localStorage.getItem('appWrite'+'-' + props.appUuid) || '{}');
    if (props.appUuid === appData.appUuid) {
        if(localStorage.getItem('writeContent'+'-'+props.appUuid)){
            const content = convertMarkdownToHtml(JSON.parse(localStorage.getItem('writeContent'+'-'+props.appUuid)));
            editorRef.value?.setContent(content);
        }else{
            const content = convertMarkdownToHtml(appData.directoryMd);
            editorRef.value?.setContent(content);
        }
        
    }
    await getAppListData()

})
const convertMarkdownToHtml = (markdown) => {
    // 使用正则表达式匹配对应的标题级别并替换为相应的HTML标签
    return markdown
        .replace(/^###### (.*$)/gim, '<h6>\$1</h6>')
        .replace(/^##### (.*$)/gim, '<h5>\$1</h5>')
        .replace(/^#### (.*$)/gim, '<h4>\$1</h4>')
        .replace(/^### (.*$)/gim, '<h3>\$1</h3>')
        .replace(/^## (.*$)/gim, '<h2>\$1</h2>')
        .replace(/^# (.*$)/gim, '<h1>\$1</h1>')
        .replace(/\n{2,}/g, ''); // 替换连续的换行符为 <br> 标签
}
// watch(
//     () => loginStore.isLoggedIn,
//     newVal => {
//         if (newVal) {
//             fetchLeadingQuestions()
//             fetchConversations('正常')
//             fetchPrompt()
//         }
//     },
//     { immediate: true }
// )
// watch(
//     () => prompt.value,
//     newVal => {
//         spsPrompt.value = newVal
//     }
// )
// const num = ref(0)
// // 回填默认的大纲
// watch(
//     () => defaultCategotry.value,
//     (newVal) => {
//         editorRef?.value?.setContent(newVal || '')
//     }
// )

//引导提问
watch(
    () => leadingQuestions.value,
    (newVal) => {
        const parent = options.page.ai.fixedOperate.find(item => item.value === 'guide')
        if (newVal) {
            parent.data = newVal.slice(0, 20).map(item => {
                return {
                    query: item,
                    label: item,
                }
            })
        }
    }
)
// 历史会话
watch(
    () => conversationList.value,
    (newVal) => {
        const parent = options.page.ai.fixedOperate.find(item => item.value === 'history')
        if (parent) {
            parent.data = newVal.slice(0, 20).map(item => {
                return {
                    query: item.name
                }
            })
        }
    }
)


// 获取简易操作分类
const { typeList, refetchType } = useType(props.dAppUuid)
watch(
    () => typeList.value,
    (newVal) => {
        options.page.ai.list = newVal
    }
)
const focusSubscribe   = async () => {
    if(cookie.get("userInfo")){
        userInfo.value = cookie.get("userInfo")
        ? JSON.parse(cookie.get("userInfo"))
        : null;
    }
    if (!cookie.get("userInfo")) {
    cookie.remove("yudaoToken", { domain: ".medsci.cn" });
    cookie.remove("yudaoToken", { domain: ".medon.com.cn" });
    cookie.remove("yudaoToken", { domain: "localhost" });
    localStorage.removeItem("hasuraToken");
    const language = getDefaultLanguageCode();
    if (!language || language == "zh") {
      window.addLoginDom();
    } else {
    window.top.location.href = location.origin + '/' + language + "/login";
    }
    document.getElementsByTagName("textarea")[0].blur()

    return false;
  }
  if(!currentItem.value.appUser?.status || currentItem.value.appUser?.status == 2){
    payShow.value = true;
    document.getElementsByTagName("textarea")[0].blur()
    return false;
  }  
    return true
}
// 获取点击的大纲信息
const onChangedActiveCategory = ({ activeCategory }) => {
    console.log(activeCategory)
}

// 划词内容
const onChangedSelection = () => {
    // classInfo.underlineText = editor.commands.getSelectionText()
    // classInfo.underlineText =  getSelectionText(editor)
}

let queryLast = reactive({})

// 重试
const onChangedRetry = async () => {
    classInfo.underlineText = ''
    if(options.page.isAnswerFinished){
            return
        }
    await getAnswer(options, queryLast, classInfo, spsPrompt)
};

// 获取答案
const onChangedAi = async (aiDemand) => {
  let language = getDefaultLanguageCode()
    if (!cookie.get("userInfo")) {
    loginStore.resetToken();
    if (!language || language == "zh") {
      return window.addLoginDom();
    } else {
      // 跳转到获取授权页
      window.top.location.href = location.origin + '/' + language + "/login";
    }
  } else {
    userInfo.value = cookie.get("userInfo")
      ? JSON.parse(cookie.get("userInfo"))
      : null;
  }
   if(!currentItem.value.appUser?.status || currentItem.value.appUser?.status == 2){
        payShow.value = true
        return false
   }
    if (Object.keys(aiDemand).length) {
        queryLast = { ...aiDemand }
        if(options.page.isAnswerFinished){
            return
        }
        await getAnswer(options, aiDemand, classInfo, spsPrompt)
    }
};

// 停止会话
const onChangedStopMessage = async () => {
    stopMessage(options, classInfo)
}

// 新对话
const onChangedNewConversation = async (aiDemand) => {
    let language = getDefaultLanguageCode()
    if (!cookie.get("userInfo")) {
    loginStore.resetToken();
    if (!language || language == "zh") {
      return window.addLoginDom();
    } else {
      // 跳转到获取授权页
      window.top.location.href = location.origin + '/' + language + "/login";
    }
  } else {
    userInfo.value = cookie.get("userInfo")
      ? JSON.parse(cookie.get("userInfo"))
      : null;
  }
  if(!currentItem.value.appUser?.status || currentItem.value.appUser?.status == 2){
        payShow.value = true
        return false
   }
    handlePrompt()
    options.page.ai.operateSvg = options.page.ai.operateSvg.map(item => ({
        ...item,
        isActive: false
    }));
    // classInfo.conversation_id = ''
    if (Object.keys(aiDemand).length) {
        queryLast = { ...aiDemand }
        classInfo.app_id = queryLast.dify_app_uuid || currentItem.value.dAppUuid
        if(options.page.isAnswerFinished){
            return
        }
        await getAnswer(options, aiDemand, classInfo, spsPrompt)
    }
}

// 获取预料
const handlePrompt = async () => {
    classInfo.underlineText = editorRef.value.getSelectionText()
    // classInfo.selectionNode = editorRef.value.getSelectionNode() 划词所在章节标题
    // const arr = editorRef.value.getTableOfContents()  获取目录
    // let basePath = `写作模板/${route.params.name}`
    // let str = basePath;
    // arr.forEach(item => {
    //     if (item.id === classInfo.selectionNode.attrs.id) {
    //         str += `/${item.textContent}`
    //     }
    // })
    // spsPrompt.value = findPrompt(str);
    // if (!spsPrompt.value) {
        // spsPrompt.value = findPrompt(basePath) || '';
    // }
}

// 查找匹配的提示
const findPrompt = (path) => {
    // if (!prompts.value) return;
    // return prompts.value.find(item => item.name === path)?.prePrompt;
};

// 吐槽 或 点赞 接口
const onChangedFeedback = async (type) => {
    const response = await fetchFeedback({
        ...classInfo,
        rating:type
    });
    if (response.result === 'success') {
        setOperateSvg(type);
    }
}

// 吐槽
const onChangedFeedbackDislike = async () => {
    const obj = options.page.ai.operateSvg.find(item => item.value === 'dislike')
    const type = obj.isActive ? null : 'dislike'
    onChangedFeedback(type);
}
// 点赞
const onChangedFeedbackLike = async () => {
    const obj = options.page.ai.operateSvg.find(item => item.value === 'like')
    const type = obj.isActive ? null : 'like'
    onChangedFeedback(type);
}

// 修改operateSvg数据中“吐槽”或“喜欢”的active状态
const setOperateSvg = (rating) => {
    options.page.ai.operateSvg.forEach((item) => {
        if (['like', 'dislike' ].includes(rating)) {
            if (item.value === 'like' || item.value === 'dislike') {
                item.isActive = (item.value === rating);
            }
        } else if (['正常', '删除'].includes(rating)) {
            if (item.value === 'collect') {
                item.isActive = rating === '正常' ? true : false
            }
        }
    });
}

// 从答案面板打开历史弹窗
const onChangedShowLog = () => {
    dialogVisible.value = true
}

// 收藏
const onChangedCollect = async () => {
    await refetchCollection({ conversation_id: classInfo.conversation_id })
    if (Array.isArray(collectionList.value) && collectionList.value.length) {
        const obj = collectionList.value[0]
        await updateCollection({
            conversation_id: obj.conversation_id,
            from_end_user: obj.from_end_user,
            status: obj.status === '正常' ? '删除' : '正常'
        })
        setOperateSvg(obj.status === '正常' ? '删除' : '正常')
    } else {
        await insertCollection({
            conversation_id: classInfo.conversation_id,
            from_end_user: loginStore.userInfo.userName
        })
        setOperateSvg('正常')
    }

}

// 获取引导问题 或 历史记录
const onChangedLeadQuestionOrRecord = (params) => {
    if (params === 'guide') {
        fetchLeadingQuestions()
    } else {
        fetchConversations('正常')
    }
}
// 提交不喜欢原因
const onChangedDislikeReason = async (params) => {
    await getDislikeReasonRecord({
        message_id: classInfo.message_id,
    })
    const { ext, reason } = params
    const obj = {
        message_id: classInfo.message_id,
        ext: ext.join('&'),
        reason: reason
    }
    if (dislikeReasonRecord.value.length) {
        updateDislikeReason(obj)
    } else {
        insertDislikeReason(obj)
    }
}

// 保存
const onSave =  async (content, page, document) => {
  
}

</script>
<style  >
.payPC {
  padding: 0px !important;
  width: 679px;
  border-radius: 20px;

  
}
.el-dialog__header {
    padding: 0px !important;
  }
</style>