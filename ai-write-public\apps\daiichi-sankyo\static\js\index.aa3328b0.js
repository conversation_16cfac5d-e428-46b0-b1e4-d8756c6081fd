/*! For license information please see index.aa3328b0.js.LICENSE.txt */
(()=>{"use strict";var e={65681:function(e,t,n){let r,o,a,i,s;var l,c,u,d,f,p,h,m,v,g,y,b=n(52676),x=n(38751),j=n(75271),w=n.t(j,2);let S=j.createContext({conversations:[],setConversations:()=>{},currentConversationId:"",setCurrentConversationId:()=>{},currentConversationInfo:void 0}),C=S.Provider;function k(){return(0,j.useContext)(S)}let E={mode:"multiApp",user:"",appService:{},enableSetting:!0},O={mode:"singleApp",user:"",appConfig:{requestConfig:{apiBase:"",apiKey:""},answerForm:{enabled:!1,feedbackText:""}}},_=j.createContext(E),N=_.Provider,A=()=>{let[e,t]=(0,j.useState)({}),n=(0,j.useContext)(_),{mode:r}=n;return{currentAppConfig:e,setCurrentAppConfig:t,..."multiApp"===r?E:O,...n}};var M=n(77345),P=n(20274);let T=e=>!!e&&e.startsWith("temp"),R=e=>{try{let t=atob(e),n=new Uint8Array(t.length);for(let e=0;e<t.length;e++)n[e]=t.charCodeAt(e);let r=M.ZP.inflate(n,{to:"string"});return console.log(r),r}catch(e){return console.error("解压缩过程中出现错误:",e),""}},I={sm:0,md:768,lg:1024,xl:1280,"2xl":1536},L=()=>{let{sm:e,md:t}=(0,P.Z)();return!!e&&!t},F="DIFY_CHAT__DIFY_VERSION",$={get version(){return localStorage.getItem(F)||""},set version(version){localStorage.setItem(F,version)}};var Z=n(15623),H=n(72422),D=n(9210),B=n(27793),z=n(92782),W=n(21778);let U=n.p+"static/image/logo.48a40366.png",G=e=>{let{hideGithubIcon:t,hideText:n}=e;return(0,b.jsxs)("div",{className:"flex h-16 items-center justify-start py-0 box-border",children:[(0,b.jsxs)("div",{className:"h-full flex items-center flex-1 overflow-hidden",children:[(0,b.jsx)("img",{className:"w-8 h-8 inline-block",src:U,draggable:!1,alt:"logo"}),n?null:(0,b.jsx)("span",{className:"inline-block my-0 ml-3 font-bold text-lg",children:"MedSci xAI"})]}),!t&&(0,b.jsx)(W.ZP,{type:"link",href:"https://github.com/lexmin0412/dify-chat",target:"_blank",className:"px-0",children:(0,b.jsx)(z.Z,{className:"text-lg cursor-pointer text-default"})})]})};var K=n(79587),q=n(54648),X=n(48415),V=n(9485),Y=n(16778),Q=n(52870),J=n(90779),ee=n(46527),et=n(71323),en=n(37915),er=n(15223),eo=n(93536),ea=n(77424),ei=n(30967),es=n.t(ei,2),el={"../../node_modules/.pnpm/@ant-design+cssinjs@1.23.0__926d2fbe617ecfde386169564e1f17aa/node_modules/@ant-design/cssinjs/es/hooks/useHMR.js":function(e,t,n){n.d(t,{Z:()=>r}),e=n.hmd(e);let r=function(){return!1}},"../../node_modules/.pnpm/react-is@18.3.1/node_modules/react-is/cjs/react-is.development.js":function(e,t){},"../../node_modules/.pnpm/react-is@18.3.1/node_modules/react-is/cjs/react-is.production.min.js":function(e,t){var n=Symbol.for("react.element"),r=Symbol.for("react.portal"),o=Symbol.for("react.fragment"),a=Symbol.for("react.strict_mode"),i=Symbol.for("react.profiler"),s=Symbol.for("react.provider"),l=Symbol.for("react.context"),c=Symbol.for("react.server_context"),u=Symbol.for("react.forward_ref"),d=Symbol.for("react.suspense"),f=Symbol.for("react.suspense_list"),p=Symbol.for("react.memo"),h=Symbol.for("react.lazy");Symbol.for("react.offscreen"),Symbol.for("react.module.reference"),t.ForwardRef=u,t.isMemo=function(e){return function(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case n:switch(e=e.type){case o:case i:case a:case d:case f:return e;default:switch(e=e&&e.$$typeof){case c:case l:case u:case h:case p:case s:return e;default:return t}}case r:return t}}}(e)===p}},"../../node_modules/.pnpm/react-is@18.3.1/node_modules/react-is/index.js":function(e,t,n){e.exports=n("../../node_modules/.pnpm/react-is@18.3.1/node_modules/react-is/cjs/react-is.production.min.js")},"../../node_modules/.pnpm/classnames@2.5.1/node_modules/classnames/index.js":function(e){var t={}.hasOwnProperty;function n(){for(var e="",o=0;o<arguments.length;o++){var a=arguments[o];a&&(e=r(e,function(e){if("string"==typeof e||"number"==typeof e)return e;if("object"!=typeof e)return"";if(Array.isArray(e))return n.apply(null,e);if(e.toString!==Object.prototype.toString&&!e.toString.toString().includes("[native code]"))return e.toString();var o="";for(var a in e)t.call(e,a)&&e[a]&&(o=r(o,a));return o}(a)))}return e}function r(e,t){return t?e?e+" "+t:e+t:e}e.exports?(n.default=n,e.exports=n):"function"==typeof define&&"object"==typeof define.amd&&define.amd?define("classnames",[],function(){return n}):window.classNames=n}},ec={};function eu(e){var t=ec[e];if(void 0!==t)return t.exports;var n=ec[e]={id:e,loaded:!1,exports:{}};return el[e](n,n.exports,eu),n.loaded=!0,n.exports}function ed(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function ef(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function ep(e,t){if(e){if("string"==typeof e)return ed(e,t);var n=({}).toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?ed(e,t):void 0}}function eh(e){return function(e){if(Array.isArray(e))return ed(e)}(e)||ef(e)||ep(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}eu.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return eu.d(t,{a:t}),t},eu.d=(e,t)=>{for(var n in t)eu.o(t,n)&&!eu.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},eu.hmd=e=>((e=Object.create(e)).children||(e.children=[]),Object.defineProperty(e,"exports",{enumerable:!0,set:()=>{throw Error("ES Modules may not assign module.exports or exports.*, Use ESM export syntax, instead: "+e.id)}}),e),eu.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t);let em=j.createContext({}),ev="anticon",eg=j.createContext({getPrefixCls:(e,t)=>t||(e?`ant-${e}`:"ant"),iconPrefixCls:ev}),{Consumer:ey}=eg;function eb(e){if(Array.isArray(e))return e}function ex(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function ej(e,t){return eb(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,a,i,s=[],l=!0,c=!1;try{if(a=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;l=!1}else for(;!(l=(r=a.call(n)).done)&&(s.push(r.value),s.length!==t);l=!0);}catch(e){c=!0,o=e}finally{try{if(!l&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(c)throw o}}return s}}(e,t)||ep(e,t)||ex()}function ew(e){return(ew="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function eS(e){var t=function(e,t){if("object"!=ew(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=ew(r))return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==ew(t)?t:t+""}function eC(e,t,n){return(t=eS(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function ek(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function eE(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ek(Object(n),!0).forEach(function(t){eC(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ek(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}let eO=function(e){for(var t,n=0,r=0,o=e.length;o>=4;++r,o-=4)t=(65535&(t=255&e.charCodeAt(r)|(255&e.charCodeAt(++r))<<8|(255&e.charCodeAt(++r))<<16|(255&e.charCodeAt(++r))<<24))*0x5bd1e995+((t>>>16)*59797<<16),t^=t>>>24,n=(65535&t)*0x5bd1e995+((t>>>16)*59797<<16)^(65535&n)*0x5bd1e995+((n>>>16)*59797<<16);switch(o){case 3:n^=(255&e.charCodeAt(r+2))<<16;case 2:n^=(255&e.charCodeAt(r+1))<<8;case 1:n^=255&e.charCodeAt(r),n=(65535&n)*0x5bd1e995+((n>>>16)*59797<<16)}return n^=n>>>13,(((n=(65535&n)*0x5bd1e995+((n>>>16)*59797<<16))^n>>>15)>>>0).toString(36)};function e_(){return!!("undefined"!=typeof window&&window.document&&window.document.createElement)}var eN="data-rc-order",eA="data-rc-priority",eM=new Map;function eP(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.mark;return t?t.startsWith("data-")?t:"data-".concat(t):"rc-util-key"}function eT(e){return e.attachTo?e.attachTo:document.querySelector("head")||document.body}function eR(e){return Array.from((eM.get(e)||e).children).filter(function(e){return"STYLE"===e.tagName})}function eI(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!e_())return null;var n=t.csp,r=t.prepend,o=t.priority,a=void 0===o?0:o,i="queue"===r?"prependQueue":r?"prepend":"append",s="prependQueue"===i,l=document.createElement("style");l.setAttribute(eN,i),s&&a&&l.setAttribute(eA,"".concat(a)),null!=n&&n.nonce&&(l.nonce=null==n?void 0:n.nonce),l.innerHTML=e;var c=eT(t),u=c.firstChild;if(r){if(s){var d=(t.styles||eR(c)).filter(function(e){return!!["prepend","prependQueue"].includes(e.getAttribute(eN))&&a>=Number(e.getAttribute(eA)||0)});if(d.length)return c.insertBefore(l,d[d.length-1].nextSibling),l}c.insertBefore(l,u)}else c.appendChild(l);return l}function eL(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=eT(t);return(t.styles||eR(n)).find(function(n){return n.getAttribute(eP(t))===e})}function eF(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=eL(e,t);n&&eT(t).removeChild(n)}function e$(e,t){var n,r,o,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},i=eT(a),s=eR(i),l=eE(eE({},a),{},{styles:s}),c=eM.get(i);if(!c||!function(e,t){if(!e)return!1;if(e.contains)return e.contains(t);for(var n=t;n;){if(n===e)return!0;n=n.parentNode}return!1}(document,c)){var u=eI("",l),d=u.parentNode;eM.set(i,d),i.removeChild(u)}var f=eL(t,l);if(f)return null!=(n=l.csp)&&n.nonce&&f.nonce!==(null==(r=l.csp)?void 0:r.nonce)&&(f.nonce=null==(o=l.csp)?void 0:o.nonce),f.innerHTML!==e&&(f.innerHTML=e),f;var p=eI(e,l);return p.setAttribute(eP(l),t),p}function eZ(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n={};for(var r in e)if(({}).hasOwnProperty.call(e,r)){if(-1!==t.indexOf(r))continue;n[r]=e[r]}return n}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(r=0;r<a.length;r++)n=a[r],-1===t.indexOf(n)&&({}).propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}function eH(e,t,n){var r=j.useRef({});return(!("value"in r.current)||n(r.current.condition,t))&&(r.current.value=e(),r.current.condition=t),r.current.value}var eD={},eB=[];function ez(e,t){}function eW(e,t){}function eU(e,t,n){t||eD[n]||(e(!1,n),eD[n]=!0)}function eG(e,t){eU(ez,e,t)}eG.preMessage=function(e){eB.push(e)},eG.resetWarned=function(){eD={}},eG.noteOnce=function(e,t){eU(eW,e,t)};let eK=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=new Set;return function e(t,o){var a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,i=r.has(t);if(eG(!i,"Warning: There may be circular references"),i)return!1;if(t===o)return!0;if(n&&a>1)return!1;r.add(t);var s=a+1;if(Array.isArray(t)){if(!Array.isArray(o)||t.length!==o.length)return!1;for(var l=0;l<t.length;l++)if(!e(t[l],o[l],s))return!1;return!0}if(t&&o&&"object"===ew(t)&&"object"===ew(o)){var c=Object.keys(t);return c.length===Object.keys(o).length&&c.every(function(n){return e(t[n],o[n],s)})}return!1}(e,t)};function eq(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}function eX(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,eS(r.key),r)}}function eV(e,t,n){return t&&eX(e.prototype,t),n&&eX(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function eY(e){return e.join("%")}var eQ=function(){function e(t){eq(this,e),eC(this,"instanceId",void 0),eC(this,"cache",new Map),this.instanceId=t}return eV(e,[{key:"get",value:function(e){return this.opGet(eY(e))}},{key:"opGet",value:function(e){return this.cache.get(e)||null}},{key:"update",value:function(e,t){return this.opUpdate(eY(e),t)}},{key:"opUpdate",value:function(e,t){var n=t(this.cache.get(e));null===n?this.cache.delete(e):this.cache.set(e,n)}}]),e}(),eJ="data-token-hash",e0="data-css-hash",e1="__cssinjs_instance__",e2=j.createContext({hashPriority:"low",cache:function(){var e=Math.random().toString(12).slice(2);if("undefined"!=typeof document&&document.head&&document.body){var t=document.body.querySelectorAll("style[".concat(e0,"]"))||[],n=document.head.firstChild;Array.from(t).forEach(function(t){t[e1]=t[e1]||e,t[e1]===e&&document.head.insertBefore(t,n)});var r={};Array.from(document.querySelectorAll("style[".concat(e0,"]"))).forEach(function(t){var n,o=t.getAttribute(e0);r[o]?t[e1]===e&&(null==(n=t.parentNode)||n.removeChild(t)):r[o]=!0})}return new eQ(e)}(),defaultCache:!0});function e5(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function e4(e,t){return(e4=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function e6(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&e4(e,t)}function e3(e){return(e3=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function e8(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(e8=function(){return!!e})()}function e7(e){var t=e8();return function(){var n,r=e3(e);n=t?Reflect.construct(r,arguments,e3(this).constructor):r.apply(this,arguments);if(n&&("object"==ew(n)||"function"==typeof n))return n;if(void 0!==n)throw TypeError("Derived constructors may only return object or undefined");return e5(this)}}var e9=function(){function e(){eq(this,e),eC(this,"cache",void 0),eC(this,"keys",void 0),eC(this,"cacheCallTimes",void 0),this.cache=new Map,this.keys=[],this.cacheCallTimes=0}return eV(e,[{key:"size",value:function(){return this.keys.length}},{key:"internalGet",value:function(e){var t,n,r=arguments.length>1&&void 0!==arguments[1]&&arguments[1],o={map:this.cache};return e.forEach(function(e){if(o){var t;o=null==(t=o)||null==(t=t.map)?void 0:t.get(e)}else o=void 0}),null!=(t=o)&&t.value&&r&&(o.value[1]=this.cacheCallTimes++),null==(n=o)?void 0:n.value}},{key:"get",value:function(e){var t;return null==(t=this.internalGet(e,!0))?void 0:t[0]}},{key:"has",value:function(e){return!!this.internalGet(e)}},{key:"set",value:function(t,n){var r=this;if(!this.has(t)){if(this.size()+1>e.MAX_CACHE_SIZE+e.MAX_CACHE_OFFSET){var o=this.keys.reduce(function(e,t){var n=ej(e,2)[1];return r.internalGet(t)[1]<n?[t,r.internalGet(t)[1]]:e},[this.keys[0],this.cacheCallTimes]),a=ej(o,1)[0];this.delete(a)}this.keys.push(t)}var i=this.cache;t.forEach(function(e,o){if(o===t.length-1)i.set(e,{value:[n,r.cacheCallTimes++]});else{var a=i.get(e);a?a.map||(a.map=new Map):i.set(e,{map:new Map}),i=i.get(e).map}})}},{key:"deleteByPath",value:function(e,t){var n,r=e.get(t[0]);if(1===t.length)return r.map?e.set(t[0],{map:r.map}):e.delete(t[0]),null==(n=r.value)?void 0:n[0];var o=this.deleteByPath(r.map,t.slice(1));return r.map&&0!==r.map.size||r.value||e.delete(t[0]),o}},{key:"delete",value:function(e){if(this.has(e))return this.keys=this.keys.filter(function(t){return!function(e,t){if(e.length!==t.length)return!1;for(var n=0;n<e.length;n++)if(e[n]!==t[n])return!1;return!0}(t,e)}),this.deleteByPath(this.cache,e)}}]),e}();eC(e9,"MAX_CACHE_SIZE",20),eC(e9,"MAX_CACHE_OFFSET",5);var te=0,tt=function(){function e(t){eq(this,e),eC(this,"derivatives",void 0),eC(this,"id",void 0),this.derivatives=Array.isArray(t)?t:[t],this.id=te,0===t.length&&t.length,te+=1}return eV(e,[{key:"getDerivativeToken",value:function(e){return this.derivatives.reduce(function(t,n){return n(e,t)},void 0)}}]),e}(),tn=new e9;function tr(e){var t=Array.isArray(e)?e:[e];return tn.has(t)||tn.set(t,new tt(t)),tn.get(t)}var to=new WeakMap,ta={},ti=new WeakMap;function ts(e){var t=ti.get(e)||"";return t||(Object.keys(e).forEach(function(n){var r=e[n];t+=n,r instanceof tt?t+=r.id:r&&"object"===ew(r)?t+=ts(r):t+=r}),t=eO(t),ti.set(e,t)),t}function tl(e,t){return eO("".concat(t,"_").concat(ts(e)))}"random-".concat(Date.now(),"-").concat(Math.random()).replace(/\./g,"");var tc=e_();function tu(e){return"number"==typeof e?"".concat(e,"px"):e}function td(e,t,n){var r,o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},a=arguments.length>4&&void 0!==arguments[4]&&arguments[4];if(a)return e;var i=eE(eE({},o),{},(eC(r={},eJ,t),eC(r,e0,n),r)),s=Object.keys(i).map(function(e){var t=i[e];return t?"".concat(e,'="').concat(t,'"'):null}).filter(function(e){return e}).join(" ");return"<style ".concat(s,">").concat(e,"</style>")}var tf=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return"--".concat(t?"".concat(t,"-"):"").concat(e).replace(/([a-z0-9])([A-Z])/g,"$1-$2").replace(/([A-Z]+)([A-Z][a-z0-9]+)/g,"$1-$2").replace(/([a-z])([A-Z0-9])/g,"$1-$2").toLowerCase()},tp=function(e,t,n){var r,o={},a={};return Object.entries(e).forEach(function(e){var t=ej(e,2),r=t[0],i=t[1];if(null!=n&&null!=(s=n.preserve)&&s[r])a[r]=i;else if(("string"==typeof i||"number"==typeof i)&&!(null!=n&&null!=(l=n.ignore)&&l[r])){var s,l,c,u=tf(r,null==n?void 0:n.prefix);o[u]="number"!=typeof i||null!=n&&null!=(c=n.unitless)&&c[r]?String(i):"".concat(i,"px"),a[r]="var(".concat(u,")")}}),[a,(r={scope:null==n?void 0:n.scope},Object.keys(o).length?".".concat(t).concat(null!=r&&r.scope?".".concat(r.scope):"","{").concat(Object.entries(o).map(function(e){var t=ej(e,2),n=t[0],r=t[1];return"".concat(n,":").concat(r,";")}).join(""),"}"):"")]},th=e_()?j.useLayoutEffect:j.useEffect;let tm=function(e,t){var n=j.useRef(!0);th(function(){return e(n.current)},t),th(function(){return n.current=!1,function(){n.current=!0}},[])};var tv=eE({},w).useInsertionEffect,tg=tv?function(e,t,n){return tv(function(){return e(),t()},n)}:function(e,t,n){j.useMemo(e,n),tm(function(){return t(!0)},n)},ty=void 0!==eE({},w).useInsertionEffect?function(e){var t=[],n=!1;return j.useEffect(function(){return n=!1,function(){n=!0,t.length&&t.forEach(function(e){return e()})}},e),function(e){n||t.push(e)}}:function(){return function(e){e()}},tb=eu("../../node_modules/.pnpm/@ant-design+cssinjs@1.23.0__926d2fbe617ecfde386169564e1f17aa/node_modules/@ant-design/cssinjs/es/hooks/useHMR.js");function tx(e,t,n,r,o){var a=j.useContext(e2).cache,i=eY([e].concat(eh(t))),s=ty([i]);(0,tb.Z)();var l=function(e){a.opUpdate(i,function(t){var r=ej(t||[void 0,void 0],2),o=r[0],a=[void 0===o?0:o,r[1]||n()];return e?e(a):a})};j.useMemo(function(){l()},[i]);var c=a.opGet(i)[1];return tg(function(){null==o||o(c)},function(e){return l(function(t){var n=ej(t,2),r=n[0],a=n[1];return e&&0===r&&(null==o||o(c)),[r+1,a]}),function(){a.opUpdate(i,function(t){var n=ej(t||[],2),o=n[0],l=void 0===o?0:o,c=n[1];return 0==l-1?(s(function(){(e||!a.opGet(i))&&(null==r||r(c,!1))}),null):[l-1,c]})}},[i]),c}var tj={},tw=new Map,tS=function(e,t,n,r){var o=eE(eE({},n.getDerivativeToken(e)),t);return r&&(o=r(o)),o},tC="token";function tk(){return(tk=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(null,arguments)}let tE={animationIterationCount:1,borderImageOutset:1,borderImageSlice:1,borderImageWidth:1,boxFlex:1,boxFlexGroup:1,boxOrdinalGroup:1,columnCount:1,columns:1,flex:1,flexGrow:1,flexPositive:1,flexShrink:1,flexNegative:1,flexOrder:1,gridRow:1,gridRowEnd:1,gridRowSpan:1,gridRowStart:1,gridColumn:1,gridColumnEnd:1,gridColumnSpan:1,gridColumnStart:1,msGridRow:1,msGridRowSpan:1,msGridColumn:1,msGridColumnSpan:1,fontWeight:1,lineHeight:1,opacity:1,order:1,orphans:1,tabSize:1,widows:1,zIndex:1,zoom:1,WebkitLineClamp:1,fillOpacity:1,floodOpacity:1,stopOpacity:1,strokeDasharray:1,strokeDashoffset:1,strokeMiterlimit:1,strokeOpacity:1,strokeWidth:1};var tO="comm",t_="rule",tN="decl",tA=Math.abs,tM=String.fromCharCode;function tP(e,t,n){return e.replace(t,n)}function tT(e,t){return 0|e.charCodeAt(t)}function tR(e,t,n){return e.slice(t,n)}function tI(e){return e.length}function tL(e,t){return t.push(e),e}function tF(e,t){for(var n="",r=0;r<e.length;r++)n+=t(e[r],r,e,t)||"";return n}function t$(e,t,n,r){switch(e.type){case"@layer":if(e.children.length)break;case"@import":case"@namespace":case tN:return e.return=e.return||e.value;case tO:return"";case"@keyframes":return e.return=e.value+"{"+tF(e.children,r)+"}";case t_:if(!tI(e.value=e.props.join(",")))return""}return tI(n=tF(e.children,r))?e.return=e.value+"{"+n+"}":""}var tZ=1,tH=1,tD=0,tB=0,tz=0,tW="";function tU(e,t,n,r,o,a,i,s){return{value:e,root:t,parent:n,type:r,props:o,children:a,line:tZ,column:tH,length:i,return:"",siblings:s}}function tG(){return tz=tB<tD?tT(tW,tB++):0,tH++,10===tz&&(tH=1,tZ++),tz}function tK(){return tT(tW,tB)}function tq(e){switch(e){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function tX(e){var t,n;return(t=tB-1,n=function e(t){for(;tG();)switch(tz){case t:return tB;case 34:case 39:34!==t&&39!==t&&e(tz);break;case 40:41===t&&e(t);break;case 92:tG()}return tB}(91===e?e+2:40===e?e+1:e),tR(tW,t,n)).trim()}function tV(e,t,n,r,o,a,i,s,l,c,u,d){for(var f=o-1,p=0===o?a:[""],h=p.length,m=0,v=0,g=0;m<r;++m)for(var y=0,b=tR(e,f+1,f=tA(v=i[m])),x=e;y<h;++y)(x=(v>0?p[y]+" "+b:tP(b,/&\f/g,p[y])).trim())&&(l[g++]=x);return tU(e,t,n,0===o?t_:s,l,c,u,d)}function tY(e,t,n,r,o){return tU(e,t,n,tN,tR(e,0,r),tR(e,r+1,-1),r,o)}var tQ="data-ant-cssinjs-cache-path",tJ="_FILE_STYLE__",t0=!0,t1="_multi_value_";function t2(e){var t,n,r;return tF((r=function e(t,n,r,o,a,i,s,l,c){for(var u,d,f,p,h,m,v=0,g=0,y=s,b=0,x=0,j=0,w=1,S=1,C=1,k=0,E="",O=a,_=i,N=o,A=E;S;)switch(j=k,k=tG()){case 40:if(108!=j&&58==tT(A,y-1)){-1!=(h=A+=tP(tX(k),"&","&\f"),m=tA(v?l[v-1]:0),h.indexOf("&\f",m))&&(C=-1);break}case 34:case 39:case 91:A+=tX(k);break;case 9:case 10:case 13:case 32:A+=function(e){for(;tz=tK();)if(tz<33)tG();else break;return tq(e)>2||tq(tz)>3?"":" "}(j);break;case 92:A+=function(e,t){for(var n;--t&&tG()&&!(tz<48)&&!(tz>102)&&(!(tz>57)||!(tz<65))&&(!(tz>70)||!(tz<97)););return n=tB+(t<6&&32==tK()&&32==tG()),tR(tW,e,n)}(tB-1,7);continue;case 47:switch(tK()){case 42:case 47:tL((u=function(e,t){for(;tG();)if(e+tz===57)break;else if(e+tz===84&&47===tK())break;return"/*"+tR(tW,t,tB-1)+"*"+tM(47===e?e:tG())}(tG(),tB),d=n,f=r,p=c,tU(u,d,f,tO,tM(tz),tR(u,2,-2),0,p)),c),(5==tq(j||1)||5==tq(tK()||1))&&tI(A)&&" "!==tR(A,-1,void 0)&&(A+=" ");break;default:A+="/"}break;case 123*w:l[v++]=tI(A)*C;case 125*w:case 59:case 0:switch(k){case 0:case 125:S=0;case 59+g:-1==C&&(A=tP(A,/\f/g,"")),x>0&&(tI(A)-y||0===w&&47===j)&&tL(x>32?tY(A+";",o,r,y-1,c):tY(tP(A," ","")+";",o,r,y-2,c),c);break;case 59:A+=";";default:if(tL(N=tV(A,n,r,v,g,a,l,E,O=[],_=[],y,i),i),123===k)if(0===g)e(A,n,N,N,O,i,y,l,_);else{switch(b){case 99:if(110===tT(A,3))break;case 108:if(97===tT(A,2))break;default:g=0;case 100:case 109:case 115:}g?e(t,N,N,o&&tL(tV(t,N,N,0,0,a,l,E,a,O=[],y,_),_),a,_,y,l,o?O:_):e(A,N,N,N,[""],_,0,l,_)}}v=g=x=0,w=C=1,E=A="",y=s;break;case 58:y=1+tI(A),x=j;default:if(w<1){if(123==k)--w;else if(125==k&&0==w++&&125==(tz=tB>0?tT(tW,--tB):0,tH--,10===tz&&(tH=1,tZ--),tz))continue}switch(A+=tM(k),k*w){case 38:C=g>0?1:(A+="\f",-1);break;case 44:l[v++]=(tI(A)-1)*C,C=1;break;case 64:45===tK()&&(A+=tX(tG())),b=tK(),g=y=tI(E=A+=function(e){for(;!tq(tK());)tG();return tR(tW,e,tB)}(tB)),k++;break;case 45:45===j&&2==tI(A)&&(w=0)}}return i}("",null,null,null,[""],(n=t=e,tZ=tH=1,tD=tI(tW=n),tB=0,t=[]),0,[0],t),tW="",r),t$).replace(/\{%%%\:[^;];}/g,";")}function t5(e,t,n){if(!t)return e;var r=".".concat(t),o="low"===n?":where(".concat(r,")"):r;return e.split(",").map(function(e){var t,n=e.trim().split(/\s+/),r=n[0]||"",a=(null==(t=r.match(/^\w+/))?void 0:t[0])||"";return[r="".concat(a).concat(o).concat(r.slice(a.length))].concat(eh(n.slice(1))).join(" ")}).join(",")}var t4=function e(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{root:!0,parentSelectors:[]},o=r.root,a=r.injectHash,i=r.parentSelectors,s=n.hashId,l=n.layer,c=(n.path,n.hashPriority),u=n.transformers,d=void 0===u?[]:u,f=(n.linters,""),p={};function h(t){var r=t.getName(s);if(!p[r]){var o=ej(e(t.style,n,{root:!1,parentSelectors:i}),1)[0];p[r]="@keyframes ".concat(t.getName(s)).concat(o)}}return(function e(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];return t.forEach(function(t){Array.isArray(t)?e(t,n):t&&n.push(t)}),n})(Array.isArray(t)?t:[t]).forEach(function(t){var r="string"!=typeof t||o?t:{};if("string"==typeof r)f+="".concat(r,"\n");else if(r._keyframe)h(r);else{var l=d.reduce(function(e,t){var n;return(null==t||null==(n=t.visit)?void 0:n.call(t,e))||e},r);Object.keys(l).forEach(function(t){var r=l[t];if("object"!==ew(r)||!r||"animationName"===t&&r._keyframe||"object"===ew(r)&&r&&("_skip_check_"in r||t1 in r)){function u(e,t){var n=e.replace(/[A-Z]/g,function(e){return"-".concat(e.toLowerCase())}),r=t;tE[e]||"number"!=typeof r||0===r||(r="".concat(r,"px")),"animationName"===e&&null!=t&&t._keyframe&&(h(t),r=t.getName(s)),f+="".concat(n,":").concat(r,";")}var d,m=null!=(d=null==r?void 0:r.value)?d:r;"object"===ew(r)&&null!=r&&r[t1]&&Array.isArray(m)?m.forEach(function(e){u(t,e)}):u(t,m)}else{var v=!1,g=t.trim(),y=!1;(o||a)&&s?g.startsWith("@")?v=!0:g="&"===g?t5("",s,c):t5(t,s,c):o&&!s&&("&"===g||""===g)&&(g="",y=!0);var b=ej(e(r,n,{root:y,injectHash:v,parentSelectors:[].concat(eh(i),[g])}),2),x=b[0],j=b[1];p=eE(eE({},p),j),f+="".concat(g).concat(x)}})}}),o?l&&(f&&(f="@layer ".concat(l.name," {").concat(f,"}")),l.dependencies&&(p["@layer ".concat(l.name)]=l.dependencies.map(function(e){return"@layer ".concat(e,", ").concat(l.name,";")}).join("\n"))):f="{".concat(f,"}"),[f,p]};function t6(e,t){return eO("".concat(e.join("%")).concat(t))}function t3(){return null}var t8="style";function t7(e,t){var n=e.token,r=e.path,o=e.hashId,a=e.layer,i=e.nonce,s=e.clientOnly,l=e.order,c=void 0===l?0:l,u=j.useContext(e2),d=u.autoClear,f=(u.mock,u.defaultCache),p=u.hashPriority,h=u.container,m=u.ssrInline,g=u.transformers,y=u.linters,b=u.cache,x=u.layer,w=n._tokenKey,S=[w];x&&S.push("layer"),S.push.apply(S,eh(r));var C=tx(t8,S,function(){var e=S.join("|");if(function(e){if(!v&&(v={},e_())){var t,n=document.createElement("div");n.className=tQ,n.style.position="fixed",n.style.visibility="hidden",n.style.top="-9999px",document.body.appendChild(n);var r=getComputedStyle(n).content||"";(r=r.replace(/^"/,"").replace(/"$/,"")).split(";").forEach(function(e){var t=ej(e.split(":"),2),n=t[0],r=t[1];v[n]=r});var o=document.querySelector("style[".concat(tQ,"]"));o&&(t0=!1,null==(t=o.parentNode)||t.removeChild(o)),document.body.removeChild(n)}return!!v[e]}(e)){var n=ej(function(e){var t=v[e],n=null;if(t&&e_())if(t0)n=tJ;else{var r=document.querySelector("style[".concat(e0,'="').concat(v[e],'"]'));r?n=r.innerHTML:delete v[e]}return[n,t]}(e),2),i=n[0],l=n[1];if(i)return[i,w,l,{},s,c]}var u=ej(t4(t(),{hashId:o,hashPriority:p,layer:x?a:void 0,path:r.join("-"),transformers:g,linters:y}),2),d=u[0],f=u[1],h=t2(d),m=t6(S,h);return[h,w,m,f,s,c]},function(e,t){var n=ej(e,3)[2];(t||d)&&tc&&eF(n,{mark:e0})},function(e){var t=ej(e,4),n=t[0],r=(t[1],t[2]),o=t[3];if(tc&&n!==tJ){var a={mark:e0,prepend:!x&&"queue",attachTo:h,priority:c},s="function"==typeof i?i():i;s&&(a.csp={nonce:s});var l=[],u=[];Object.keys(o).forEach(function(e){e.startsWith("@layer")?l.push(e):u.push(e)}),l.forEach(function(e){e$(t2(o[e]),"_layer-".concat(e),eE(eE({},a),{},{prepend:!0}))});var d=e$(n,r,a);d[e1]=b.instanceId,d.setAttribute(eJ,w),u.forEach(function(e){e$(t2(o[e]),"_effect-".concat(e),a)})}}),k=ej(C,3),E=k[0],O=k[1],_=k[2];return function(e){var t,n;return t=m&&!tc&&f?j.createElement("style",tk({},(eC(n={},eJ,O),eC(n,e0,_),n),{dangerouslySetInnerHTML:{__html:E}})):j.createElement(t3,null),j.createElement(j.Fragment,null,t,e)}}var t9="cssVar";let ne=function(e,t){var n=e.key,r=e.prefix,o=e.unitless,a=e.ignore,i=e.token,s=e.scope,l=void 0===s?"":s,c=(0,j.useContext)(e2),u=c.cache.instanceId,d=c.container,f=i._tokenKey,p=[].concat(eh(e.path),[n,l,f]);return tx(t9,p,function(){var e=ej(tp(t(),n,{prefix:r,unitless:o,ignore:a,scope:l}),2),i=e[0],s=e[1],c=t6(p,s);return[i,s,c,n]},function(e){var t=ej(e,3)[2];tc&&eF(t,{mark:e0})},function(e){var t=ej(e,3),r=t[1],o=t[2];if(r){var a=e$(r,o,{mark:e0,prepend:"queue",attachTo:d,priority:-999});a[e1]=u,a.setAttribute(eJ,n)}})};eC(g={},t8,function(e,t,n){var r=ej(e,6),o=r[0],a=r[1],i=r[2],s=r[3],l=r[4],c=r[5],u=(n||{}).plain;if(l)return null;var d=o,f={"data-rc-order":"prependQueue","data-rc-priority":"".concat(c)};return d=td(o,a,i,f,u),s&&Object.keys(s).forEach(function(e){if(!t[e]){t[e]=!0;var n=td(t2(s[e]),a,"_effect-".concat(e),f,u);e.startsWith("@layer")?d=n+d:d+=n}}),[c,i,d]}),eC(g,tC,function(e,t,n){var r=ej(e,5),o=r[2],a=r[3],i=r[4],s=(n||{}).plain;if(!a)return null;var l=o._tokenKey,c=td(a,i,l,{"data-rc-order":"prependQueue","data-rc-priority":"".concat(-999)},s);return[-999,l,c]}),eC(g,t9,function(e,t,n){var r=ej(e,4),o=r[1],a=r[2],i=r[3],s=(n||{}).plain;if(!o)return null;var l=td(o,i,a,{"data-rc-order":"prependQueue","data-rc-priority":"".concat(-999)},s);return[-999,a,l]});var nt=function(){function e(t,n){eq(this,e),eC(this,"name",void 0),eC(this,"style",void 0),eC(this,"_keyframe",!0),this.name=t,this.style=n}return eV(e,[{key:"getName",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return e?"".concat(e,"-").concat(this.name):this.name}}]),e}();function nn(e){return e.notSplit=!0,e}nn(["borderTop","borderBottom"]),nn(["borderTop"]),nn(["borderBottom"]),nn(["borderLeft","borderRight"]),nn(["borderLeft"]),nn(["borderRight"]);var nr=(0,j.createContext)({});function no(e,t){for(var n=e,r=0;r<t.length;r+=1){if(null==n)return;n=n[t[r]]}return n}function na(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]&&arguments[3];return t.length&&r&&void 0===n&&!no(e,t.slice(0,-1))?e:function e(t,n,r,o){if(!n.length)return r;var a,i=eb(n)||ef(n)||ep(n)||ex(),s=i[0],l=i.slice(1);return a=t||"number"!=typeof s?Array.isArray(t)?eh(t):eE({},t):[],o&&void 0===r&&1===l.length?delete a[s][l[0]]:a[s]=e(a[s],l,r,o),a}(e,t,n,r)}function ni(e){return Array.isArray(e)?[]:{}}var ns="undefined"==typeof Reflect?Object.keys:Reflect.ownKeys;function nl(){}let nc=j.createContext({}),nu=()=>{let e=()=>{};return e.deprecated=nl,e},nd=(0,j.createContext)(void 0);Object.assign({placeholder:"Select date",yearPlaceholder:"Select year",quarterPlaceholder:"Select quarter",monthPlaceholder:"Select month",weekPlaceholder:"Select week",rangePlaceholder:["Start date","End date"],rangeYearPlaceholder:["Start year","End year"],rangeQuarterPlaceholder:["Start quarter","End quarter"],rangeMonthPlaceholder:["Start month","End month"],rangeWeekPlaceholder:["Start week","End week"]},eE(eE({},{yearFormat:"YYYY",dayFormat:"D",cellMeridiemFormat:"A",monthBeforeYear:!0}),{},{locale:"en_US",today:"Today",now:"Now",backToToday:"Back to today",ok:"OK",clear:"Clear",week:"Week",month:"Month",year:"Year",timeSelect:"select time",dateSelect:"select date",weekSelect:"Choose a week",monthSelect:"Choose a month",yearSelect:"Choose a year",decadeSelect:"Choose a decade",dateFormat:"M/D/YYYY",dateTimeFormat:"M/D/YYYY HH:mm:ss",previousMonth:"Previous month (PageUp)",nextMonth:"Next month (PageDown)",previousYear:"Last year (Control + left)",nextYear:"Next year (Control + right)",previousDecade:"Last decade",nextDecade:"Next decade",previousCentury:"Last century",nextCentury:"Next century"})),Object.assign({},{placeholder:"Select time",rangePlaceholder:["Start time","End time"]});let nf="${label} is not a valid ${type}",np={Modal:{okText:"OK",cancelText:"Cancel",justOkText:"OK"},Form:{optional:"(optional)",defaultValidateMessages:{default:"Field validation error for ${label}",required:"Please enter ${label}",enum:"${label} must be one of [${enum}]",whitespace:"${label} cannot be a blank character",date:{format:"${label} date format is invalid",parse:"${label} cannot be converted to a date",invalid:"${label} is an invalid date"},types:{string:nf,method:nf,array:nf,object:nf,number:nf,date:nf,boolean:nf,integer:nf,float:nf,regexp:nf,email:nf,url:nf,hex:nf},string:{len:"${label} must be ${len} characters",min:"${label} must be at least ${min} characters",max:"${label} must be up to ${max} characters",range:"${label} must be between ${min}-${max} characters"},number:{len:"${label} must be equal to ${len}",min:"${label} must be minimum ${min}",max:"${label} must be maximum ${max}",range:"${label} must be between ${min}-${max}"},array:{len:"Must be ${len} ${label}",min:"At least ${min} ${label}",max:"At most ${max} ${label}",range:"The amount of ${label} must be between ${min}-${max}"},pattern:{mismatch:"${label} does not match the pattern ${pattern}"}}}};Object.assign({},np.Modal);let nh=[],nm=()=>nh.reduce((e,t)=>Object.assign(Object.assign({},e),t),np.Modal),nv=(0,j.createContext)(void 0),ng=e=>{let{locale:t={},children:n,_ANT_MARK__:r}=e;j.useEffect(()=>(function(e){if(e){let t=Object.assign({},e);return nh.push(t),nm(),()=>{nh=nh.filter(e=>e!==t),nm()}}Object.assign({},np.Modal)})(null==t?void 0:t.Modal),[t]);let o=j.useMemo(()=>Object.assign(Object.assign({},t),{exist:!0}),[t]);return j.createElement(nv.Provider,{value:o},n)},ny=Math.round;function nb(e,t){let n=e.replace(/^[^(]*\((.*)/,"$1").replace(/\).*/,"").match(/\d*\.?\d+%?/g)||[],r=n.map(e=>parseFloat(e));for(let e=0;e<3;e+=1)r[e]=t(r[e]||0,n[e]||"",e);return n[3]?r[3]=n[3].includes("%")?r[3]/100:r[3]:r[3]=1,r}let nx=(e,t,n)=>0===n?e:e/100;function nj(e,t){let n=t||255;return e>n?n:e<0?0:e}class nw{setR(e){return this._sc("r",e)}setG(e){return this._sc("g",e)}setB(e){return this._sc("b",e)}setA(e){return this._sc("a",e,1)}setHue(e){let t=this.toHsv();return t.h=e,this._c(t)}getLuminance(){function e(e){let t=e/255;return t<=.03928?t/12.92:Math.pow((t+.055)/1.055,2.4)}let t=e(this.r);return .2126*t+.7152*e(this.g)+.0722*e(this.b)}getHue(){if(void 0===this._h){let e=this.getMax()-this.getMin();0===e?this._h=0:this._h=ny(60*(this.r===this.getMax()?(this.g-this.b)/e+6*(this.g<this.b):this.g===this.getMax()?(this.b-this.r)/e+2:(this.r-this.g)/e+4))}return this._h}getSaturation(){if(void 0===this._s){let e=this.getMax()-this.getMin();0===e?this._s=0:this._s=e/this.getMax()}return this._s}getLightness(){return void 0===this._l&&(this._l=(this.getMax()+this.getMin())/510),this._l}getValue(){return void 0===this._v&&(this._v=this.getMax()/255),this._v}getBrightness(){return void 0===this._brightness&&(this._brightness=(299*this.r+587*this.g+114*this.b)/1e3),this._brightness}darken(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:10,t=this.getHue(),n=this.getSaturation(),r=this.getLightness()-e/100;return r<0&&(r=0),this._c({h:t,s:n,l:r,a:this.a})}lighten(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:10,t=this.getHue(),n=this.getSaturation(),r=this.getLightness()+e/100;return r>1&&(r=1),this._c({h:t,s:n,l:r,a:this.a})}mix(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:50,n=this._c(e),r=t/100,o=e=>(n[e]-this[e])*r+this[e],a={r:ny(o("r")),g:ny(o("g")),b:ny(o("b")),a:ny(100*o("a"))/100};return this._c(a)}tint(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:10;return this.mix({r:255,g:255,b:255,a:1},e)}shade(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:10;return this.mix({r:0,g:0,b:0,a:1},e)}onBackground(e){let t=this._c(e),n=this.a+t.a*(1-this.a),r=e=>ny((this[e]*this.a+t[e]*t.a*(1-this.a))/n);return this._c({r:r("r"),g:r("g"),b:r("b"),a:n})}isDark(){return 128>this.getBrightness()}isLight(){return this.getBrightness()>=128}equals(e){return this.r===e.r&&this.g===e.g&&this.b===e.b&&this.a===e.a}clone(){return this._c(this)}toHexString(){let e="#",t=(this.r||0).toString(16);e+=2===t.length?t:"0"+t;let n=(this.g||0).toString(16);e+=2===n.length?n:"0"+n;let r=(this.b||0).toString(16);if(e+=2===r.length?r:"0"+r,"number"==typeof this.a&&this.a>=0&&this.a<1){let t=ny(255*this.a).toString(16);e+=2===t.length?t:"0"+t}return e}toHsl(){return{h:this.getHue(),s:this.getSaturation(),l:this.getLightness(),a:this.a}}toHslString(){let e=this.getHue(),t=ny(100*this.getSaturation()),n=ny(100*this.getLightness());return 1!==this.a?`hsla(${e},${t}%,${n}%,${this.a})`:`hsl(${e},${t}%,${n}%)`}toHsv(){return{h:this.getHue(),s:this.getSaturation(),v:this.getValue(),a:this.a}}toRgb(){return{r:this.r,g:this.g,b:this.b,a:this.a}}toRgbString(){return 1!==this.a?`rgba(${this.r},${this.g},${this.b},${this.a})`:`rgb(${this.r},${this.g},${this.b})`}toString(){return this.toRgbString()}_sc(e,t,n){let r=this.clone();return r[e]=nj(t,n),r}_c(e){return new this.constructor(e)}getMax(){return void 0===this._max&&(this._max=Math.max(this.r,this.g,this.b)),this._max}getMin(){return void 0===this._min&&(this._min=Math.min(this.r,this.g,this.b)),this._min}fromHexString(e){let t=e.replace("#","");function n(e,n){return parseInt(t[e]+t[n||e],16)}t.length<6?(this.r=n(0),this.g=n(1),this.b=n(2),this.a=t[3]?n(3)/255:1):(this.r=n(0,1),this.g=n(2,3),this.b=n(4,5),this.a=t[6]?n(6,7)/255:1)}fromHsl(e){let{h:t,s:n,l:r,a:o}=e;if(this._h=t%360,this._s=n,this._l=r,this.a="number"==typeof o?o:1,n<=0){let e=ny(255*r);this.r=e,this.g=e,this.b=e}let a=0,i=0,s=0,l=t/60,c=(1-Math.abs(2*r-1))*n,u=c*(1-Math.abs(l%2-1));l>=0&&l<1?(a=c,i=u):l>=1&&l<2?(a=u,i=c):l>=2&&l<3?(i=c,s=u):l>=3&&l<4?(i=u,s=c):l>=4&&l<5?(a=u,s=c):l>=5&&l<6&&(a=c,s=u);let d=r-c/2;this.r=ny((a+d)*255),this.g=ny((i+d)*255),this.b=ny((s+d)*255)}fromHsv(e){let{h:t,s:n,v:r,a:o}=e;this._h=t%360,this._s=n,this._v=r,this.a="number"==typeof o?o:1;let a=ny(255*r);if(this.r=a,this.g=a,this.b=a,n<=0)return;let i=t/60,s=Math.floor(i),l=i-s,c=ny(r*(1-n)*255),u=ny(r*(1-n*l)*255),d=ny(r*(1-n*(1-l))*255);switch(s){case 0:this.g=d,this.b=c;break;case 1:this.r=u,this.b=c;break;case 2:this.r=c,this.b=d;break;case 3:this.r=c,this.g=u;break;case 4:this.r=d,this.g=c;break;default:this.g=c,this.b=u}}fromHsvString(e){let t=nb(e,nx);this.fromHsv({h:t[0],s:t[1],v:t[2],a:t[3]})}fromHslString(e){let t=nb(e,nx);this.fromHsl({h:t[0],s:t[1],l:t[2],a:t[3]})}fromRgbString(e){let t=nb(e,(e,t)=>t.includes("%")?ny(e/100*255):e);this.r=t[0],this.g=t[1],this.b=t[2],this.a=t[3]}constructor(e){function t(t){return t[0]in e&&t[1]in e&&t[2]in e}if(eC(this,"isValid",!0),eC(this,"r",0),eC(this,"g",0),eC(this,"b",0),eC(this,"a",1),eC(this,"_h",void 0),eC(this,"_s",void 0),eC(this,"_l",void 0),eC(this,"_v",void 0),eC(this,"_max",void 0),eC(this,"_min",void 0),eC(this,"_brightness",void 0),e)if("string"==typeof e){let t=e.trim();function n(e){return t.startsWith(e)}/^#?[A-F\d]{3,8}$/i.test(t)?this.fromHexString(t):n("rgb")?this.fromRgbString(t):n("hsl")?this.fromHslString(t):(n("hsv")||n("hsb"))&&this.fromHsvString(t)}else if(e instanceof nw)this.r=e.r,this.g=e.g,this.b=e.b,this.a=e.a,this._h=e._h,this._s=e._s,this._l=e._l,this._v=e._v;else if(t("rgb"))this.r=nj(e.r),this.g=nj(e.g),this.b=nj(e.b),this.a="number"==typeof e.a?nj(e.a,1):1;else if(t("hsl"))this.fromHsl(e);else if(t("hsv"))this.fromHsv(e);else throw Error("@ant-design/fast-color: unsupported input "+JSON.stringify(e))}}var nS=[{index:7,amount:15},{index:6,amount:25},{index:5,amount:30},{index:5,amount:45},{index:5,amount:65},{index:5,amount:85},{index:4,amount:90},{index:3,amount:95},{index:2,amount:97},{index:1,amount:98}];function nC(e,t,n){var r;return(r=Math.round(e.h)>=60&&240>=Math.round(e.h)?n?Math.round(e.h)-2*t:Math.round(e.h)+2*t:n?Math.round(e.h)+2*t:Math.round(e.h)-2*t)<0?r+=360:r>=360&&(r-=360),r}function nk(e,t,n){var r;return 0===e.h&&0===e.s?e.s:((r=n?e.s-.16*t:4===t?e.s+.16:e.s+.05*t)>1&&(r=1),n&&5===t&&r>.1&&(r=.1),r<.06&&(r=.06),Math.round(100*r)/100)}function nE(e,t,n){var r;return Math.round(100*Math.max(0,Math.min(1,n?e.v+.05*t:e.v-.15*t)))/100}function nO(e){for(var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=[],r=new nw(e),o=r.toHsv(),a=5;a>0;a-=1){var i=new nw({h:nC(o,a,!0),s:nk(o,a,!0),v:nE(o,a,!0)});n.push(i)}n.push(r);for(var s=1;s<=4;s+=1){var l=new nw({h:nC(o,s),s:nk(o,s),v:nE(o,s)});n.push(l)}return"dark"===t.theme?nS.map(function(e){var r=e.index,o=e.amount;return new nw(t.backgroundColor||"#141414").mix(n[r],o).toHexString()}):n.map(function(e){return e.toHexString()})}var n_={red:"#F5222D",volcano:"#FA541C",orange:"#FA8C16",gold:"#FAAD14",yellow:"#FADB14",lime:"#A0D911",green:"#52C41A",cyan:"#13C2C2",blue:"#1677FF",geekblue:"#2F54EB",purple:"#722ED1",magenta:"#EB2F96",grey:"#666666"},nN=["#fff1f0","#ffccc7","#ffa39e","#ff7875","#ff4d4f","#f5222d","#cf1322","#a8071a","#820014","#5c0011"];nN.primary=nN[5];var nA=["#fff2e8","#ffd8bf","#ffbb96","#ff9c6e","#ff7a45","#fa541c","#d4380d","#ad2102","#871400","#610b00"];nA.primary=nA[5];var nM=["#fff7e6","#ffe7ba","#ffd591","#ffc069","#ffa940","#fa8c16","#d46b08","#ad4e00","#873800","#612500"];nM.primary=nM[5];var nP=["#fffbe6","#fff1b8","#ffe58f","#ffd666","#ffc53d","#faad14","#d48806","#ad6800","#874d00","#613400"];nP.primary=nP[5];var nT=["#feffe6","#ffffb8","#fffb8f","#fff566","#ffec3d","#fadb14","#d4b106","#ad8b00","#876800","#614700"];nT.primary=nT[5];var nR=["#fcffe6","#f4ffb8","#eaff8f","#d3f261","#bae637","#a0d911","#7cb305","#5b8c00","#3f6600","#254000"];nR.primary=nR[5];var nI=["#f6ffed","#d9f7be","#b7eb8f","#95de64","#73d13d","#52c41a","#389e0d","#237804","#135200","#092b00"];nI.primary=nI[5];var nL=["#e6fffb","#b5f5ec","#87e8de","#5cdbd3","#36cfc9","#13c2c2","#08979c","#006d75","#00474f","#002329"];nL.primary=nL[5];var nF=["#e6f4ff","#bae0ff","#91caff","#69b1ff","#4096ff","#1677ff","#0958d9","#003eb3","#002c8c","#001d66"];nF.primary=nF[5];var n$=["#f0f5ff","#d6e4ff","#adc6ff","#85a5ff","#597ef7","#2f54eb","#1d39c4","#10239e","#061178","#030852"];n$.primary=n$[5];var nZ=["#f9f0ff","#efdbff","#d3adf7","#b37feb","#9254de","#722ed1","#531dab","#391085","#22075e","#120338"];nZ.primary=nZ[5];var nH=["#fff0f6","#ffd6e7","#ffadd2","#ff85c0","#f759ab","#eb2f96","#c41d7f","#9e1068","#780650","#520339"];nH.primary=nH[5];var nD=["#a6a6a6","#999999","#8c8c8c","#808080","#737373","#666666","#404040","#1a1a1a","#000000","#000000"];nD.primary=nD[5];var nB={red:nN,volcano:nA,orange:nM,gold:nP,yellow:nT,lime:nR,green:nI,cyan:nL,blue:nF,geekblue:n$,purple:nZ,magenta:nH,grey:nD},nz=["#2a1215","#431418","#58181c","#791a1f","#a61d24","#d32029","#e84749","#f37370","#f89f9a","#fac8c3"];nz.primary=nz[5];var nW=["#2b1611","#441d12","#592716","#7c3118","#aa3e19","#d84a1b","#e87040","#f3956a","#f8b692","#fad4bc"];nW.primary=nW[5];var nU=["#2b1d11","#442a11","#593815","#7c4a15","#aa6215","#d87a16","#e89a3c","#f3b765","#f8cf8d","#fae3b7"];nU.primary=nU[5];var nG=["#2b2111","#443111","#594214","#7c5914","#aa7714","#d89614","#e8b339","#f3cc62","#f8df8b","#faedb5"];nG.primary=nG[5];var nK=["#2b2611","#443b11","#595014","#7c6e14","#aa9514","#d8bd14","#e8d639","#f3ea62","#f8f48b","#fafab5"];nK.primary=nK[5];var nq=["#1f2611","#2e3c10","#3e4f13","#536d13","#6f9412","#8bbb11","#a9d134","#c9e75d","#e4f88b","#f0fab5"];nq.primary=nq[5];var nX=["#162312","#1d3712","#274916","#306317","#3c8618","#49aa19","#6abe39","#8fd460","#b2e58b","#d5f2bb"];nX.primary=nX[5];var nV=["#112123","#113536","#144848","#146262","#138585","#13a8a8","#33bcb7","#58d1c9","#84e2d8","#b2f1e8"];nV.primary=nV[5];var nY=["#111a2c","#112545","#15325b","#15417e","#1554ad","#1668dc","#3c89e8","#65a9f3","#8dc5f8","#b7dcfa"];nY.primary=nY[5];var nQ=["#131629","#161d40","#1c2755","#203175","#263ea0","#2b4acb","#5273e0","#7f9ef3","#a8c1f8","#d2e0fa"];nQ.primary=nQ[5];var nJ=["#1a1325","#24163a","#301c4d","#3e2069","#51258f","#642ab5","#854eca","#ab7ae0","#cda8f0","#ebd7fa"];nJ.primary=nJ[5];var n0=["#291321","#40162f","#551c3b","#75204f","#a02669","#cb2b83","#e0529c","#f37fb7","#f8a8cc","#fad2e3"];n0.primary=n0[5];var n1=["#151515","#1f1f1f","#2d2d2d","#393939","#494949","#5a5a5a","#6a6a6a","#7b7b7b","#888888","#969696"];n1.primary=n1[5];let n2={blue:"#1677FF",purple:"#722ED1",cyan:"#13C2C2",green:"#52C41A",magenta:"#EB2F96",pink:"#EB2F96",red:"#F5222D",orange:"#FA8C16",yellow:"#FADB14",volcano:"#FA541C",geekblue:"#2F54EB",gold:"#FAAD14",lime:"#A0D911"},n5=Object.assign(Object.assign({},n2),{colorPrimary:"#1677ff",colorSuccess:"#52c41a",colorWarning:"#faad14",colorError:"#ff4d4f",colorInfo:"#1677ff",colorLink:"",colorTextBase:"",colorBgBase:"",fontFamily:`-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial,
'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',
'Noto Color Emoji'`,fontFamilyCode:"'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace",fontSize:14,lineWidth:1,lineType:"solid",motionUnit:.1,motionBase:0,motionEaseOutCirc:"cubic-bezier(0.08, 0.82, 0.17, 1)",motionEaseInOutCirc:"cubic-bezier(0.78, 0.14, 0.15, 0.86)",motionEaseOut:"cubic-bezier(0.215, 0.61, 0.355, 1)",motionEaseInOut:"cubic-bezier(0.645, 0.045, 0.355, 1)",motionEaseOutBack:"cubic-bezier(0.12, 0.4, 0.29, 1.46)",motionEaseInBack:"cubic-bezier(0.71, -0.46, 0.88, 0.6)",motionEaseInQuint:"cubic-bezier(0.755, 0.05, 0.855, 0.06)",motionEaseOutQuint:"cubic-bezier(0.23, 1, 0.32, 1)",borderRadius:6,sizeUnit:4,sizeStep:4,sizePopupArrow:16,controlHeight:32,zIndexBase:0,zIndexPopupBase:1e3,opacityImage:1,wireframe:!1,motion:!0}),n4=e=>{let t=e,n=e,r=e,o=e;return e<6&&e>=5?t=e+1:e<16&&e>=6?t=e+2:e>=16&&(t=16),e<7&&e>=5?n=4:e<8&&e>=7?n=5:e<14&&e>=8?n=6:e<16&&e>=14?n=7:e>=16&&(n=8),e<6&&e>=2?r=1:e>=6&&(r=2),e>4&&e<8?o=4:e>=8&&(o=6),{borderRadius:e,borderRadiusXS:r,borderRadiusSM:n,borderRadiusLG:t,borderRadiusOuter:o}},n6=e=>{let{controlHeight:t}=e;return{controlHeightSM:.75*t,controlHeightXS:.5*t,controlHeightLG:1.25*t}},n3=e=>{let t=function(e){let t=Array.from({length:10}).map((t,n)=>{let r=e*Math.pow(Math.E,(n-1)/5);return 2*Math.floor((n>1?Math.floor(r):Math.ceil(r))/2)});return t[1]=e,t.map(e=>({size:e,lineHeight:(e+8)/e}))}(e),n=t.map(e=>e.size),r=t.map(e=>e.lineHeight),o=n[1],a=n[0],i=n[2],s=r[1],l=r[0],c=r[2];return{fontSizeSM:a,fontSize:o,fontSizeLG:i,fontSizeXL:n[3],fontSizeHeading1:n[6],fontSizeHeading2:n[5],fontSizeHeading3:n[4],fontSizeHeading4:n[3],fontSizeHeading5:n[2],lineHeight:s,lineHeightLG:c,lineHeightSM:l,fontHeight:Math.round(s*o),fontHeightLG:Math.round(c*i),fontHeightSM:Math.round(l*a),lineHeightHeading1:r[6],lineHeightHeading2:r[5],lineHeightHeading3:r[4],lineHeightHeading4:r[3],lineHeightHeading5:r[2]}},n8=(e,t)=>new nw(e).setA(t).toRgbString(),n7=(e,t)=>new nw(e).darken(t).toHexString(),n9=e=>{let t=nO(e);return{1:t[0],2:t[1],3:t[2],4:t[3],5:t[4],6:t[5],7:t[6],8:t[4],9:t[5],10:t[6]}},re=(e,t)=>{let n=e||"#fff",r=t||"#000";return{colorBgBase:n,colorTextBase:r,colorText:n8(r,.88),colorTextSecondary:n8(r,.65),colorTextTertiary:n8(r,.45),colorTextQuaternary:n8(r,.25),colorFill:n8(r,.15),colorFillSecondary:n8(r,.06),colorFillTertiary:n8(r,.04),colorFillQuaternary:n8(r,.02),colorBgSolid:n8(r,1),colorBgSolidHover:n8(r,.75),colorBgSolidActive:n8(r,.95),colorBgLayout:n7(n,4),colorBgContainer:n7(n,0),colorBgElevated:n7(n,0),colorBgSpotlight:n8(r,.85),colorBgBlur:"transparent",colorBorder:n7(n,15),colorBorderSecondary:n7(n,6)}},rt=tr(function(e){n_.pink=n_.magenta,nB.pink=nB.magenta;let t=Object.keys(n2).map(t=>{let n=e[t]===n_[t]?nB[t]:nO(e[t]);return Array.from({length:10},()=>1).reduce((e,r,o)=>(e[`${t}-${o+1}`]=n[o],e[`${t}${o+1}`]=n[o],e),{})}).reduce((e,t)=>e=Object.assign(Object.assign({},e),t),{});return Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},e),t),function(e,t){let{generateColorPalettes:n,generateNeutralColorPalettes:r}=t,{colorSuccess:o,colorWarning:a,colorError:i,colorInfo:s,colorPrimary:l,colorBgBase:c,colorTextBase:u}=e,d=n(l),f=n(o),p=n(a),h=n(i),m=n(s),v=r(c,u),g=n(e.colorLink||e.colorInfo),y=new nw(h[1]).mix(new nw(h[3]),50).toHexString();return Object.assign(Object.assign({},v),{colorPrimaryBg:d[1],colorPrimaryBgHover:d[2],colorPrimaryBorder:d[3],colorPrimaryBorderHover:d[4],colorPrimaryHover:d[5],colorPrimary:d[6],colorPrimaryActive:d[7],colorPrimaryTextHover:d[8],colorPrimaryText:d[9],colorPrimaryTextActive:d[10],colorSuccessBg:f[1],colorSuccessBgHover:f[2],colorSuccessBorder:f[3],colorSuccessBorderHover:f[4],colorSuccessHover:f[4],colorSuccess:f[6],colorSuccessActive:f[7],colorSuccessTextHover:f[8],colorSuccessText:f[9],colorSuccessTextActive:f[10],colorErrorBg:h[1],colorErrorBgHover:h[2],colorErrorBgFilledHover:y,colorErrorBgActive:h[3],colorErrorBorder:h[3],colorErrorBorderHover:h[4],colorErrorHover:h[5],colorError:h[6],colorErrorActive:h[7],colorErrorTextHover:h[8],colorErrorText:h[9],colorErrorTextActive:h[10],colorWarningBg:p[1],colorWarningBgHover:p[2],colorWarningBorder:p[3],colorWarningBorderHover:p[4],colorWarningHover:p[4],colorWarning:p[6],colorWarningActive:p[7],colorWarningTextHover:p[8],colorWarningText:p[9],colorWarningTextActive:p[10],colorInfoBg:m[1],colorInfoBgHover:m[2],colorInfoBorder:m[3],colorInfoBorderHover:m[4],colorInfoHover:m[4],colorInfo:m[6],colorInfoActive:m[7],colorInfoTextHover:m[8],colorInfoText:m[9],colorInfoTextActive:m[10],colorLinkHover:g[4],colorLink:g[6],colorLinkActive:g[7],colorBgMask:new nw("#000").setA(.45).toRgbString(),colorWhite:"#fff"})}(e,{generateColorPalettes:n9,generateNeutralColorPalettes:re})),n3(e.fontSize)),function(e){let{sizeUnit:t,sizeStep:n}=e;return{sizeXXL:t*(n+8),sizeXL:t*(n+4),sizeLG:t*(n+2),sizeMD:t*(n+1),sizeMS:t*n,size:t*n,sizeSM:t*(n-1),sizeXS:t*(n-2),sizeXXS:t*(n-3)}}(e)),n6(e)),function(e){let{motionUnit:t,motionBase:n,borderRadius:r,lineWidth:o}=e;return Object.assign({motionDurationFast:`${(n+t).toFixed(1)}s`,motionDurationMid:`${(n+2*t).toFixed(1)}s`,motionDurationSlow:`${(n+3*t).toFixed(1)}s`,lineWidthBold:o+1},n4(r))}(e))}),rn={token:n5,override:{override:n5},hashed:!0},rr=j.createContext(rn),ro=`-ant-${Date.now()}-${Math.random()}`,ra=j.createContext(!1),ri=e=>{let{children:t,disabled:n}=e,r=j.useContext(ra);return j.createElement(ra.Provider,{value:null!=n?n:r},t)},rs=j.createContext(void 0),rl=e=>{let{children:t,size:n}=e,r=j.useContext(rs);return j.createElement(rs.Provider,{value:n||r},t)},{useId:rc}=Object.assign({},w),ru=void 0===rc?()=>"":rc;var rd=eu("../../node_modules/.pnpm/classnames@2.5.1/node_modules/classnames/index.js"),rf=eu.n(rd);function rp(e){return e instanceof HTMLElement||e instanceof SVGElement}var rh=eu("../../node_modules/.pnpm/react-is@18.3.1/node_modules/react-is/index.js"),rm=Symbol.for("react.element"),rv=Symbol.for("react.transitional.element"),rg=Symbol.for("react.fragment"),ry=Number(j.version.split(".")[0]),rb=function(e,t){"function"==typeof e?e(t):"object"===ew(e)&&e&&"current"in e&&(e.current=t)},rx=function(e){if(!e)return!1;if(rj(e)&&ry>=19)return!0;var t,n,r=(0,rh.isMemo)(e)?e.type.type:e.type;return("function"!=typeof r||!!(null!=(t=r.prototype)&&t.render)||r.$$typeof===rh.ForwardRef)&&("function"!=typeof e||!!(null!=(n=e.prototype)&&n.render)||e.$$typeof===rh.ForwardRef)};function rj(e){return(0,j.isValidElement)(e)&&!(e&&"object"===ew(e)&&(e.$$typeof===rm||e.$$typeof===rv)&&e.type===rg)}var rw=["children"],rS=j.createContext({});function rC(e){var t=e.children,n=eZ(e,rw);return j.createElement(rS.Provider,{value:n},t)}var rk=function(e){e6(n,e);var t=e7(n);function n(){return eq(this,n),t.apply(this,arguments)}return eV(n,[{key:"render",value:function(){return this.props.children}}]),n}(j.Component);function rE(e){var t=j.useRef();return t.current=e,j.useCallback(function(){for(var e,n=arguments.length,r=Array(n),o=0;o<n;o++)r[o]=arguments[o];return null==(e=t.current)?void 0:e.call.apply(e,[t].concat(r))},[])}function rO(e){var t=j.useRef(!1),n=ej(j.useState(e),2),r=n[0],o=n[1];return j.useEffect(function(){return t.current=!1,function(){t.current=!0}},[]),[r,function(e,n){n&&t.current||o(e)}]}var r_="none",rN="appear",rA="enter",rM="leave",rP="none",rT="prepare",rR="start",rI="active",rL="prepared";function rF(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit".concat(e)]="webkit".concat(t),n["Moz".concat(e)]="moz".concat(t),n["ms".concat(e)]="MS".concat(t),n["O".concat(e)]="o".concat(t.toLowerCase()),n}var r$=(l=e_(),c="undefined"!=typeof window?window:{},u={animationend:rF("Animation","AnimationEnd"),transitionend:rF("Transition","TransitionEnd")},l&&("AnimationEvent"in c||delete u.animationend.animation,"TransitionEvent"in c||delete u.transitionend.transition),u),rZ={};e_()&&(rZ=document.createElement("div").style);var rH={};function rD(e){if(rH[e])return rH[e];var t=r$[e];if(t)for(var n=Object.keys(t),r=n.length,o=0;o<r;o+=1){var a=n[o];if(Object.prototype.hasOwnProperty.call(t,a)&&a in rZ)return rH[e]=t[a],rH[e]}return""}var rB=rD("animationend"),rz=rD("transitionend"),rW=!!(rB&&rz),rU=rB||"animationend",rG=rz||"transitionend";function rK(e,t){return e?"object"===ew(e)?e[t.replace(/-\w/g,function(e){return e[1].toUpperCase()})]:"".concat(e,"-").concat(t):null}let rq=function(e){var t=(0,j.useRef)();function n(t){t&&(t.removeEventListener(rG,e),t.removeEventListener(rU,e))}return j.useEffect(function(){return function(){n(t.current)}},[]),[function(r){t.current&&t.current!==r&&n(t.current),r&&r!==t.current&&(r.addEventListener(rG,e),r.addEventListener(rU,e),t.current=r)},n]};var rX=e_()?j.useLayoutEffect:j.useEffect,rV=function(e){return+setTimeout(e,16)},rY=function(e){return clearTimeout(e)};"undefined"!=typeof window&&"requestAnimationFrame"in window&&(rV=function(e){return window.requestAnimationFrame(e)},rY=function(e){return window.cancelAnimationFrame(e)});var rQ=0,rJ=new Map,r0=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,n=rQ+=1;return!function t(r){if(0===r)rJ.delete(n),e();else{var o=rV(function(){t(r-1)});rJ.set(n,o)}}(t),n};r0.cancel=function(e){var t=rJ.get(e);return rJ.delete(e),rY(t)};let r1=function(){var e=j.useRef(null);function t(){r0.cancel(e.current)}return j.useEffect(function(){return function(){t()}},[]),[function n(r){var o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:2;t();var a=r0(function(){o<=1?r({isCanceled:function(){return a!==e.current}}):n(r,o-1)});e.current=a},t]};var r2=[rT,rR,rI,"end"],r5=[rT,rL];function r4(e){return e===rI||"end"===e}let r6=function(e,t,n){var r=ej(rO(rP),2),o=r[0],a=r[1],i=ej(r1(),2),s=i[0],l=i[1],c=t?r5:r2;return rX(function(){if(o!==rP&&"end"!==o){var e=c.indexOf(o),t=c[e+1],r=n(o);!1===r?a(t,!0):t&&s(function(e){function n(){e.isCanceled()||a(t,!0)}!0===r?n():Promise.resolve(r).then(n)})}},[e,o]),j.useEffect(function(){return function(){l()}},[]),[function(){a(rT,!0)},o]},r3=(d=rW,"object"===ew(rW)&&(d=rW.transitionSupport),(f=j.forwardRef(function(e,t){var n,r=e.visible,o=void 0===r||r,a=e.removeOnLeave,i=void 0===a||a,s=e.forceRender,l=e.children,c=e.motionName,u=e.leavedClassName,f=e.eventProps,p=j.useContext(rS).motion,h=!!(e.motionName&&d&&!1!==p),m=(0,j.useRef)(),v=(0,j.useRef)(),g=function(e,t,n,r){var o,a,i=r.motionEnter,s=void 0===i||i,l=r.motionAppear,c=void 0===l||l,u=r.motionLeave,d=void 0===u||u,f=r.motionDeadline,p=r.motionLeaveImmediately,h=r.onAppearPrepare,m=r.onEnterPrepare,v=r.onLeavePrepare,g=r.onAppearStart,y=r.onEnterStart,b=r.onLeaveStart,x=r.onAppearActive,w=r.onEnterActive,S=r.onLeaveActive,C=r.onAppearEnd,k=r.onEnterEnd,E=r.onLeaveEnd,O=r.onVisibleChanged,_=ej(rO(),2),N=_[0],A=_[1],M=(o=ej(j.useReducer(function(e){return e+1},0),2)[1],a=j.useRef(r_),[rE(function(){return a.current}),rE(function(e){a.current="function"==typeof e?e(a.current):e,o()})]),P=ej(M,2),T=P[0],R=P[1],I=ej(rO(null),2),L=I[0],F=I[1],$=T(),Z=(0,j.useRef)(!1),H=(0,j.useRef)(null),D=(0,j.useRef)(!1);function B(){R(r_),F(null,!0)}var z=rE(function(e){var t,r=T();if(r!==r_){var o=n();if(!e||e.deadline||e.target===o){var a=D.current;r===rN&&a?t=null==C?void 0:C(o,e):r===rA&&a?t=null==k?void 0:k(o,e):r===rM&&a&&(t=null==E?void 0:E(o,e)),a&&!1!==t&&B()}}}),W=ej(rq(z),1)[0],U=function(e){switch(e){case rN:return eC(eC(eC({},rT,h),rR,g),rI,x);case rA:return eC(eC(eC({},rT,m),rR,y),rI,w);case rM:return eC(eC(eC({},rT,v),rR,b),rI,S);default:return{}}},G=j.useMemo(function(){return U($)},[$]),K=ej(r6($,!e,function(e){if(e===rT){var t,r=G[rT];return!!r&&r(n())}return X in G&&F((null==(t=G[X])?void 0:t.call(G,n(),null))||null),X===rI&&$!==r_&&(W(n()),f>0&&(clearTimeout(H.current),H.current=setTimeout(function(){z({deadline:!0})},f))),X===rL&&B(),!0}),2),q=K[0],X=K[1];D.current=r4(X);var V=(0,j.useRef)(null);rX(function(){if(!Z.current||V.current!==t){A(t);var n,r=Z.current;Z.current=!0,!r&&t&&c&&(n=rN),r&&t&&s&&(n=rA),(r&&!t&&d||!r&&p&&!t&&d)&&(n=rM);var o=U(n);n&&(e||o[rT])?(R(n),q()):R(r_),V.current=t}},[t]),(0,j.useEffect)(function(){($!==rN||c)&&($!==rA||s)&&($!==rM||d)||R(r_)},[c,s,d]),(0,j.useEffect)(function(){return function(){Z.current=!1,clearTimeout(H.current)}},[]);var Y=j.useRef(!1);(0,j.useEffect)(function(){N&&(Y.current=!0),void 0!==N&&$===r_&&((Y.current||N)&&(null==O||O(N)),Y.current=!0)},[N,$]);var Q=L;return G[rT]&&X===rR&&(Q=eE({transition:"none"},Q)),[$,X,Q,null!=N?N:t]}(h,o,function(){try{var e,t,n,r;return m.current instanceof HTMLElement?m.current:(r=(t=e=v.current)&&"object"===ew(t)&&rp(t.nativeElement)?t.nativeElement:rp(t)?t:null)?r:e instanceof j.Component?null==(n=ei.findDOMNode)?void 0:n.call(ei,e):null}catch(e){return null}},e),y=ej(g,4),b=y[0],x=y[1],w=y[2],S=y[3],C=j.useRef(S);S&&(C.current=!0);var k=j.useCallback(function(e){m.current=e,rb(t,e)},[t]),E=eE(eE({},f),{},{visible:o});if(l)if(b===r_)O=S?l(eE({},E),k):!i&&C.current&&u?l(eE(eE({},E),{},{className:u}),k):!s&&(i||u)?null:l(eE(eE({},E),{},{style:{display:"none"}}),k);else{x===rT?_="prepare":r4(x)?_="active":x===rR&&(_="start");var O,_,N=rK(c,"".concat(b,"-").concat(_));O=l(eE(eE({},E),{},{className:rf()(rK(c,b),eC(eC({},N,N&&_),c,"string"==typeof c)),style:w}),k)}else O=null;return j.isValidElement(O)&&rx(O)&&(((n=O)&&rj(n)?n.props.propertyIsEnumerable("ref")?n.props.ref:n.ref:null)||(O=j.cloneElement(O,{ref:k}))),j.createElement(rk,{ref:v},O)})).displayName="CSSMotion",f);var r8="keep",r7="remove",r9="removed";function oe(e){var t;return eE(eE({},t=e&&"object"===ew(e)&&"key"in e?e:{key:e}),{},{key:String(t.key)})}function ot(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return e.map(oe)}var on=["component","children","onVisibleChanged","onAllRemoved"],or=["status"],oo=["eventProps","visible","children","motionName","motionAppear","motionEnter","motionLeave","motionLeaveImmediately","motionDeadline","removeOnLeave","leavedClassName","onAppearPrepare","onAppearStart","onAppearActive","onAppearEnd","onEnterStart","onEnterActive","onEnterEnd","onLeaveStart","onLeaveActive","onLeaveEnd"];let oa=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:r3,n=function(e){e6(r,e);var n=e7(r);function r(){var e;eq(this,r);for(var t=arguments.length,o=Array(t),a=0;a<t;a++)o[a]=arguments[a];return eC(e5(e=n.call.apply(n,[this].concat(o))),"state",{keyEntities:[]}),eC(e5(e),"removeKey",function(t){e.setState(function(e){return{keyEntities:e.keyEntities.map(function(e){return e.key!==t?e:eE(eE({},e),{},{status:r9})})}},function(){0===e.state.keyEntities.filter(function(e){return e.status!==r9}).length&&e.props.onAllRemoved&&e.props.onAllRemoved()})}),e}return eV(r,[{key:"render",value:function(){var e=this,n=this.state.keyEntities,r=this.props,o=r.component,a=r.children,i=r.onVisibleChanged,s=(r.onAllRemoved,eZ(r,on)),l=o||j.Fragment,c={};return oo.forEach(function(e){c[e]=s[e],delete s[e]}),delete s.keys,j.createElement(l,s,n.map(function(n,r){var o=n.status,s=eZ(n,or);return j.createElement(t,tk({},c,{key:s.key,visible:"add"===o||o===r8,eventProps:s,onVisibleChanged:function(t){null==i||i(t,{key:s.key}),t||e.removeKey(s.key)}}),function(e,t){return a(eE(eE({},e),{},{index:r}),t)})}))}}],[{key:"getDerivedStateFromProps",value:function(e,t){var n=e.keys,r=t.keyEntities;return{keyEntities:(function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=[],r=0,o=t.length,a=ot(e),i=ot(t);a.forEach(function(e){for(var t=!1,a=r;a<o;a+=1){var s=i[a];if(s.key===e.key){r<a&&(n=n.concat(i.slice(r,a).map(function(e){return eE(eE({},e),{},{status:"add"})})),r=a),n.push(eE(eE({},s),{},{status:r8})),r+=1,t=!0;break}}t||n.push(eE(eE({},e),{},{status:r7}))}),r<o&&(n=n.concat(i.slice(r).map(function(e){return eE(eE({},e),{},{status:"add"})})));var s={};return n.forEach(function(e){var t=e.key;s[t]=(s[t]||0)+1}),Object.keys(s).filter(function(e){return s[e]>1}).forEach(function(e){(n=n.filter(function(t){var n=t.key,r=t.status;return n!==e||r!==r7})).forEach(function(t){t.key===e&&(t.status=r8)})}),n})(r,ot(n)).filter(function(e){var t=r.find(function(t){var n=t.key;return e.key===n});return!t||t.status!==r9||e.status!==r7})}}}]),r}(j.Component);return eC(n,"defaultProps",{component:"div"}),n}(rW);function oi(e){return e>=0&&e<=255}let os=function(e,t){let{r:n,g:r,b:o,a:a}=new nw(e).toRgb();if(a<1)return e;let{r:i,g:s,b:l}=new nw(t).toRgb();for(let e=.01;e<=1;e+=.01){let t=Math.round((n-i*(1-e))/e),a=Math.round((r-s*(1-e))/e),c=Math.round((o-l*(1-e))/e);if(oi(t)&&oi(a)&&oi(c))return new nw({r:t,g:a,b:c,a:Math.round(100*e)/100}).toRgbString()}return new nw({r:n,g:r,b:o,a:1}).toRgbString()};var ol=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};function oc(e){let{override:t}=e,n=ol(e,["override"]),r=Object.assign({},t);Object.keys(n5).forEach(e=>{delete r[e]});let o=Object.assign(Object.assign({},n),r);return!1===o.motion&&(o.motionDurationFast="0s",o.motionDurationMid="0s",o.motionDurationSlow="0s"),Object.assign(Object.assign(Object.assign({},o),{colorFillContent:o.colorFillSecondary,colorFillContentHover:o.colorFill,colorFillAlter:o.colorFillQuaternary,colorBgContainerDisabled:o.colorFillTertiary,colorBorderBg:o.colorBgContainer,colorSplit:os(o.colorBorderSecondary,o.colorBgContainer),colorTextPlaceholder:o.colorTextQuaternary,colorTextDisabled:o.colorTextQuaternary,colorTextHeading:o.colorText,colorTextLabel:o.colorTextSecondary,colorTextDescription:o.colorTextTertiary,colorTextLightSolid:o.colorWhite,colorHighlight:o.colorError,colorBgTextHover:o.colorFillSecondary,colorBgTextActive:o.colorFill,colorIcon:o.colorTextTertiary,colorIconHover:o.colorText,colorErrorOutline:os(o.colorErrorBg,o.colorBgContainer),colorWarningOutline:os(o.colorWarningBg,o.colorBgContainer),fontSizeIcon:o.fontSizeSM,lineWidthFocus:3*o.lineWidth,lineWidth:o.lineWidth,controlOutlineWidth:2*o.lineWidth,controlInteractiveSize:o.controlHeight/2,controlItemBgHover:o.colorFillTertiary,controlItemBgActive:o.colorPrimaryBg,controlItemBgActiveHover:o.colorPrimaryBgHover,controlItemBgActiveDisabled:o.colorFill,controlTmpOutline:o.colorFillQuaternary,controlOutline:os(o.colorPrimaryBg,o.colorBgContainer),lineType:o.lineType,borderRadius:o.borderRadius,borderRadiusXS:o.borderRadiusXS,borderRadiusSM:o.borderRadiusSM,borderRadiusLG:o.borderRadiusLG,fontWeightStrong:600,opacityLoading:.65,linkDecoration:"none",linkHoverDecoration:"none",linkFocusDecoration:"none",controlPaddingHorizontal:12,controlPaddingHorizontalSM:8,paddingXXS:o.sizeXXS,paddingXS:o.sizeXS,paddingSM:o.sizeSM,padding:o.size,paddingMD:o.sizeMD,paddingLG:o.sizeLG,paddingXL:o.sizeXL,paddingContentHorizontalLG:o.sizeLG,paddingContentVerticalLG:o.sizeMS,paddingContentHorizontal:o.sizeMS,paddingContentVertical:o.sizeSM,paddingContentHorizontalSM:o.size,paddingContentVerticalSM:o.sizeXS,marginXXS:o.sizeXXS,marginXS:o.sizeXS,marginSM:o.sizeSM,margin:o.size,marginMD:o.sizeMD,marginLG:o.sizeLG,marginXL:o.sizeXL,marginXXL:o.sizeXXL,boxShadow:`
      0 6px 16px 0 rgba(0, 0, 0, 0.08),
      0 3px 6px -4px rgba(0, 0, 0, 0.12),
      0 9px 28px 8px rgba(0, 0, 0, 0.05)
    `,boxShadowSecondary:`
      0 6px 16px 0 rgba(0, 0, 0, 0.08),
      0 3px 6px -4px rgba(0, 0, 0, 0.12),
      0 9px 28px 8px rgba(0, 0, 0, 0.05)
    `,boxShadowTertiary:`
      0 1px 2px 0 rgba(0, 0, 0, 0.03),
      0 1px 6px -1px rgba(0, 0, 0, 0.02),
      0 2px 4px 0 rgba(0, 0, 0, 0.02)
    `,screenXS:480,screenXSMin:480,screenXSMax:575,screenSM:576,screenSMMin:576,screenSMMax:767,screenMD:768,screenMDMin:768,screenMDMax:991,screenLG:992,screenLGMin:992,screenLGMax:1199,screenXL:1200,screenXLMin:1200,screenXLMax:1599,screenXXL:1600,screenXXLMin:1600,boxShadowPopoverArrow:"2px 2px 5px rgba(0, 0, 0, 0.05)",boxShadowCard:`
      0 1px 2px -2px ${new nw("rgba(0, 0, 0, 0.16)").toRgbString()},
      0 3px 6px 0 ${new nw("rgba(0, 0, 0, 0.12)").toRgbString()},
      0 5px 12px 4px ${new nw("rgba(0, 0, 0, 0.09)").toRgbString()}
    `,boxShadowDrawerRight:`
      -6px 0 16px 0 rgba(0, 0, 0, 0.08),
      -3px 0 6px -4px rgba(0, 0, 0, 0.12),
      -9px 0 28px 8px rgba(0, 0, 0, 0.05)
    `,boxShadowDrawerLeft:`
      6px 0 16px 0 rgba(0, 0, 0, 0.08),
      3px 0 6px -4px rgba(0, 0, 0, 0.12),
      9px 0 28px 8px rgba(0, 0, 0, 0.05)
    `,boxShadowDrawerUp:`
      0 6px 16px 0 rgba(0, 0, 0, 0.08),
      0 3px 6px -4px rgba(0, 0, 0, 0.12),
      0 9px 28px 8px rgba(0, 0, 0, 0.05)
    `,boxShadowDrawerDown:`
      0 -6px 16px 0 rgba(0, 0, 0, 0.08),
      0 -3px 6px -4px rgba(0, 0, 0, 0.12),
      0 -9px 28px 8px rgba(0, 0, 0, 0.05)
    `,boxShadowTabsOverflowLeft:"inset 10px 0 8px -8px rgba(0, 0, 0, 0.08)",boxShadowTabsOverflowRight:"inset -10px 0 8px -8px rgba(0, 0, 0, 0.08)",boxShadowTabsOverflowTop:"inset 0 10px 8px -8px rgba(0, 0, 0, 0.08)",boxShadowTabsOverflowBottom:"inset 0 -10px 8px -8px rgba(0, 0, 0, 0.08)"}),r)}var ou=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};let od={lineHeight:!0,lineHeightSM:!0,lineHeightLG:!0,lineHeightHeading1:!0,lineHeightHeading2:!0,lineHeightHeading3:!0,lineHeightHeading4:!0,lineHeightHeading5:!0,opacityLoading:!0,fontWeightStrong:!0,zIndexPopupBase:!0,zIndexBase:!0,opacityImage:!0},of={size:!0,sizeSM:!0,sizeLG:!0,sizeMD:!0,sizeXS:!0,sizeXXS:!0,sizeMS:!0,sizeXL:!0,sizeXXL:!0,sizeUnit:!0,sizeStep:!0,motionBase:!0,motionUnit:!0},op={screenXS:!0,screenXSMin:!0,screenXSMax:!0,screenSM:!0,screenSMMin:!0,screenSMMax:!0,screenMD:!0,screenMDMin:!0,screenMDMax:!0,screenLG:!0,screenLGMin:!0,screenLGMax:!0,screenXL:!0,screenXLMin:!0,screenXLMax:!0,screenXXL:!0,screenXXLMin:!0},oh=(e,t,n)=>{let r=n.getDerivativeToken(e),{override:o}=t,a=ou(t,["override"]),i=Object.assign(Object.assign({},r),{override:o});return i=oc(i),a&&Object.entries(a).forEach(e=>{let[t,n]=e,{theme:r}=n,o=ou(n,["theme"]),a=o;r&&(a=oh(Object.assign(Object.assign({},i),o),{override:o},r)),i[t]=a}),i};function om(){let{token:e,hashed:t,theme:n,override:r,cssVar:o}=j.useContext(rr),a=n||rt,[i,s,l]=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=(0,j.useContext)(e2),o=r.cache.instanceId,a=r.container,i=n.salt,s=void 0===i?"":i,l=n.override,c=void 0===l?tj:l,u=n.formatToken,d=n.getComputedToken,f=n.cssVar,p=function(e,t){for(var n=to,r=0;r<t.length;r+=1){var o=t[r];n.has(o)||n.set(o,new WeakMap),n=n.get(o)}return n.has(ta)||n.set(ta,e()),n.get(ta)}(function(){return Object.assign.apply(Object,[{}].concat(eh(t)))},t),h=ts(p),m=ts(c),v=f?ts(f):"";return tx(tC,[s,e.id,h,m,v],function(){var t,n=d?d(p,c,e):tS(p,c,e,u),r=eE({},n),o="";if(f){var a=ej(tp(n,f.key,{prefix:f.prefix,ignore:f.ignore,unitless:f.unitless,preserve:f.preserve}),2);n=a[0],o=a[1]}var i=tl(n,s);n._tokenKey=i,r._tokenKey=tl(r,s);var l=null!=(t=null==f?void 0:f.key)?t:i;n._themeKey=l,tw.set(l,(tw.get(l)||0)+1);var h="".concat("css","-").concat(eO(i));return n._hashId=h,[n,h,r,o,(null==f?void 0:f.key)||""]},function(e){var t,n,r;t=e[0]._themeKey,tw.set(t,(tw.get(t)||0)-1),r=(n=Array.from(tw.keys())).filter(function(e){return 0>=(tw.get(e)||0)}),n.length-r.length>0&&r.forEach(function(e){"undefined"!=typeof document&&document.querySelectorAll("style[".concat(eJ,'="').concat(e,'"]')).forEach(function(e){if(e[e1]===o){var t;null==(t=e.parentNode)||t.removeChild(e)}}),tw.delete(e)})},function(e){var t=ej(e,4),n=t[0],r=t[3];if(f&&r){var i=e$(r,eO("css-variables-".concat(n._themeKey)),{mark:e0,prepend:"queue",attachTo:a,priority:-999});i[e1]=o,i.setAttribute(eJ,n._themeKey)}})}(a,[n5,e],{salt:`5.24.7-${t||""}`,override:r,getComputedToken:oh,formatToken:oc,cssVar:o&&{prefix:o.prefix,key:o.key,unitless:od,ignore:of,preserve:op}});return[a,l,t?s:"",i,o]}function ov(e){let{children:t}=e,[,n]=om(),{motion:r}=n,o=j.useRef(!1);return(o.current=o.current||!1===r,o.current)?j.createElement(rC,{motion:r},t):t}let og=()=>null,oy=function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return{boxSizing:"border-box",margin:0,padding:0,color:e.colorText,fontSize:e.fontSize,lineHeight:e.lineHeight,listStyle:"none",fontFamily:t?"inherit":e.fontFamily}},ob=()=>({display:"inline-flex",alignItems:"center",color:"inherit",fontStyle:"normal",lineHeight:0,textAlign:"center",textTransform:"none",verticalAlign:"-0.125em",textRendering:"optimizeLegibility","-webkit-font-smoothing":"antialiased","-moz-osx-font-smoothing":"grayscale","> *":{lineHeight:1},svg:{display:"inline-block"}}),ox=e=>({a:{color:e.colorLink,textDecoration:e.linkDecoration,backgroundColor:"transparent",outline:"none",cursor:"pointer",transition:`color ${e.motionDurationSlow}`,"-webkit-text-decoration-skip":"objects","&:hover":{color:e.colorLinkHover},"&:active":{color:e.colorLinkActive},"&:active, &:hover":{textDecoration:e.linkHoverDecoration,outline:0},"&:focus":{textDecoration:e.linkFocusDecoration,outline:0},"&[disabled]":{color:e.colorTextDisabled,cursor:"not-allowed"}}}),oj=e=>({[`.${e}`]:Object.assign(Object.assign({},ob()),{[`.${e} .${e}-icon`]:{display:"block"}})}),ow=(e,t)=>{let[n,r]=om();return t7({theme:n,token:r,hashId:"",path:["ant-design-icons",e],nonce:()=>null==t?void 0:t.nonce,layer:{name:"antd"}},()=>[oj(e)])};var oS=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};let oC=["getTargetContainer","getPopupContainer","renderEmpty","input","pagination","form","select","button"];function ok(){return r||"ant"}function oE(){return o||ev}let oO=()=>({getPrefixCls:(e,t)=>t||(e?`${ok()}-${e}`:ok()),getIconPrefixCls:oE,getRootPrefixCls:()=>r||ok(),getTheme:()=>a,holderRender:i}),o_=e=>{let{children:t,csp:n,autoInsertSpaceInButton:r,alert:o,anchor:a,form:i,locale:s,componentSize:l,direction:c,space:u,splitter:d,virtual:f,dropdownMatchSelectWidth:p,popupMatchSelectWidth:h,popupOverflow:m,legacyLocale:v,parentContext:g,iconPrefixCls:y,theme:b,componentDisabled:x,segmented:w,statistic:S,spin:C,calendar:k,carousel:E,cascader:O,collapse:_,typography:N,checkbox:A,descriptions:M,divider:P,drawer:T,skeleton:R,steps:I,image:L,layout:F,list:$,mentions:Z,modal:H,progress:D,result:B,slider:z,breadcrumb:W,menu:U,pagination:G,input:K,textArea:q,empty:X,badge:V,radio:Y,rate:Q,switch:J,transfer:ee,avatar:et,message:en,tag:er,table:eo,card:ea,tabs:ei,timeline:es,timePicker:el,upload:ec,notification:eu,tree:ed,colorPicker:ef,datePicker:ep,rangePicker:em,flex:ey,wave:eb,dropdown:ex,warning:ej,tour:eS,tooltip:eC,popover:ek,popconfirm:eE,floatButtonGroup:eO,variant:e_,inputNumber:eN,treeSelect:eA}=e,eM=j.useCallback((t,n)=>{let{prefixCls:r}=e;if(n)return n;let o=r||g.getPrefixCls("");return t?`${o}-${t}`:o},[g.getPrefixCls,e.prefixCls]),eP=y||g.iconPrefixCls||ev,eT=n||g.csp;ow(eP,eT);let eR=function(e,t,n){var r;nu("ConfigProvider");let o=e||{},a=!1!==o.inherit&&t?t:Object.assign(Object.assign({},rn),{hashed:null!=(r=null==t?void 0:t.hashed)?r:rn.hashed,cssVar:null==t?void 0:t.cssVar}),i=ru();return eH(()=>{var r,s;if(!e)return t;let l=Object.assign({},a.components);Object.keys(e.components||{}).forEach(t=>{l[t]=Object.assign(Object.assign({},l[t]),e.components[t])});let c=`css-var-${i.replace(/:/g,"")}`,u=(null!=(r=o.cssVar)?r:a.cssVar)&&Object.assign(Object.assign(Object.assign({prefix:null==n?void 0:n.prefixCls},"object"==typeof a.cssVar?a.cssVar:{}),"object"==typeof o.cssVar?o.cssVar:{}),{key:"object"==typeof o.cssVar&&(null==(s=o.cssVar)?void 0:s.key)||c});return Object.assign(Object.assign(Object.assign({},a),o),{token:Object.assign(Object.assign({},a.token),o.token),components:l,cssVar:u})},[o,a],(e,t)=>e.some((e,n)=>!eK(e,t[n],!0)))}(b,g.theme,{prefixCls:eM("")}),eI={csp:eT,autoInsertSpaceInButton:r,alert:o,anchor:a,locale:s||v,direction:c,space:u,splitter:d,virtual:f,popupMatchSelectWidth:null!=h?h:p,popupOverflow:m,getPrefixCls:eM,iconPrefixCls:eP,theme:eR,segmented:w,statistic:S,spin:C,calendar:k,carousel:E,cascader:O,collapse:_,typography:N,checkbox:A,descriptions:M,divider:P,drawer:T,skeleton:R,steps:I,image:L,input:K,textArea:q,layout:F,list:$,mentions:Z,modal:H,progress:D,result:B,slider:z,breadcrumb:W,menu:U,pagination:G,empty:X,badge:V,radio:Y,rate:Q,switch:J,transfer:ee,avatar:et,message:en,tag:er,table:eo,card:ea,tabs:ei,timeline:es,timePicker:el,upload:ec,notification:eu,tree:ed,colorPicker:ef,datePicker:ep,rangePicker:em,flex:ey,wave:eb,dropdown:ex,warning:ej,tour:eS,tooltip:eC,popover:ek,popconfirm:eE,floatButtonGroup:eO,variant:e_,inputNumber:eN,treeSelect:eA},eL=Object.assign({},g);Object.keys(eI).forEach(e=>{void 0!==eI[e]&&(eL[e]=eI[e])}),oC.forEach(t=>{let n=e[t];n&&(eL[t]=n)}),void 0!==r&&(eL.button=Object.assign({autoInsertSpace:r},eL.button));let eF=eH(()=>eL,eL,(e,t)=>{let n=Object.keys(e),r=Object.keys(t);return n.length!==r.length||n.some(n=>e[n]!==t[n])}),{layer:e$}=j.useContext(e2),eZ=j.useMemo(()=>({prefixCls:eP,csp:eT,layer:e$?"antd":void 0}),[eP,eT,e$]),eD=j.createElement(j.Fragment,null,j.createElement(og,{dropdownMatchSelectWidth:p}),t),eB=j.useMemo(()=>{var e,t,n,r;return function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];var r=ni(t[0]);return t.forEach(function(e){!function t(n,o){var a=new Set(o),i=no(e,n),s=Array.isArray(i);if(s||"object"===ew(i)&&null!==i&&Object.getPrototypeOf(i)===Object.prototype){if(!a.has(i)){a.add(i);var l=no(r,n);s?r=na(r,n,[]):l&&"object"===ew(l)||(r=na(r,n,ni(i))),ns(i).forEach(function(e){t([].concat(eh(n),[e]),a)})}}else r=na(r,n,i)}([])}),r}((null==(e=np.Form)?void 0:e.defaultValidateMessages)||{},(null==(n=null==(t=eF.locale)?void 0:t.Form)?void 0:n.defaultValidateMessages)||{},(null==(r=eF.form)?void 0:r.validateMessages)||{},(null==i?void 0:i.validateMessages)||{})},[eF,null==i?void 0:i.validateMessages]);Object.keys(eB).length>0&&(eD=j.createElement(nd.Provider,{value:eB},eD)),s&&(eD=j.createElement(ng,{locale:s,_ANT_MARK__:"internalMark"},eD)),(eP||eT)&&(eD=j.createElement(nr.Provider,{value:eZ},eD)),l&&(eD=j.createElement(rl,{size:l},eD)),eD=j.createElement(ov,null,eD);let ez=j.useMemo(()=>{let e=eR||{},{algorithm:t,token:n,components:r,cssVar:o}=e,a=oS(e,["algorithm","token","components","cssVar"]),i=t&&(!Array.isArray(t)||t.length>0)?tr(t):rt,s={};Object.entries(r||{}).forEach(e=>{let[t,n]=e,r=Object.assign({},n);"algorithm"in r&&(!0===r.algorithm?r.theme=i:(Array.isArray(r.algorithm)||"function"==typeof r.algorithm)&&(r.theme=tr(r.algorithm)),delete r.algorithm),s[t]=r});let l=Object.assign(Object.assign({},n5),n);return Object.assign(Object.assign({},a),{theme:i,token:l,components:s,override:Object.assign({override:l},s),cssVar:o})},[eR]);return b&&(eD=j.createElement(rr.Provider,{value:ez},eD)),eF.warning&&(eD=j.createElement(nc.Provider,{value:eF.warning},eD)),void 0!==x&&(eD=j.createElement(ri,{disabled:x},eD)),j.createElement(eg.Provider,{value:eF},eD)},oN=e=>{let t=j.useContext(eg),n=j.useContext(nv);return j.createElement(o_,Object.assign({parentContext:t,legacyLocale:n},e))};function oA(){oA=function(){return t};var e,t={},n=Object.prototype,r=n.hasOwnProperty,o=Object.defineProperty||function(e,t,n){e[t]=n.value},a="function"==typeof Symbol?Symbol:{},i=a.iterator||"@@iterator",s=a.asyncIterator||"@@asyncIterator",l=a.toStringTag||"@@toStringTag";function c(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{c({},"")}catch(e){c=function(e,t,n){return e[t]=n}}function u(t,n,r,a){var i,s,l,c,u=Object.create((n&&n.prototype instanceof v?n:v).prototype);return o(u,"_invoke",{value:(i=t,s=r,l=new O(a||[]),c=f,function(t,n){if(c===p)throw Error("Generator is already running");if(c===h){if("throw"===t)throw n;return{value:e,done:!0}}for(l.method=t,l.arg=n;;){var r=l.delegate;if(r){var o=function t(n,r){var o=r.method,a=n.iterator[o];if(a===e)return r.delegate=null,"throw"===o&&n.iterator.return&&(r.method="return",r.arg=e,t(n,r),"throw"===r.method)||"return"!==o&&(r.method="throw",r.arg=TypeError("The iterator does not provide a '"+o+"' method")),m;var i=d(a,n.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,m;var s=i.arg;return s?s.done?(r[n.resultName]=s.value,r.next=n.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,m):s:(r.method="throw",r.arg=TypeError("iterator result is not an object"),r.delegate=null,m)}(r,l);if(o){if(o===m)continue;return o}}if("next"===l.method)l.sent=l._sent=l.arg;else if("throw"===l.method){if(c===f)throw c=h,l.arg;l.dispatchException(l.arg)}else"return"===l.method&&l.abrupt("return",l.arg);c=p;var a=d(i,s,l);if("normal"===a.type){if(c=l.done?h:"suspendedYield",a.arg===m)continue;return{value:a.arg,done:l.done}}"throw"===a.type&&(c=h,l.method="throw",l.arg=a.arg)}})}),u}function d(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}t.wrap=u;var f="suspendedStart",p="executing",h="completed",m={};function v(){}function g(){}function y(){}var b={};c(b,i,function(){return this});var x=Object.getPrototypeOf,j=x&&x(x(_([])));j&&j!==n&&r.call(j,i)&&(b=j);var w=y.prototype=v.prototype=Object.create(b);function S(e){["next","throw","return"].forEach(function(t){c(e,t,function(e){return this._invoke(t,e)})})}function C(e,t){var n;o(this,"_invoke",{value:function(o,a){function i(){return new t(function(n,i){!function n(o,a,i,s){var l=d(e[o],e,a);if("throw"!==l.type){var c=l.arg,u=c.value;return u&&"object"==ew(u)&&r.call(u,"__await")?t.resolve(u.__await).then(function(e){n("next",e,i,s)},function(e){n("throw",e,i,s)}):t.resolve(u).then(function(e){c.value=e,i(c)},function(e){return n("throw",e,i,s)})}s(l.arg)}(o,a,n,i)})}return n=n?n.then(i,i):i()}})}function k(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function E(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function O(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(k,this),this.reset(!0)}function _(t){if(t||""===t){var n=t[i];if(n)return n.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var o=-1,a=function n(){for(;++o<t.length;)if(r.call(t,o))return n.value=t[o],n.done=!1,n;return n.value=e,n.done=!0,n};return a.next=a}}throw TypeError(ew(t)+" is not iterable")}return g.prototype=y,o(w,"constructor",{value:y,configurable:!0}),o(y,"constructor",{value:g,configurable:!0}),g.displayName=c(y,l,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===g||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,y):(e.__proto__=y,c(e,l,"GeneratorFunction")),e.prototype=Object.create(w),e},t.awrap=function(e){return{__await:e}},S(C.prototype),c(C.prototype,s,function(){return this}),t.AsyncIterator=C,t.async=function(e,n,r,o,a){void 0===a&&(a=Promise);var i=new C(u(e,n,r,o),a);return t.isGeneratorFunction(n)?i:i.next().then(function(e){return e.done?e.value:i.next()})},S(w),c(w,l,"Generator"),c(w,i,function(){return this}),c(w,"toString",function(){return"[object Generator]"}),t.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},t.values=_,O.prototype={constructor:O,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(E),!t)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var n=this;function o(r,o){return s.type="throw",s.arg=t,n.next=r,o&&(n.method="next",n.arg=e),!!o}for(var a=this.tryEntries.length-1;a>=0;--a){var i=this.tryEntries[a],s=i.completion;if("root"===i.tryLoc)return o("end");if(i.tryLoc<=this.prev){var l=r.call(i,"catchLoc"),c=r.call(i,"finallyLoc");if(l&&c){if(this.prev<i.catchLoc)return o(i.catchLoc,!0);if(this.prev<i.finallyLoc)return o(i.finallyLoc)}else if(l){if(this.prev<i.catchLoc)return o(i.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return o(i.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var a=o;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=t&&t<=a.finallyLoc&&(a=null);var i=a?a.completion:{};return i.type=e,i.arg=t,a?(this.method="next",this.next=a.finallyLoc,m):this.complete(i)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),m},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),E(n),m}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var o=r.arg;E(n)}return o}}throw Error("illegal catch attempt")},delegateYield:function(t,n,r){return this.delegate={iterator:_(t),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=e),m}},t}function oM(e,t,n,r,o,a,i){try{var s=e[a](i),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,o)}function oP(e){return function(){var t=this,n=arguments;return new Promise(function(r,o){var a=e.apply(t,n);function i(e){oM(a,r,o,i,s,"next",e)}function s(e){oM(a,r,o,i,s,"throw",e)}i(void 0)})}}oN.ConfigContext=eg,oN.SizeContext=rs,oN.config=e=>{let{prefixCls:t,iconPrefixCls:n,theme:s,holderRender:l}=e;if(void 0!==t&&(r=t),void 0!==n&&(o=n),"holderRender"in e&&(i=l),s)if(Object.keys(s).some(e=>e.endsWith("Color"))){let e=function(e,t){let n={},r=(e,t)=>{let n=e.clone();return(n=(null==t?void 0:t(n))||n).toRgbString()},o=(e,t)=>{let o=new nw(e),a=nO(o.toRgbString());n[`${t}-color`]=r(o),n[`${t}-color-disabled`]=a[1],n[`${t}-color-hover`]=a[4],n[`${t}-color-active`]=a[6],n[`${t}-color-outline`]=o.clone().setA(.2).toRgbString(),n[`${t}-color-deprecated-bg`]=a[0],n[`${t}-color-deprecated-border`]=a[2]};if(t.primaryColor){o(t.primaryColor,"primary");let e=new nw(t.primaryColor),a=nO(e.toRgbString());a.forEach((e,t)=>{n[`primary-${t+1}`]=e}),n["primary-color-deprecated-l-35"]=r(e,e=>e.lighten(35)),n["primary-color-deprecated-l-20"]=r(e,e=>e.lighten(20)),n["primary-color-deprecated-t-20"]=r(e,e=>e.tint(20)),n["primary-color-deprecated-t-50"]=r(e,e=>e.tint(50)),n["primary-color-deprecated-f-12"]=r(e,e=>e.setA(.12*e.a));let i=new nw(a[0]);n["primary-color-active-deprecated-f-30"]=r(i,e=>e.setA(.3*e.a)),n["primary-color-active-deprecated-d-02"]=r(i,e=>e.darken(2))}t.successColor&&o(t.successColor,"success"),t.warningColor&&o(t.warningColor,"warning"),t.errorColor&&o(t.errorColor,"error"),t.infoColor&&o(t.infoColor,"info");let a=Object.keys(n).map(t=>`--${e}-${t}: ${n[t]};`);return`
  :root {
    ${a.join("\n")}
  }
  `.trim()}(ok(),s);e_()&&e$(e,`${ro}-dynamic-theme`)}else a=s},oN.useConfig=function(){return{componentDisabled:(0,j.useContext)(ra),componentSize:(0,j.useContext)(rs)}},Object.defineProperty(oN,"SizeContext",{get:()=>rs});var oT=eE({},es),oR=oT.version,oI=oT.render,oL=oT.unmountComponentAtNode;try{Number((oR||"").split(".")[0])>=18&&(y=oT.createRoot)}catch(e){}function oF(e){var t=oT.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;t&&"object"===ew(t)&&(t.usingClientEntryPoint=e)}var o$="__rc_react_root__";function oZ(){return(oZ=oP(oA().mark(function e(t){return oA().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",Promise.resolve().then(function(){var e;null==(e=t[o$])||e.unmount(),delete t[o$]}));case 1:case"end":return e.stop()}},e)}))).apply(this,arguments)}function oH(){return(oH=oP(oA().mark(function e(t){return oA().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(void 0===y){e.next=2;break}return e.abrupt("return",function(e){return oZ.apply(this,arguments)}(t));case 2:oL(t);case 3:case"end":return e.stop()}},e)}))).apply(this,arguments)}let oD=(e,t)=>(!function(e,t){var n;if(y)return oF(!0),n=t[o$]||y(t),oF(!1),n.render(e),t[o$]=n;null==oI||oI(e,t)}(e,t),()=>(function(e){return oH.apply(this,arguments)})(t)),oB={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm193.5 301.7l-210.6 292a31.8 31.8 0 01-51.7 0L318.5 484.9c-3.8-5.3 0-12.7 6.5-12.7h46.9c10.2 0 19.9 4.9 25.9 13.3l71.2 98.8 157.2-218c6-8.3 15.6-13.3 25.9-13.3H699c6.5 0 10.3 7.4 6.5 12.7z"}}]},name:"check-circle",theme:"filled"};function oz(e){var t;return null==e||null==(t=e.getRootNode)?void 0:t.call(e)}function oW(e){return"object"===ew(e)&&"string"==typeof e.name&&"string"==typeof e.theme&&("object"===ew(e.icon)||"function"==typeof e.icon)}function oU(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return Object.keys(e).reduce(function(t,n){var r=e[n];return"class"===n?(t.className=r,delete t.class):(delete t[n],t[n.replace(/-(.)/g,function(e,t){return t.toUpperCase()})]=r),t},{})}function oG(e){return e?Array.isArray(e)?e:[e]:[]}var oK=function(e){var t=(0,j.useContext)(nr),n=t.csp,r=t.prefixCls,o=t.layer,a="\n.anticon {\n  display: inline-flex;\n  align-items: center;\n  color: inherit;\n  font-style: normal;\n  line-height: 0;\n  text-align: center;\n  text-transform: none;\n  vertical-align: -0.125em;\n  text-rendering: optimizeLegibility;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n\n.anticon > * {\n  line-height: 1;\n}\n\n.anticon svg {\n  display: inline-block;\n}\n\n.anticon::before {\n  display: none;\n}\n\n.anticon .anticon-icon {\n  display: block;\n}\n\n.anticon[tabindex] {\n  cursor: pointer;\n}\n\n.anticon-spin::before,\n.anticon-spin {\n  display: inline-block;\n  -webkit-animation: loadingCircle 1s infinite linear;\n  animation: loadingCircle 1s infinite linear;\n}\n\n@-webkit-keyframes loadingCircle {\n  100% {\n    -webkit-transform: rotate(360deg);\n    transform: rotate(360deg);\n  }\n}\n\n@keyframes loadingCircle {\n  100% {\n    -webkit-transform: rotate(360deg);\n    transform: rotate(360deg);\n  }\n}\n";r&&(a=a.replace(/anticon/g,r)),o&&(a="@layer ".concat(o," {\n").concat(a,"\n}")),(0,j.useEffect)(function(){var t,r=oz(t=e.current)instanceof ShadowRoot?oz(t):null;e$(a,"@ant-design-icons",{prepend:!o,csp:n,attachTo:r})},[])},oq=["icon","className","onClick","style","primaryColor","secondaryColor"],oX={primaryColor:"#333",secondaryColor:"#E6E6E6",calculated:!1},oV=function(e){var t,n,r=e.icon,o=e.className,a=e.onClick,i=e.style,s=e.primaryColor,l=e.secondaryColor,c=eZ(e,oq),u=j.useRef(),d=oX;if(s&&(d={primaryColor:s,secondaryColor:l||nO(s)[0]}),oK(u),t=oW(r),n="icon should be icon definiton, but got ".concat(r),eG(t,"[@ant-design/icons] ".concat(n)),!oW(r))return null;var f=r;return f&&"function"==typeof f.icon&&(f=eE(eE({},f),{},{icon:f.icon(d.primaryColor,d.secondaryColor)})),function e(t,n,r){return r?j.createElement(t.tag,eE(eE({key:n},oU(t.attrs)),r),(t.children||[]).map(function(r,o){return e(r,"".concat(n,"-").concat(t.tag,"-").concat(o))})):j.createElement(t.tag,eE({key:n},oU(t.attrs)),(t.children||[]).map(function(r,o){return e(r,"".concat(n,"-").concat(t.tag,"-").concat(o))}))}(f.icon,"svg-".concat(f.name),eE(eE({className:o,onClick:a,style:i,"data-icon":f.name,width:"1em",height:"1em",fill:"currentColor","aria-hidden":"true"},c),{},{ref:u}))};function oY(e){var t=ej(oG(e),2),n=t[0],r=t[1];return oV.setTwoToneColors({primaryColor:n,secondaryColor:r})}oV.displayName="IconReact",oV.getTwoToneColors=function(){return eE({},oX)},oV.setTwoToneColors=function(e){var t=e.primaryColor,n=e.secondaryColor;oX.primaryColor=t,oX.secondaryColor=n||nO(t)[0],oX.calculated=!!n};var oQ=["className","icon","spin","rotate","tabIndex","onClick","twoToneColor"];oY(nF.primary);var oJ=j.forwardRef(function(e,t){var n=e.className,r=e.icon,o=e.spin,a=e.rotate,i=e.tabIndex,s=e.onClick,l=e.twoToneColor,c=eZ(e,oQ),u=j.useContext(nr),d=u.prefixCls,f=void 0===d?"anticon":d,p=u.rootClassName,h=rf()(p,f,eC(eC({},"".concat(f,"-").concat(r.name),!!r.name),"".concat(f,"-spin"),!!o||"loading"===r.name),n),m=i;void 0===m&&s&&(m=-1);var v=ej(oG(l),2),g=v[0],y=v[1];return j.createElement("span",tk({role:"img","aria-label":r.name},c,{ref:t,tabIndex:m,onClick:s,className:h}),j.createElement(oV,{icon:r,primaryColor:g,secondaryColor:y,style:a?{msTransform:"rotate(".concat(a,"deg)"),transform:"rotate(".concat(a,"deg)")}:void 0}))});oJ.displayName="AntdIcon",oJ.getTwoToneColor=function(){var e=oV.getTwoToneColors();return e.calculated?[e.primaryColor,e.secondaryColor]:e.primaryColor},oJ.setTwoToneColor=oY;var o0=j.forwardRef(function(e,t){return j.createElement(oJ,tk({},e,{ref:t,icon:oB}))});let o1={icon:{tag:"svg",attrs:{"fill-rule":"evenodd",viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64c247.4 0 448 200.6 448 448S759.4 960 512 960 64 759.4 64 512 264.6 64 512 64zm127.98 274.82h-.04l-.08.06L512 466.75 384.14 338.88c-.04-.05-.06-.06-.08-.06a.12.12 0 00-.07 0c-.03 0-.05.01-.09.05l-45.02 45.02a.2.2 0 00-.05.09.12.12 0 000 .07v.02a.27.27 0 00.06.06L466.75 512 338.88 639.86c-.05.04-.06.06-.06.08a.12.12 0 000 .07c0 .03.01.05.05.09l45.02 45.02a.2.2 0 00.09.05.12.12 0 00.07 0c.02 0 .04-.01.08-.05L512 557.25l127.86 127.87c.04.04.06.05.08.05a.12.12 0 00.07 0c.03 0 .05-.01.09-.05l45.02-45.02a.2.2 0 00.05-.09.12.12 0 000-.07v-.02a.27.27 0 00-.05-.06L557.25 512l127.87-127.86c.04-.04.05-.06.05-.08a.12.12 0 000-.07c0-.03-.01-.05-.05-.09l-45.02-45.02a.2.2 0 00-.09-.05.12.12 0 00-.07 0z"}}]},name:"close-circle",theme:"filled"};var o2=j.forwardRef(function(e,t){return j.createElement(oJ,tk({},e,{ref:t,icon:o1}))});let o5={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm-32 232c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v272c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V296zm32 440a48.01 48.01 0 010-96 48.01 48.01 0 010 96z"}}]},name:"exclamation-circle",theme:"filled"};var o4=j.forwardRef(function(e,t){return j.createElement(oJ,tk({},e,{ref:t,icon:o5}))});let o6={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm32 664c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V456c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v272zm-32-344a48.01 48.01 0 010-96 48.01 48.01 0 010 96z"}}]},name:"info-circle",theme:"filled"};var o3=j.forwardRef(function(e,t){return j.createElement(oJ,tk({},e,{ref:t,icon:o6}))});let o8={icon:{tag:"svg",attrs:{viewBox:"0 0 1024 1024",focusable:"false"},children:[{tag:"path",attrs:{d:"M988 548c-19.9 0-36-16.1-36-36 0-59.4-11.6-117-34.6-171.3a440.45 440.45 0 00-94.3-139.9 437.71 437.71 0 00-139.9-94.3C629 83.6 571.4 72 512 72c-19.9 0-36-16.1-36-36s16.1-36 36-36c69.1 0 136.2 13.5 199.3 40.3C772.3 66 827 103 874 150c47 47 83.9 101.8 109.7 162.7 26.7 63.1 40.2 130.2 40.2 199.3.1 19.9-16 36-35.9 36z"}}]},name:"loading",theme:"outlined"};var o7=j.forwardRef(function(e,t){return j.createElement(oJ,tk({},e,{ref:t,icon:o8}))}),o9={MAC_ENTER:3,BACKSPACE:8,TAB:9,NUM_CENTER:12,ENTER:13,SHIFT:16,CTRL:17,ALT:18,PAUSE:19,CAPS_LOCK:20,ESC:27,SPACE:32,PAGE_UP:33,PAGE_DOWN:34,END:35,HOME:36,LEFT:37,UP:38,RIGHT:39,DOWN:40,PRINT_SCREEN:44,INSERT:45,DELETE:46,ZERO:48,ONE:49,TWO:50,THREE:51,FOUR:52,FIVE:53,SIX:54,SEVEN:55,EIGHT:56,NINE:57,QUESTION_MARK:63,A:65,B:66,C:67,D:68,E:69,F:70,G:71,H:72,I:73,J:74,K:75,L:76,M:77,N:78,O:79,P:80,Q:81,R:82,S:83,T:84,U:85,V:86,W:87,X:88,Y:89,Z:90,META:91,WIN_KEY_RIGHT:92,CONTEXT_MENU:93,NUM_ZERO:96,NUM_ONE:97,NUM_TWO:98,NUM_THREE:99,NUM_FOUR:100,NUM_FIVE:101,NUM_SIX:102,NUM_SEVEN:103,NUM_EIGHT:104,NUM_NINE:105,NUM_MULTIPLY:106,NUM_PLUS:107,NUM_MINUS:109,NUM_PERIOD:110,NUM_DIVISION:111,F1:112,F2:113,F3:114,F4:115,F5:116,F6:117,F7:118,F8:119,F9:120,F10:121,F11:122,F12:123,NUMLOCK:144,SEMICOLON:186,DASH:189,EQUALS:187,COMMA:188,PERIOD:190,SLASH:191,APOSTROPHE:192,SINGLE_QUOTE:222,OPEN_SQUARE_BRACKET:219,BACKSLASH:220,CLOSE_SQUARE_BRACKET:221,WIN_KEY:224,MAC_FF_META:224,WIN_IME:229,isTextModifyingKeyEvent:function(e){var t=e.keyCode;if(e.altKey&&!e.ctrlKey||e.metaKey||t>=o9.F1&&t<=o9.F12)return!1;switch(t){case o9.ALT:case o9.CAPS_LOCK:case o9.CONTEXT_MENU:case o9.CTRL:case o9.DOWN:case o9.END:case o9.ESC:case o9.HOME:case o9.INSERT:case o9.LEFT:case o9.MAC_FF_META:case o9.META:case o9.NUMLOCK:case o9.NUM_CENTER:case o9.PAGE_DOWN:case o9.PAGE_UP:case o9.PAUSE:case o9.PRINT_SCREEN:case o9.RIGHT:case o9.SHIFT:case o9.UP:case o9.WIN_KEY:case o9.WIN_KEY_RIGHT:return!1;default:return!0}},isCharacterKey:function(e){if(e>=o9.ZERO&&e<=o9.NINE||e>=o9.NUM_ZERO&&e<=o9.NUM_MULTIPLY||e>=o9.A&&e<=o9.Z||-1!==window.navigator.userAgent.indexOf("WebKit")&&0===e)return!0;switch(e){case o9.SPACE:case o9.QUESTION_MARK:case o9.NUM_PLUS:case o9.NUM_MINUS:case o9.NUM_PERIOD:case o9.NUM_DIVISION:case o9.SEMICOLON:case o9.DASH:case o9.EQUALS:case o9.COMMA:case o9.PERIOD:case o9.SLASH:case o9.APOSTROPHE:case o9.SINGLE_QUOTE:case o9.OPEN_SQUARE_BRACKET:case o9.BACKSLASH:case o9.CLOSE_SQUARE_BRACKET:return!0;default:return!1}}},ae="".concat("accept acceptCharset accessKey action allowFullScreen allowTransparency\n    alt async autoComplete autoFocus autoPlay capture cellPadding cellSpacing challenge\n    charSet checked classID className colSpan cols content contentEditable contextMenu\n    controls coords crossOrigin data dateTime default defer dir disabled download draggable\n    encType form formAction formEncType formMethod formNoValidate formTarget frameBorder\n    headers height hidden high href hrefLang htmlFor httpEquiv icon id inputMode integrity\n    is keyParams keyType kind label lang list loop low manifest marginHeight marginWidth max maxLength media\n    mediaGroup method min minLength multiple muted name noValidate nonce open\n    optimum pattern placeholder poster preload radioGroup readOnly rel required\n    reversed role rowSpan rows sandbox scope scoped scrolling seamless selected\n    shape size sizes span spellCheck src srcDoc srcLang srcSet start step style\n    summary tabIndex target title type useMap value width wmode wrap"," ").concat("onCopy onCut onPaste onCompositionEnd onCompositionStart onCompositionUpdate onKeyDown\n    onKeyPress onKeyUp onFocus onBlur onChange onInput onSubmit onClick onContextMenu onDoubleClick\n    onDrag onDragEnd onDragEnter onDragExit onDragLeave onDragOver onDragStart onDrop onMouseDown\n    onMouseEnter onMouseLeave onMouseMove onMouseOut onMouseOver onMouseUp onSelect onTouchCancel\n    onTouchEnd onTouchMove onTouchStart onScroll onWheel onAbort onCanPlay onCanPlayThrough\n    onDurationChange onEmptied onEncrypted onEnded onError onLoadedData onLoadedMetadata\n    onLoadStart onPause onPlay onPlaying onProgress onRateChange onSeeked onSeeking onStalled onSuspend onTimeUpdate onVolumeChange onWaiting onLoad onError").split(/[\s\n]+/);function at(e,t){return 0===e.indexOf(t)}var an=j.forwardRef(function(e,t){var n=e.prefixCls,r=e.style,o=e.className,a=e.duration,i=void 0===a?4.5:a,s=e.showProgress,l=e.pauseOnHover,c=void 0===l||l,u=e.eventKey,d=e.content,f=e.closable,p=e.closeIcon,h=void 0===p?"x":p,m=e.props,v=e.onClick,g=e.onNoticeClose,y=e.times,b=e.hovering,x=ej(j.useState(!1),2),w=x[0],S=x[1],C=ej(j.useState(0),2),k=C[0],E=C[1],O=ej(j.useState(0),2),_=O[0],N=O[1],A=b||w,M=i>0&&s,P=function(){g(u)};j.useEffect(function(){if(!A&&i>0){var e=Date.now()-_,t=setTimeout(function(){P()},1e3*i-_);return function(){c&&clearTimeout(t),N(Date.now()-e)}}},[i,A,y]),j.useEffect(function(){if(!A&&M&&(c||0===_)){var e,t=performance.now();return!function n(){cancelAnimationFrame(e),e=requestAnimationFrame(function(e){var r=Math.min((e+_-t)/(1e3*i),1);E(100*r),r<1&&n()})}(),function(){c&&cancelAnimationFrame(e)}}},[i,_,A,M,y]);var T=j.useMemo(function(){return"object"===ew(f)&&null!==f?f:f?{closeIcon:h}:{}},[f,h]),R=function(e){var t,n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];t=!1===n?{aria:!0,data:!0,attr:!0}:!0===n?{aria:!0}:eE({},n);var r={};return Object.keys(e).forEach(function(n){(t.aria&&("role"===n||at(n,"aria-"))||t.data&&at(n,"data-")||t.attr&&ae.includes(n))&&(r[n]=e[n])}),r}(T,!0),I=100-(!k||k<0?0:k>100?100:k),L="".concat(n,"-notice");return j.createElement("div",tk({},m,{ref:t,className:rf()(L,o,eC({},"".concat(L,"-closable"),f)),style:r,onMouseEnter:function(e){var t;S(!0),null==m||null==(t=m.onMouseEnter)||t.call(m,e)},onMouseLeave:function(e){var t;S(!1),null==m||null==(t=m.onMouseLeave)||t.call(m,e)},onClick:v}),j.createElement("div",{className:"".concat(L,"-content")},d),f&&j.createElement("a",tk({tabIndex:0,className:"".concat(L,"-close"),onKeyDown:function(e){("Enter"===e.key||"Enter"===e.code||e.keyCode===o9.ENTER)&&P()},"aria-label":"Close"},R,{onClick:function(e){e.preventDefault(),e.stopPropagation(),P()}}),T.closeIcon),M&&j.createElement("progress",{className:"".concat(L,"-progress"),max:"100",value:I},I+"%"))}),ar=j.createContext({});let ao=function(e){var t=e.children,n=e.classNames;return j.createElement(ar.Provider,{value:{classNames:n}},t)},aa=function(e){var t,n,r,o={offset:8,threshold:3,gap:16};return e&&"object"===ew(e)&&(o.offset=null!=(t=e.offset)?t:8,o.threshold=null!=(n=e.threshold)?n:3,o.gap=null!=(r=e.gap)?r:16),[!!e,o]};var ai=["className","style","classNames","styles"];let as=function(e){var t=e.configList,n=e.placement,r=e.prefixCls,o=e.className,a=e.style,i=e.motion,s=e.onAllNoticeRemoved,l=e.onNoticeClose,c=e.stack,u=(0,j.useContext)(ar).classNames,d=(0,j.useRef)({}),f=ej((0,j.useState)(null),2),p=f[0],h=f[1],m=ej((0,j.useState)([]),2),v=m[0],g=m[1],y=t.map(function(e){return{config:e,key:String(e.key)}}),b=ej(aa(c),2),x=b[0],w=b[1],S=w.offset,C=w.threshold,k=w.gap,E=x&&(v.length>0||y.length<=C),O="function"==typeof i?i(n):i;return(0,j.useEffect)(function(){x&&v.length>1&&g(function(e){return e.filter(function(e){return y.some(function(t){return e===t.key})})})},[v,y,x]),(0,j.useEffect)(function(){var e,t;x&&d.current[null==(e=y[y.length-1])?void 0:e.key]&&h(d.current[null==(t=y[y.length-1])?void 0:t.key])},[y,x]),j.createElement(oa,tk({key:n,className:rf()(r,"".concat(r,"-").concat(n),null==u?void 0:u.list,o,eC(eC({},"".concat(r,"-stack"),!!x),"".concat(r,"-stack-expanded"),E)),style:a,keys:y,motionAppear:!0},O,{onAllRemoved:function(){s(n)}}),function(e,t){var o=e.config,a=e.className,i=e.style,s=e.index,c=o.key,f=o.times,h=String(c),m=o.className,b=o.style,w=o.classNames,C=o.styles,O=eZ(o,ai),_=y.findIndex(function(e){return e.key===h}),N={};if(x){var A=y.length-1-(_>-1?_:s-1),M="top"===n||"bottom"===n?"-50%":"0";if(A>0){N.height=E?null==(P=d.current[h])?void 0:P.offsetHeight:null==p?void 0:p.offsetHeight;for(var P,T,R,I,L=0,F=0;F<A;F++)L+=(null==(I=d.current[y[y.length-1-F].key])?void 0:I.offsetHeight)+k;var $=(E?L:A*S)*(n.startsWith("top")?1:-1),Z=!E&&null!=p&&p.offsetWidth&&null!=(T=d.current[h])&&T.offsetWidth?((null==p?void 0:p.offsetWidth)-2*S*(A<3?A:3))/(null==(R=d.current[h])?void 0:R.offsetWidth):1;N.transform="translate3d(".concat(M,", ").concat($,"px, 0) scaleX(").concat(Z,")")}else N.transform="translate3d(".concat(M,", 0, 0)")}return j.createElement("div",{ref:t,className:rf()("".concat(r,"-notice-wrapper"),a,null==w?void 0:w.wrapper),style:eE(eE(eE({},i),N),null==C?void 0:C.wrapper),onMouseEnter:function(){return g(function(e){return e.includes(h)?e:[].concat(eh(e),[h])})},onMouseLeave:function(){return g(function(e){return e.filter(function(e){return e!==h})})}},j.createElement(an,tk({},O,{ref:function(e){_>-1?d.current[h]=e:delete d.current[h]},prefixCls:r,classNames:w,styles:C,className:rf()(m,null==u?void 0:u.notice),style:b,times:f,key:c,eventKey:c,onNoticeClose:l,hovering:x&&v.length>0})))})};var al=j.forwardRef(function(e,t){var n=e.prefixCls,r=void 0===n?"rc-notification":n,o=e.container,a=e.motion,i=e.maxCount,s=e.className,l=e.style,c=e.onAllRemoved,u=e.stack,d=e.renderNotifications,f=ej(j.useState([]),2),p=f[0],h=f[1],m=function(e){var t,n=p.find(function(t){return t.key===e});null==n||null==(t=n.onClose)||t.call(n),h(function(t){return t.filter(function(t){return t.key!==e})})};j.useImperativeHandle(t,function(){return{open:function(e){h(function(t){var n,r=eh(t),o=r.findIndex(function(t){return t.key===e.key}),a=eE({},e);return o>=0?(a.times=((null==(n=t[o])?void 0:n.times)||0)+1,r[o]=a):(a.times=0,r.push(a)),i>0&&r.length>i&&(r=r.slice(-i)),r})},close:function(e){m(e)},destroy:function(){h([])}}});var v=ej(j.useState({}),2),g=v[0],y=v[1];j.useEffect(function(){var e={};p.forEach(function(t){var n=t.placement,r=void 0===n?"topRight":n;r&&(e[r]=e[r]||[],e[r].push(t))}),Object.keys(g).forEach(function(t){e[t]=e[t]||[]}),y(e)},[p]);var b=function(e){y(function(t){var n=eE({},t);return(n[e]||[]).length||delete n[e],n})},x=j.useRef(!1);if(j.useEffect(function(){Object.keys(g).length>0?x.current=!0:x.current&&(null==c||c(),x.current=!1)},[g]),!o)return null;var w=Object.keys(g);return(0,ei.createPortal)(j.createElement(j.Fragment,null,w.map(function(e){var t=g[e],n=j.createElement(as,{key:e,configList:t,placement:e,prefixCls:r,className:null==s?void 0:s(e),style:null==l?void 0:l(e),motion:a,onNoticeClose:m,onAllNoticeRemoved:b,stack:u});return d?d(n,{prefixCls:r,key:e}):n})),o)}),ac=["getContainer","motion","prefixCls","maxCount","className","style","onAllRemoved","stack","renderNotifications"],au=function(){return document.body},ad=0;let af=e=>{let[,,,,t]=om();return t?`${e}-css-var`:""};var ap=eV(function e(){eq(this,e)}),ah="CALC_UNIT",am=RegExp(ah,"g");function av(e){return"number"==typeof e?"".concat(e).concat(ah):e}var ag=function(e){e6(n,e);var t=e7(n);function n(e,r){eq(this,n),eC(e5(o=t.call(this)),"result",""),eC(e5(o),"unitlessCssVar",void 0),eC(e5(o),"lowPriority",void 0);var o,a=ew(e);return o.unitlessCssVar=r,e instanceof n?o.result="(".concat(e.result,")"):"number"===a?o.result=av(e):"string"===a&&(o.result=e),o}return eV(n,[{key:"add",value:function(e){return e instanceof n?this.result="".concat(this.result," + ").concat(e.getResult()):("number"==typeof e||"string"==typeof e)&&(this.result="".concat(this.result," + ").concat(av(e))),this.lowPriority=!0,this}},{key:"sub",value:function(e){return e instanceof n?this.result="".concat(this.result," - ").concat(e.getResult()):("number"==typeof e||"string"==typeof e)&&(this.result="".concat(this.result," - ").concat(av(e))),this.lowPriority=!0,this}},{key:"mul",value:function(e){return this.lowPriority&&(this.result="(".concat(this.result,")")),e instanceof n?this.result="".concat(this.result," * ").concat(e.getResult(!0)):("number"==typeof e||"string"==typeof e)&&(this.result="".concat(this.result," * ").concat(e)),this.lowPriority=!1,this}},{key:"div",value:function(e){return this.lowPriority&&(this.result="(".concat(this.result,")")),e instanceof n?this.result="".concat(this.result," / ").concat(e.getResult(!0)):("number"==typeof e||"string"==typeof e)&&(this.result="".concat(this.result," / ").concat(e)),this.lowPriority=!1,this}},{key:"getResult",value:function(e){return this.lowPriority||e?"(".concat(this.result,")"):this.result}},{key:"equal",value:function(e){var t=this,n=(e||{}).unit,r=!0;return("boolean"==typeof n?r=n:Array.from(this.unitlessCssVar).some(function(e){return t.result.includes(e)})&&(r=!1),this.result=this.result.replace(am,r?"px":""),void 0!==this.lowPriority)?"calc(".concat(this.result,")"):this.result}}]),n}(ap),ay=function(e){e6(n,e);var t=e7(n);function n(e){var r;return eq(this,n),eC(e5(r=t.call(this)),"result",0),e instanceof n?r.result=e.result:"number"==typeof e&&(r.result=e),r}return eV(n,[{key:"add",value:function(e){return e instanceof n?this.result+=e.result:"number"==typeof e&&(this.result+=e),this}},{key:"sub",value:function(e){return e instanceof n?this.result-=e.result:"number"==typeof e&&(this.result-=e),this}},{key:"mul",value:function(e){return e instanceof n?this.result*=e.result:"number"==typeof e&&(this.result*=e),this}},{key:"div",value:function(e){return e instanceof n?this.result/=e.result:"number"==typeof e&&(this.result/=e),this}},{key:"equal",value:function(){return this.result}}]),n}(ap);let ab=function(e,t){var n="css"===e?ag:ay;return function(e){return new n(e,t)}},ax=function(e,t){return"".concat([t,e.replace(/([A-Z]+)([A-Z][a-z]+)/g,"$1-$2").replace(/([a-z])([A-Z])/g,"$1-$2")].filter(Boolean).join("-"))},aj=function(e,t,n,r){var o=eE({},t[e]);null!=r&&r.deprecatedTokens&&r.deprecatedTokens.forEach(function(e){var t=ej(e,2),n=t[0],r=t[1];(null!=o&&o[n]||null!=o&&o[r])&&(null!=o[r]||(o[r]=null==o?void 0:o[n]))});var a=eE(eE({},n),o);return Object.keys(a).forEach(function(e){a[e]===t[e]&&delete a[e]}),a};var aw="undefined"!=typeof CSSINJS_STATISTIC,aS=!0;function aC(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];if(!aw)return Object.assign.apply(Object,[{}].concat(t));aS=!1;var r={};return t.forEach(function(e){"object"===ew(e)&&Object.keys(e).forEach(function(t){Object.defineProperty(r,t,{configurable:!0,enumerable:!0,get:function(){return e[t]}})})}),aS=!0,r}var ak={};function aE(){}let aO=function(e){var t,n=e,r=aE;return aw&&"undefined"!=typeof Proxy&&(t=new Set,n=new Proxy(e,{get:function(e,n){if(aS){var r;null==(r=t)||r.add(n)}return e[n]}}),r=function(e,n){var r;ak[e]={global:Array.from(t),component:eE(eE({},null==(r=ak[e])?void 0:r.component),n)}}),{token:n,keys:t,flush:r}},a_=function(e,t,n){if("function"==typeof n){var r;return n(aC(t,null!=(r=t[e])?r:{}))}return null!=n?n:{}};var aN=new(function(){function e(){eq(this,e),eC(this,"map",new Map),eC(this,"objectIDMap",new WeakMap),eC(this,"nextID",0),eC(this,"lastAccessBeat",new Map),eC(this,"accessBeat",0)}return eV(e,[{key:"set",value:function(e,t){this.clear();var n=this.getCompositeKey(e);this.map.set(n,t),this.lastAccessBeat.set(n,Date.now())}},{key:"get",value:function(e){var t=this.getCompositeKey(e),n=this.map.get(t);return this.lastAccessBeat.set(t,Date.now()),this.accessBeat+=1,n}},{key:"getCompositeKey",value:function(e){var t=this;return e.map(function(e){return e&&"object"===ew(e)?"obj_".concat(t.getObjectID(e)):"".concat(ew(e),"_").concat(e)}).join("|")}},{key:"getObjectID",value:function(e){if(this.objectIDMap.has(e))return this.objectIDMap.get(e);var t=this.nextID;return this.objectIDMap.set(e,t),this.nextID+=1,t}},{key:"clear",value:function(){var e=this;if(this.accessBeat>1e4){var t=Date.now();this.lastAccessBeat.forEach(function(n,r){t-n>6e5&&(e.map.delete(r),e.lastAccessBeat.delete(r))}),this.accessBeat=0}}}]),e}());let aA=function(){return{}},{genStyleHooks:aM,genComponentStyleHook:aP,genSubStyleComponent:aT}=function(e){var t=e.useCSP,n=void 0===t?aA:t,r=e.useToken,o=e.usePrefix,a=e.getResetStyles,i=e.getCommonStyle,s=e.getCompUnitless;function l(t,s,l){var c=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},u=Array.isArray(t)?t:[t,t],d=ej(u,1)[0],f=u.join("-"),p=e.layer||{name:"antd"};return function(e){var t,u,h=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e,m=r(),v=m.theme,g=m.realToken,y=m.hashId,b=m.token,x=m.cssVar,w=o(),S=w.rootPrefixCls,C=w.iconPrefixCls,k=n(),E=x?"css":"js",O=(t=function(){var e=new Set;return x&&Object.keys(c.unitless||{}).forEach(function(t){e.add(tf(t,x.prefix)),e.add(tf(t,ax(d,x.prefix)))}),ab(E,e)},u=[E,d,null==x?void 0:x.prefix],j.useMemo(function(){var e=aN.get(u);if(e)return e;var n=t();return aN.set(u,n),n},u)),_="js"===E?{max:Math.max,min:Math.min}:{max:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return"max(".concat(t.map(function(e){return tu(e)}).join(","),")")},min:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return"min(".concat(t.map(function(e){return tu(e)}).join(","),")")}},N=_.max,A=_.min,M={theme:v,token:b,hashId:y,nonce:function(){return k.nonce},clientOnly:c.clientOnly,layer:p,order:c.order||-999};return"function"==typeof a&&t7(eE(eE({},M),{},{clientOnly:!1,path:["Shared",S]}),function(){return a(b,{prefix:{rootPrefixCls:S,iconPrefixCls:C},csp:k})}),[t7(eE(eE({},M),{},{path:[f,e,C]}),function(){if(!1===c.injectStyle)return[];var t=aO(b),n=t.token,r=t.flush,o=a_(d,g,l),a=".".concat(e),u=aj(d,g,o,{deprecatedTokens:c.deprecatedTokens});x&&o&&"object"===ew(o)&&Object.keys(o).forEach(function(e){o[e]="var(".concat(tf(e,ax(d,x.prefix)),")")});var f=aC(n,{componentCls:a,prefixCls:e,iconCls:".".concat(C),antCls:".".concat(S),calc:O,max:N,min:A},x?o:u),p=s(f,{hashId:y,prefixCls:e,rootPrefixCls:S,iconPrefixCls:C});r(d,u);var m="function"==typeof i?i(f,e,h,c.resetFont):null;return[!1===c.resetStyle?null:m,p]}),y]}}return{genStyleHooks:function(e,t,n,o){var a,i,c,u,d,f,p,h,m,v=Array.isArray(e)?e[0]:e;function g(e){return"".concat(String(v)).concat(e.slice(0,1).toUpperCase()).concat(e.slice(1))}var y=(null==o?void 0:o.unitless)||{},b=eE(eE({},"function"==typeof s?s(e):{}),{},eC({},g("zIndexPopup"),!0));Object.keys(y).forEach(function(e){b[g(e)]=y[e]});var x=eE(eE({},o),{},{unitless:b,prefixToken:g}),w=l(e,t,n,x),S=(a=v,i=n,u=(c=x).unitless,f=void 0===(d=c.injectStyle)||d,p=c.prefixToken,h=c.ignore,m=function(e){var t=e.rootCls,n=e.cssVar,o=void 0===n?{}:n,s=r().realToken;return ne({path:[a],prefix:o.prefix,key:o.key,unitless:u,ignore:h,token:s,scope:t},function(){var e=a_(a,s,i),t=aj(a,s,e,{deprecatedTokens:null==c?void 0:c.deprecatedTokens});return Object.keys(e).forEach(function(e){t[p(e)]=t[e],delete t[e]}),t}),null},function(e){var t=r().cssVar;return[function(n){return f&&t?j.createElement(j.Fragment,null,j.createElement(m,{rootCls:e,cssVar:t,component:a}),n):n},null==t?void 0:t.key]});return function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e,n=ej(w(e,t),2)[1],r=ej(S(t),2);return[r[0],n,r[1]]}},genSubStyleComponent:function(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},o=l(e,t,n,eE({resetStyle:!1,order:-998},r));return function(e){var t=e.prefixCls,n=e.rootCls,r=void 0===n?t:n;return o(t,r),null}},genComponentStyleHook:l}}({usePrefix:()=>{let{getPrefixCls:e,iconPrefixCls:t}=(0,j.useContext)(eg);return{rootPrefixCls:e(),iconPrefixCls:t}},useToken:()=>{let[e,t,n,r,o]=om();return{theme:e,realToken:t,hashId:n,token:r,cssVar:o}},useCSP:()=>{let{csp:e}=(0,j.useContext)(eg);return null!=e?e:{}},getResetStyles:(e,t)=>{var n;let r=ox(e);return[r,{"&":r},oj(null!=(n=null==t?void 0:t.prefix.iconPrefixCls)?n:ev)]},getCommonStyle:(e,t,n,r)=>{let o=`[class^="${t}"], [class*=" ${t}"]`,a=n?`.${n}`:o,i={boxSizing:"border-box","&::before, &::after":{boxSizing:"border-box"}},s={};return!1!==r&&(s={fontFamily:e.fontFamily,fontSize:e.fontSize}),{[a]:Object.assign(Object.assign(Object.assign({},s),i),{[o]:i})}},getCompUnitless:()=>od}),aR=e=>{let{componentCls:t,iconCls:n,boxShadow:r,colorText:o,colorSuccess:a,colorError:i,colorWarning:s,colorInfo:l,fontSizeLG:c,motionEaseInOutCirc:u,motionDurationSlow:d,marginXS:f,paddingXS:p,borderRadiusLG:h,zIndexPopup:m,contentPadding:v,contentBg:g}=e,y=`${t}-notice`,b=new nt("MessageMoveIn",{"0%":{padding:0,transform:"translateY(-100%)",opacity:0},"100%":{padding:p,transform:"translateY(0)",opacity:1}}),x=new nt("MessageMoveOut",{"0%":{maxHeight:e.height,padding:p,opacity:1},"100%":{maxHeight:0,padding:0,opacity:0}}),j={padding:p,textAlign:"center",[`${t}-custom-content`]:{display:"flex",alignItems:"center"},[`${t}-custom-content > ${n}`]:{marginInlineEnd:f,fontSize:c},[`${y}-content`]:{display:"inline-block",padding:v,background:g,borderRadius:h,boxShadow:r,pointerEvents:"all"},[`${t}-success > ${n}`]:{color:a},[`${t}-error > ${n}`]:{color:i},[`${t}-warning > ${n}`]:{color:s},[`${t}-info > ${n},
      ${t}-loading > ${n}`]:{color:l}};return[{[t]:Object.assign(Object.assign({},oy(e)),{color:o,position:"fixed",top:f,width:"100%",pointerEvents:"none",zIndex:m,[`${t}-move-up`]:{animationFillMode:"forwards"},[`
        ${t}-move-up-appear,
        ${t}-move-up-enter
      `]:{animationName:b,animationDuration:d,animationPlayState:"paused",animationTimingFunction:u},[`
        ${t}-move-up-appear${t}-move-up-appear-active,
        ${t}-move-up-enter${t}-move-up-enter-active
      `]:{animationPlayState:"running"},[`${t}-move-up-leave`]:{animationName:x,animationDuration:d,animationPlayState:"paused",animationTimingFunction:u},[`${t}-move-up-leave${t}-move-up-leave-active`]:{animationPlayState:"running"},"&-rtl":{direction:"rtl",span:{direction:"rtl"}}})},{[t]:{[`${y}-wrapper`]:Object.assign({},j)}},{[`${t}-notice-pure-panel`]:Object.assign(Object.assign({},j),{padding:0,textAlign:"start"})}]},aI=aM("Message",e=>[aR(aC(e,{height:150}))],e=>({zIndexPopup:e.zIndexPopupBase+1e3+10,contentBg:e.colorBgElevated,contentPadding:`${(e.controlHeightLG-e.fontSize*e.lineHeight)/2}px ${e.paddingSM}px`}));var aL=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};let aF={info:j.createElement(o3,null),success:j.createElement(o0,null),error:j.createElement(o2,null),warning:j.createElement(o4,null),loading:j.createElement(o7,null)},a$=e=>{let{prefixCls:t,type:n,icon:r,children:o}=e;return j.createElement("div",{className:rf()(`${t}-custom-content`,`${t}-${n}`)},r||aF[n],j.createElement("span",null,o))},aZ={icon:{tag:"svg",attrs:{"fill-rule":"evenodd",viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M799.86 166.31c.02 0 .04.02.08.06l57.69 57.7c.04.03.05.05.06.08a.12.12 0 010 .06c0 .03-.02.05-.06.09L569.93 512l287.7 287.7c.04.04.05.06.06.09a.12.12 0 010 .07c0 .02-.02.04-.06.08l-57.7 57.69c-.03.04-.05.05-.07.06a.12.12 0 01-.07 0c-.03 0-.05-.02-.09-.06L512 569.93l-287.7 287.7c-.04.04-.06.05-.09.06a.12.12 0 01-.07 0c-.02 0-.04-.02-.08-.06l-57.69-57.7c-.04-.03-.05-.05-.06-.07a.12.12 0 010-.07c0-.03.02-.05.06-.09L454.07 512l-287.7-287.7c-.04-.04-.05-.06-.06-.09a.12.12 0 010-.07c0-.02.02-.04.06-.08l57.7-57.69c.03-.04.05-.05.07-.06a.12.12 0 01.07 0c.03 0 .05.02.09.06L512 454.07l287.7-287.7c.04-.04.06-.05.09-.06a.12.12 0 01.07 0z"}}]},name:"close",theme:"outlined"};var aH=j.forwardRef(function(e,t){return j.createElement(oJ,tk({},e,{ref:t,icon:aZ}))});function aD(e){let t,n=new Promise(n=>{t=e(()=>{n(!0)})}),r=()=>{null==t||t()};return r.then=(e,t)=>n.then(e,t),r.promise=n,r}var aB=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};let az=e=>{let{children:t,prefixCls:n}=e,r=af(n),[o,a,i]=aI(n,r);return o(j.createElement(ao,{classNames:{list:rf()(a,i,r)}},t))},aW=(e,t)=>{let{prefixCls:n,key:r}=t;return j.createElement(az,{prefixCls:n,key:r},e)},aU=j.forwardRef((e,t)=>{let{top:n,prefixCls:r,getContainer:o,maxCount:a,duration:i=3,rtl:s,transitionName:l,onAllRemoved:c}=e,{getPrefixCls:u,getPopupContainer:d,message:f,direction:p}=j.useContext(eg),h=r||u("message"),m=j.createElement("span",{className:`${h}-close-x`},j.createElement(aH,{className:`${h}-close-icon`})),[v,g]=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.getContainer,n=void 0===t?au:t,r=e.motion,o=e.prefixCls,a=e.maxCount,i=e.className,s=e.style,l=e.onAllRemoved,c=e.stack,u=e.renderNotifications,d=eZ(e,ac),f=ej(j.useState(),2),p=f[0],h=f[1],m=j.useRef(),v=j.createElement(al,{container:p,ref:m,prefixCls:o,motion:r,maxCount:a,className:i,style:s,onAllRemoved:l,stack:c,renderNotifications:u}),g=ej(j.useState([]),2),y=g[0],b=g[1],x=j.useMemo(function(){return{open:function(e){var t=function(){for(var e={},t=arguments.length,n=Array(t),r=0;r<t;r++)n[r]=arguments[r];return n.forEach(function(t){t&&Object.keys(t).forEach(function(n){var r=t[n];void 0!==r&&(e[n]=r)})}),e}(d,e);(null===t.key||void 0===t.key)&&(t.key="rc-notification-".concat(ad),ad+=1),b(function(e){return[].concat(eh(e),[{type:"open",config:t}])})},close:function(e){b(function(t){return[].concat(eh(t),[{type:"close",key:e}])})},destroy:function(){b(function(e){return[].concat(eh(e),[{type:"destroy"}])})}}},[]);return j.useEffect(function(){h(n())}),j.useEffect(function(){if(m.current&&y.length){var e,t;y.forEach(function(e){switch(e.type){case"open":m.current.open(e.config);break;case"close":m.current.close(e.key);break;case"destroy":m.current.destroy()}}),b(function(n){return e===n&&t||(e=n,t=n.filter(function(e){return!y.includes(e)})),t})}},[y]),[x,v]}({prefixCls:h,style:()=>({left:"50%",transform:"translateX(-50%)",top:null!=n?n:8}),className:()=>rf()({[`${h}-rtl`]:null!=s?s:"rtl"===p}),motion:()=>({motionName:null!=l?l:`${h}-move-up`}),closable:!1,closeIcon:m,duration:i,getContainer:()=>(null==o?void 0:o())||(null==d?void 0:d())||document.body,maxCount:a,onAllRemoved:c,renderNotifications:aW});return j.useImperativeHandle(t,()=>Object.assign(Object.assign({},v),{prefixCls:h,message:f})),g}),aG=0;function aK(e){let t=j.useRef(null);return nu("Message"),[j.useMemo(()=>{let e=e=>{var n;null==(n=t.current)||n.close(e)},n=n=>{if(!t.current){let e=()=>{};return e.then=()=>{},e}let{open:r,prefixCls:o,message:a}=t.current,i=`${o}-notice`,{content:s,icon:l,type:c,key:u,className:d,style:f,onClose:p}=n,h=aB(n,["content","icon","type","key","className","style","onClose"]),m=u;return null==m&&(aG+=1,m=`antd-message-${aG}`),aD(t=>(r(Object.assign(Object.assign({},h),{key:m,content:j.createElement(a$,{prefixCls:o,type:c,icon:l},s),placement:"top",className:rf()(c&&`${i}-${c}`,d,null==a?void 0:a.className),style:Object.assign(Object.assign({},null==a?void 0:a.style),f),onClose:()=>{null==p||p(),t()}})),()=>{e(m)}))},r={open:n,destroy:n=>{var r;void 0!==n?e(n):null==(r=t.current)||r.destroy()}};return["info","success","warning","error","loading"].forEach(e=>{r[e]=(t,r,o)=>{let a,i;return"function"==typeof r?i=r:(a=r,i=o),n(Object.assign(Object.assign({onClose:i,duration:a},t&&"object"==typeof t&&"content"in t?t:{content:t}),{type:e}))}}),r},[]),j.createElement(aU,Object.assign({key:"message-holder"},e,{ref:t}))]}let aq=null,aX=e=>e(),aV=[],aY={};function aQ(){let{getContainer:e,duration:t,rtl:n,maxCount:r,top:o}=aY,a=(null==e?void 0:e())||document.body;return{getContainer:()=>a,duration:t,rtl:n,maxCount:r,top:o}}let aJ=j.forwardRef((e,t)=>{let{messageConfig:n,sync:r}=e,{getPrefixCls:o}=(0,j.useContext)(eg),a=aY.prefixCls||o("message"),i=(0,j.useContext)(em),[s,l]=aK(Object.assign(Object.assign(Object.assign({},n),{prefixCls:a}),i.message));return j.useImperativeHandle(t,()=>{let e=Object.assign({},s);return Object.keys(e).forEach(t=>{e[t]=function(){return r(),s[t].apply(s,arguments)}}),{instance:e,sync:r}}),l}),a0=j.forwardRef((e,t)=>{let[n,r]=j.useState(aQ),o=()=>{r(aQ)};j.useEffect(o,[]);let a=oO(),i=a.getRootPrefixCls(),s=a.getIconPrefixCls(),l=a.getTheme(),c=j.createElement(aJ,{ref:t,sync:o,messageConfig:n});return j.createElement(oN,{prefixCls:i,iconPrefixCls:s,theme:l},a.holderRender?a.holderRender(c):c)});function a1(){if(!aq){let e=document.createDocumentFragment(),t={fragment:e};aq=t,aX(()=>{oD(j.createElement(a0,{ref:e=>{let{instance:n,sync:r}=e||{};Promise.resolve().then(()=>{!t.instance&&n&&(t.instance=n,t.sync=r,a1())})}}),e)});return}aq.instance&&(aV.forEach(e=>{let{type:t,skipped:n}=e;if(!n)switch(t){case"open":aX(()=>{let t=aq.instance.open(Object.assign(Object.assign({},aY),e.config));null==t||t.then(e.resolve),e.setCloseFn(t)});break;case"destroy":aX(()=>{null==aq||aq.instance.destroy(e.key)});break;default:aX(()=>{var n;let r=(n=aq.instance)[t].apply(n,eh(e.args));null==r||r.then(e.resolve),e.setCloseFn(r)})}}),aV=[])}let a2={open:function(e){let t=aD(t=>{let n,r={type:"open",config:e,resolve:t,setCloseFn:e=>{n=e}};return aV.push(r),()=>{n?aX(()=>{n()}):r.skipped=!0}});return a1(),t},destroy:e=>{aV.push({type:"destroy",key:e}),a1()},config:function(e){aY=Object.assign(Object.assign({},aY),e),aX(()=>{var e;null==(e=null==aq?void 0:aq.sync)||e.call(aq)})},useMessage:function(e){return aK(e)},_InternalPanelDoNotUseOrYouWillBeFired:e=>{let{prefixCls:t,className:n,type:r,icon:o,content:a}=e,i=aL(e,["prefixCls","className","type","icon","content"]),{getPrefixCls:s}=j.useContext(eg),l=t||s("message"),c=af(l),[u,d,f]=aI(l,c);return u(j.createElement(an,Object.assign({},i,{prefixCls:l,className:rf()(n,d,`${l}-notice-pure-panel`,f,c),eventKey:"pure",duration:null,content:j.createElement(a$,{prefixCls:l,type:r,icon:o},a)})))}};["success","info","warning","error","loading"].forEach(e=>{a2[e]=function(){for(var t=arguments.length,n=Array(t),r=0;r<t;r++)n[r]=arguments[r];oO();let o=aD(t=>{let r,o={type:e,args:n,resolve:t,setCloseFn:e=>{r=e}};return aV.push(o),()=>{r?aX(()=>{r()}):o.skipped=!0}});return a1(),o}});class a5 extends Error{constructor(e){super("Unauthorized"),this.name="UnauthorizedError",this.message=e}}let a4=class{async baseRequest(e,t){let n=await fetch(`${this.options.baseURL}${e}`,{...t,headers:{...t.headers,Authorization:`Bearer ${this.options.apiKey}`}});if(n.headers.get("X-Version")){let e=n.headers.get("X-Version");e&&e!==$.version&&($.version=e)}if(401===n.status)throw a2.error("未授权, 请检查你的配置"),new a5("Unauthorized");return n}async jsonRequest(e,t){return(await this.baseRequest(e,{...t,headers:{...t.headers,"Content-Type":"application/json"}})).json()}async get(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=t?`?${new URLSearchParams(t).toString()}`:"";return await this.jsonRequest(`${e}${r}`,{method:"GET",headers:n})}async post(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return await this.jsonRequest(e,{method:"POST",body:JSON.stringify(t),headers:n})}async delete(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return await this.jsonRequest(e,{method:"DELETE",body:JSON.stringify(t),headers:n})}constructor(e){var t,n;n=void 0,(t="options")in this?Object.defineProperty(this,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):this[t]=n,this.options=e}};function a6(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class a3{updateOptions(e){this.options=e,this.baseRequest=new a4({baseURL:e.apiBase,apiKey:e.apiKey})}async getAppInfo(){return this.baseRequest.get("/info")}async getAppMeta(){return this.baseRequest.get("/meta")}getConversationList(e){return this.baseRequest.get("/conversations",{user:this.options.user,limit:((null==e?void 0:e.limit)||100).toString()})}sendMessage(e){return this.baseRequest.baseRequest("/chat-messages",{method:"POST",body:JSON.stringify(e),headers:{"Content-Type":"application/json"}})}async stopTask(e){return this.baseRequest.post(`/chat-messages/${e}/stop`,{user:this.options.user})}async uploadFile(e){let t=new FormData;return t.append("file",e),t.append("user",this.options.user),this.baseRequest.baseRequest("/files/upload",{method:"POST",body:t}).then(e=>e.json())}async getNextSuggestions(e){return this.baseRequest.get(`/messages/${e.message_id}/suggested`,{user:this.options.user})}feedbackMessage(e){let{messageId:t,...n}=e;return this.baseRequest.post(`/messages/${t}/feedbacks`,{...n,user:this.options.user})}async text2Audio(e){return this.baseRequest.baseRequest("/text-to-audio",{method:"POST",body:JSON.stringify({...e,user:this.options.user}),headers:{"Content-Type":"application/json"}})}async audio2Text(e){let t=new FormData;return t.append("file",e),t.append("user",this.options.user),this.baseRequest.baseRequest("/audio-to-text",{method:"POST",body:t}).then(e=>e.json())}constructor(e){a6(this,"options",void 0),a6(this,"baseRequest",void 0),a6(this,"getAppParameters",()=>this.baseRequest.get("/parameters")),a6(this,"renameConversation",e=>{let{conversation_id:t,...n}=e;return this.baseRequest.post(`/conversations/${t}/name`,{...n,user:this.options.user})}),a6(this,"deleteConversation",e=>this.baseRequest.delete(`/conversations/${e}`,{user:this.options.user})),a6(this,"getConversationHistory",e=>this.baseRequest.get("/messages",{user:this.options.user,conversation_id:e})),this.options=e,this.baseRequest=new a4({baseURL:e.apiBase,apiKey:e.apiKey})}}let a8=e=>new a3(e);var a7=((p={}).MESSAGE="message",p.AGENT_MESSAGE="agent_message",p.AGENT_THOUGHT="agent_thought",p.MESSAGE_FILE="message_file",p.MESSAGE_END="message_end",p.TTS_MESSAGE="tts_message",p.TTS_MESSAGE_END="tts_message_end",p.MESSAGE_REPLACE="message_replace",p.ERROR="error",p.PING="ping",p.WORKFLOW_STARTED="workflow_started",p.WORKFLOW_FINISHED="workflow_finished",p.WORKFLOW_NODE_STARTED="node_started",p.WORKFLOW_NODE_FINISHED="node_finished",p);let a9=new a4({baseURL:"https://ai.medsci.cn",apiKey:"123456"});class ie{async getAsrToken(){return a9.get("/dev-api/ai-base/yps-chat/getAsrToken")}}var it=n(9253),ir=n(53577);function io(e){let{formInstance:t}=e,n=J.Z.useWatch("answerForm.enabled",t);return(0,b.jsxs)(J.Z,{autoComplete:"off",form:t,labelAlign:"left",labelCol:{span:5},initialValues:{"answerForm.enabled":!1},children:[(0,b.jsxs)("div",{className:"text-base mb-3 flex items-center",children:[(0,b.jsx)("div",{className:"h-4 w-1 bg-primary rounded"}),(0,b.jsx)("div",{className:"ml-2 font-semibold",children:"请求配置"})]}),(0,b.jsx)(J.Z.Item,{label:"API Base",name:"apiBase",rules:[{required:!0}],tooltip:"Dify API 的域名+版本号前缀，如 https://api.dify.ai/v1",required:!0,children:(0,b.jsx)(it.Z,{autoComplete:"new-password",placeholder:"请输入 API BASE"})}),(0,b.jsx)(J.Z.Item,{label:"API Secret",name:"apiKey",tooltip:"Dify App 的 API Secret (以 app- 开头)",rules:[{required:!0}],required:!0,children:(0,b.jsx)(it.Z.Password,{autoComplete:"new-password",placeholder:"请输入 API Secret"})}),(0,b.jsxs)("div",{className:"text-base mb-3 flex items-center",children:[(0,b.jsx)("div",{className:"h-4 w-1 bg-primary rounded"}),(0,b.jsx)("div",{className:"ml-2 font-semibold",children:"更多配置"})]}),(0,b.jsx)(J.Z.Item,{label:"表单回复",name:"answerForm.enabled",tooltip:"当工作流需要回复表单给用户填写时，建议开启此功能",rules:[{required:!0}],required:!0,children:(0,b.jsx)(ir.Z,{placeholder:"请选择",options:[{label:"启用",value:!0},{label:"禁用",value:!1}]})}),n?(0,b.jsx)(J.Z.Item,{label:"提交消息文本",name:"answerForm.feedbackText",tooltip:"当启用表单回复时，用户填写表单并提交后，默认会以用户角色将填写的表单数据作为消息文本发送，如果配置了此字段，将会固定展示配置的字段值",children:(0,b.jsx)(it.Z,{placeholder:"请输入提交消息文本"})}):null]})}let ia=e=>{var t;let{detailDrawerMode:n,appItem:r,open:o,onClose:a,confirmCallback:i}=e,{user:s,appService:l}=A(),[c]=J.Z.useForm(),[u,d]=(0,j.useState)(!1);(0,j.useEffect)(()=>{if(o){if(n===ii.edit){var e,t;c.setFieldsValue({apiBase:null==r?void 0:r.requestConfig.apiBase,apiKey:null==r?void 0:r.requestConfig.apiKey,"answerForm.enabled":(null==r||null==(e=r.answerForm)?void 0:e.enabled)||!1,"answerForm.feedbackText":(null==r||null==(t=r.answerForm)?void 0:t.feedbackText)||""})}}else c.resetFields()},[o]);let{runAsync:f}=(0,X.Z)(async e=>l.addApp(e),{manual:!0,onSuccess:()=>{null==a||a(),V.ZP.success("新增应用配置成功")}}),{runAsync:p}=(0,X.Z)(async e=>l.updateApp(e),{manual:!0,onSuccess:()=>{null==a||a(),V.ZP.success("编辑应用配置成功")}});return(0,b.jsxs)(ee.Z,{width:600,title:`${n===ii.create?"新增应用配置":`编辑应用配置 - ${null==r?void 0:r.info.name}`}`,open:o,onClose:a,extra:(0,b.jsxs)(er.Z,{children:[(0,b.jsx)(W.ZP,{onClick:a,children:"取消"}),(0,b.jsx)(W.ZP,{type:"primary",loading:u,onClick:async()=>{await c.validateFields(),d(!0);try{let e=c.getFieldsValue(),t=new a3({user:s,apiBase:e.apiBase,apiKey:e.apiKey}),o={info:await t.getAppInfo(),requestConfig:{apiBase:e.apiBase,apiKey:e.apiKey},answerForm:{enabled:e["answerForm.enabled"],feedbackText:e["answerForm.feedbackText"]}};n===ii.edit?await p({id:r.id,...o}):await f({id:Math.random().toString(),...o}),null==i||i()}catch(e){console.error("保存应用配置失败",e),V.ZP.error(`保存应用配置失败: ${e}`)}finally{d(!1)}},children:n===ii.create?"确定":"更新"})]}),children:[n===ii.edit?(0,b.jsxs)(J.Z,{labelAlign:"left",labelCol:{span:5},layout:"horizontal",children:[(0,b.jsxs)("div",{className:"text-base mb-3 flex items-center",children:[(0,b.jsx)("div",{className:"h-4 w-1 bg-primary rounded"}),(0,b.jsx)("div",{className:"ml-2 font-semibold",children:"基本信息"})]}),(0,b.jsx)(J.Z.Item,{label:"应用名称",children:(0,b.jsx)(it.Z,{disabled:!0,value:null==r?void 0:r.info.name})}),(0,b.jsx)(J.Z.Item,{label:"应用描述",children:(0,b.jsx)(it.Z,{disabled:!0,value:null==r?void 0:r.info.name})}),(0,b.jsx)(J.Z.Item,{label:"应用标签",children:(null==r||null==(t=r.info.tags)?void 0:t.length)?(0,b.jsx)("div",{className:"text-default",children:r.info.tags.join(", ")}):(0,b.jsx)(b.Fragment,{children:"无"})})]}):null,(0,b.jsx)(io,{formInstance:c})]})};var ii=((h={}).create="create",h.edit="edit",h);function is(e){let{activeAppId:t,getAppList:n,appListLoading:r,appList:o,onDeleteSuccess:a,...i}=e,{appService:s}=A(),[l,c]=(0,j.useState)(),[u,d]=(0,j.useState)(!1),[f]=J.Z.useForm(),[p,h]=(0,j.useState)(),m=L(),v=null==o?void 0:o.find(e=>e.id===l);return(0,b.jsxs)(ee.Z,{width:700,title:"应用配置管理",...i,children:[(0,b.jsxs)("div",{className:"w-full h-full overflow-hidden flex flex-col",children:[(0,b.jsx)("div",{className:"pb-3 flex-1 overflow-y-auto",children:(0,b.jsx)(B.Z,{spinning:r,children:(0,b.jsx)(et.Z,{gutter:16*!m,className:"w-full",children:(null==o?void 0:o.length)?null==o?void 0:o.map(e=>{var r;return(0,b.jsx)(en.Z,{span:m?24:12,children:(0,b.jsxs)("div",{className:"p-3 bg-white mb-3 border border-solid border-gray-200 rounded-lg cursor-pointer hover:border-primary hover:text-primary",onClick:()=>{var t,n;c(e.id),f.setFieldsValue({apiBase:e.requestConfig.apiBase,apiKey:e.requestConfig.apiKey,"answerForm.enabled":(null==(t=e.answerForm)?void 0:t.enabled)||!1,"answerForm.feedbackText":(null==(n=e.answerForm)?void 0:n.feedbackText)||""}),h("edit"),d(!0)},children:[(0,b.jsxs)("div",{className:"w-full flex items-center overflow-hidden",children:[(0,b.jsxs)("div",{className:"flex-1 font-semibold truncate",children:[t===e.id&&"【当前】",e.info.name]}),(0,b.jsx)(er.Z,{className:"inline-flex items-center",children:(0,b.jsx)(eo.Z,{onPopupClick:e=>e.stopPropagation(),cancelText:"取消",okText:"确定",title:"确定删除应用吗？",onConfirm:async()=>{await s.deleteApp(e.id),V.ZP.success("删除应用成功"),n(),null==a||a(e.id)},children:(0,b.jsx)(Q.Z,{onClick:e=>e.stopPropagation(),className:"p-0 text-red-500"})})})]}),(0,b.jsx)("div",{title:e.info.description,className:"truncate text-sm mt-2 text-desc h-6 leading-6",children:e.info.description}),(0,b.jsxs)("div",{className:"mt-3 text-desc truncate",title:e.info.tags.join(", "),children:["标签：",(null==(r=e.info.tags)?void 0:r.length)?e.info.tags.join(", "):(0,b.jsx)(b.Fragment,{children:"无"})]})]})},e.id)}):(0,b.jsx)(ea.Z,{className:"mx-auto",description:"暂无应用"})})})}),(0,b.jsx)(W.ZP,{type:"primary",size:"large",block:!0,onClick:()=>{c(""),h("create"),f.resetFields(),d(!0)},children:"添加应用"})]}),(0,b.jsx)(ia,{open:u,detailDrawerMode:p,onClose:()=>d(!1),appItem:v})]})}var il=n(16526),ic=n(34748),iu=n(30146),id=n(57500),ip=n(17432),ih=n(97823),im=n(12473),iv=n(38211),ig=n(2260),iy=n(87844),ib=n(72834);let ix=e=>{let{stream:t,isRecording:n,width:r=300,height:o=50,barWidth:a=3,barGap:i=1,barColor:s="#1677ff"}=e,l=(0,j.useRef)(null),c=(0,j.useRef)(null),u=(0,j.useRef)(null),d=(0,j.useRef)(null),f=(0,j.useRef)(null);return(0,j.useEffect)(()=>{if(!t||!n||!l.current)return;c.current=new(window.AudioContext||window.webkitAudioContext),u.current=c.current.createAnalyser(),u.current.fftSize=256,c.current.createMediaStreamSource(t).connect(u.current),d.current=new Uint8Array(u.current.frequencyBinCount);let e=()=>{if(!l.current||!u.current||!d.current)return;let t=l.current,n=t.getContext("2d");if(!n)return;let c=t.parentElement;c&&(t.width=c.clientWidth||r,t.height=c.clientHeight||o),u.current.getByteFrequencyData(d.current),n.clearRect(0,0,t.width,t.height);let p=Math.floor(t.width/(a+i)),h=Math.ceil(d.current.length/p);for(let e=0;e<p;e++){let r=Math.floor(e*h),o=(d.current[r]||0)/255*t.height,l=n.createLinearGradient(0,t.height-o,0,t.height);l.addColorStop(0,s),l.addColorStop(1,`${s}33`),n.fillStyle=l,n.fillRect(e*(a+i),t.height-o,a,o)}f.current=requestAnimationFrame(e)};return e(),()=>{f.current&&cancelAnimationFrame(f.current),c.current&&c.current.close().catch(console.error)}},[t,n,a,i,s,o,r]),(0,b.jsx)("canvas",{ref:l,style:{width:"100%",height:"100%",display:"block"}})},ij=new Map;ij.set("document",["txt","md","markdown","pdf","html","xlsx","xls","docx","csv","eml","msg","pptx","ppt","xml","epub"]),ij.set("image",["jpg","jpeg","png","gif","svg","webp"]),ij.set("audio",["mp3","m4a","wav","webm","amr"]),ij.set("video",["mp4","mov","mpeg","mpga"]),ij.set("custom",[]);let iw=e=>{let t=e.split(".").pop(),n=null;return ij.forEach((e,r)=>{e.indexOf(t)>-1&&(n=r)}),n},iS=e=>e>1048576?`${(e/1024/1024).toFixed(2)} MB`:`${(e/1024).toFixed(2)} KB`,iC=()=>"10000000-1000-4000-8000-100000000000".replace(/[018]/g,e=>(e^crypto.getRandomValues(new Uint8Array(1))[0]&15>>e/4).toString(16)).replace(/-/g,""),ik=e=>{var t;let{isRequesting:n,onSubmit:r,className:o,onCancel:a,uploadFileApi:i,audio2TextApi:s,appParameters:l,asrConfig:c}=e,[u,d]=(0,j.useState)(""),[f,p]=(0,j.useState)(!1),[h,m]=(0,j.useState)([]),[v,g]=(0,j.useState)(new Map),y=(0,j.useRef)([]),[x,w]=(0,j.useState)(!1),S=(0,j.useRef)(""),C=(0,j.useRef)(null),k=(0,j.useRef)(null),E=(0,j.useRef)(null),O=(0,j.useRef)(null),[_,N]=(0,j.useState)(!1),A=(0,j.useRef)(""),[M,P]=(0,j.useState)(!1),[T,R]=(0,j.useState)(null),I=(0,j.useRef)(null),L=(0,j.useRef)(null),F=(0,j.useRef)(null),[$,Z]=(0,j.useState)(0);(0,j.useEffect)(()=>{if(L.current){let e=()=>{if(L.current){let e=L.current.offsetWidth;Z(e),F.current&&(F.current.style.width=`${e}px`)}};e();let t=new ResizeObserver(e);return t.observe(L.current),()=>{L.current&&t.unobserve(L.current),t.disconnect()}}},[M]),(0,j.useEffect)(()=>{let e=()=>{if(L.current&&F.current){let e=L.current.getBoundingClientRect().width;F.current.style.width=`${e}px`}};M&&(e(),setTimeout(e,100))},[M,$]);let H=()=>{try{let e=`${c.url}?token=${c.token}`;C.current=new WebSocket(e),C.current.onopen=()=>{var e;N(!0),console.log("WebSocket已连接");let t=iC();A.current=t;let n={header:{appkey:c.appKey,namespace:"SpeechTranscriber",name:"StartTranscription",task_id:t,message_id:iC()},payload:{format:"pcm",sample_rate:16e3,enable_intermediate_result:!0,enable_punctuation_prediction:!0,enable_inverse_text_normalization:!0}};null==(e=C.current)||e.send(JSON.stringify(n))},C.current.onmessage=e=>{let t=JSON.parse(e.data);if(console.log("收到WebSocket消息:",t),"TranscriptionResultChanged"===t.header.name||"TranscriptionCompleted"===t.header.name){if(t.payload&&t.payload.result){let e=t.payload.result;d(n=>n?S.current===e?n:"TranscriptionCompleted"===t.header.name?(S.current="",n+(n?" ":"")+e):(S.current=e,n):(S.current=e,e))}}else if("SentenceEnd"===t.header.name){if(t.payload&&t.payload.result){let e=t.payload.result;d(t=>(S.current="",t+(t?" ":"")+e))}}else if("TaskFailed"===t.header.name){var n;console.error("语音识别失败:",t.payload),t.error("语音识别失败: "+((null==(n=t.payload)?void 0:n.message)||"未知错误"))}},C.current.onerror=e=>{console.error("语音转译服务连接错误:",e),V.ZP.error("语音转译服务连接错误"),N(!1)},C.current.onclose=()=>{console.log("WebSocket已关闭"),N(!1)}}catch(e){console.error("创建语音转译服务连接失败:",e),V.ZP.error("创建语音转译服务连接失败")}},D=async()=>{try{var e,t;if(_||(null==(e=C.current)?void 0:e.readyState)===WebSocket.OPEN){if(_&&(null==(t=C.current)?void 0:t.readyState)===WebSocket.OPEN){let e=iC();A.current=e;let t={header:{appkey:c.appKey,namespace:"SpeechTranscriber",name:"StartTranscription",task_id:e,message_id:iC()},payload:{format:"pcm",sample_rate:16e3,enable_intermediate_result:!0,enable_punctuation_prediction:!0,enable_inverse_text_normalization:!0}};C.current.send(JSON.stringify(t))}}else H();S.current="";let n=await navigator.mediaDevices.getUserMedia({audio:!0});R(n),P(!0),w(!0),k.current=new(window.AudioContext||window.webkitAudioContext)({sampleRate:16e3}),z(n)}catch(e){console.error("开始录音失败:",e),V.ZP.error("开始录音失败"),U()}},z=e=>{if(k.current)try{O.current=k.current.createMediaStreamSource(e);let t=k.current.createScriptProcessor(2048,1,1);t.onaudioprocess=e=>{var t;if((null==(t=C.current)?void 0:t.readyState)===WebSocket.OPEN){let t=e.inputBuffer.getChannelData(0),n=new Int16Array(t.length);for(let e=0;e<t.length;++e)n[e]=32767*Math.max(-1,Math.min(1,t[e]));C.current.send(n.buffer)}},O.current.connect(t),t.connect(k.current.destination),E.current=t}catch(e){console.error("音频处理初始化失败:",e),V.ZP.error("音频处理初始化失败"),U()}},U=()=>{var e;E.current&&(E.current.disconnect(),E.current=null),O.current&&(O.current.disconnect(),O.current=null),k.current&&(k.current.close().catch(console.error),k.current=null),T&&(T.getTracks().forEach(e=>e.stop()),R(null)),(null==(e=C.current)?void 0:e.readyState)===WebSocket.OPEN&&C.current.close(),P(!1),w(!1)};(0,j.useEffect)(()=>()=>{C.current&&(C.current.close(),C.current=null),U()},[]);let G={width:"100%",height:"40px",background:"rgba(255, 255, 255, 0.7)",borderRadius:"6px",padding:"2px 8px",overflow:"hidden",border:"1px solid #dbe9ff",display:"flex",alignItems:"center",justifyContent:"center"};return(0,b.jsxs)("div",{ref:L,style:{position:"relative"},children:[M&&(0,b.jsxs)("div",{ref:F,style:{width:"100%",maxWidth:"100%",background:"linear-gradient(to bottom, #f0f7ff, #e6f1ff)",padding:"16px",borderRadius:"12px 12px 0 0",display:"flex",flexDirection:"column",alignItems:"center",boxShadow:"0 -2px 6px rgba(0, 0, 0, 0.03)",borderBottom:"1px solid #e1edff",transition:"all 0.3s ease",position:"relative",overflow:"hidden",boxSizing:"border-box"},children:[(0,b.jsxs)("div",{style:{width:"100%",display:"flex",alignItems:"center",justifyContent:"space-between",marginBottom:"8px"},children:[(0,b.jsxs)("div",{style:{display:"flex",alignItems:"center"},children:[(0,b.jsx)("div",{style:{width:"8px",height:"8px",borderRadius:"50%",backgroundColor:"#4096ff",animation:"pulse 2s infinite ease-in-out",marginRight:"8px"}}),(0,b.jsx)(im.Z,{style:{color:"#4096ff",marginRight:"6px",fontSize:"14px"}}),(0,b.jsx)(iy.Z.Text,{style:{fontSize:"13px",fontWeight:500,color:"#4096ff"},children:"正在实时语音识别中..."})]}),(0,b.jsx)(W.ZP,{type:"primary",danger:!0,size:"small",icon:(0,b.jsx)("span",{style:{fontSize:"10px",display:"flex",alignItems:"center",justifyContent:"center"},children:"■"}),onClick:()=>{U()},title:"停止录音",style:{width:"28px",height:"28px",padding:0,display:"flex",alignItems:"center",justifyContent:"center",borderRadius:"50%",boxShadow:"0 2px 4px rgba(220, 0, 0, 0.15)"}})]}),(0,b.jsx)(()=>(0,b.jsx)("div",{style:G,children:(0,b.jsx)("div",{style:{width:"100%",height:"100%",display:"flex",alignItems:"center"},children:(0,b.jsx)(ix,{stream:T,isRecording:M,barWidth:2,barGap:2,height:30,barColor:"#4096ff"})})}),{})]}),(0,b.jsx)(ig.Z,{ref:I,allowSpeech:null!=l&&!!l.speech_to_text.enabled&&{recording:M,onRecordingChange:async e=>{e?D():U(),P(e)}},value:u,onChange:e=>{d(e)},prefix:(null==l||null==(t=l.file_upload)?void 0:t.enabled)?(0,b.jsx)(ib.Z,{dot:h.length>0&&!f,children:(0,b.jsx)(W.ZP,{onClick:()=>p(!f),icon:(0,b.jsx)(iv.Z,{})})}):null,style:{boxShadow:M?"none":"0px -2px 12px 4px #efefef",borderTopLeftRadius:M?0:void 0,borderTopRightRadius:M?0:void 0,borderTop:M?"none":void 0,transition:"all 0.3s ease",width:"100%"},loading:n,disabled:x&&!c,className:o,onSubmit:async e=>e?(null==h?void 0:h.length)&&!h.every(e=>"done"===e.status)?void V.ZP.error("请等待所有文件上传完成"):void(await r(e,{files:(null==h?void 0:h.map(e=>{let t=iw(e.name);return{...e,type:t||"document",transfer_method:"local_file",upload_file_id:v.get(e.uid)}}))||[]}),d(""),m([]),p(!1),R(null),w(!1),y.current=[],S.current=""):void V.ZP.error("内容不能为空"),onCancel:a,actions:(e,t)=>{let{SendButton:n,LoadingButton:r,ClearButton:o,SpeechButton:a}=t.components;return(0,b.jsx)(er.Z,{size:"small",children:x&&!c?(0,b.jsxs)(b.Fragment,{children:[(0,b.jsx)(iy.Z.Text,{type:"secondary",children:(0,b.jsx)("small",{children:"正在将语音转为文本, 请稍等..."})}),(0,b.jsx)(r,{icon:(0,b.jsx)(B.Z,{size:"small"}),disabled:!1,onClick:()=>{w(!1),d(""),y.current=[],T&&(T.getTracks().forEach(e=>e.stop()),R(null)),V.ZP.info("已取消语音识别, 您可以重新录入语音")}})]}):(0,b.jsxs)(b.Fragment,{children:[!M&&(0,b.jsx)(a,{}),(0,b.jsx)(n,{type:"primary",disabled:M&&!c})]})})}}),(0,b.jsx)("style",{children:`
				@keyframes pulse {
					0% {
						box-shadow: 0 0 0 0 rgba(64, 150, 255, 0.5);
					}
					70% {
						box-shadow: 0 0 0 4px rgba(64, 150, 255, 0);
					}
					100% {
						box-shadow: 0 0 0 0 rgba(64, 150, 255, 0);
					}
				}
			`})]})};var iE=n(37061),iO=n(61100),i_=n(7790),iN=n(66642),iA=n(83191);n(96141);var iM=n(13224),iP=n(12599),iT=n(6785),iR=n(96858),iI=n(9722),iL=n(30322),iF=n(92445),i$=(n(27276),n(259)),iZ=n(30781),iH=n(45709),iD=n(17118),iB=n(4527),iz=n(93926);s=null,"undefined"!=typeof window&&(s=iZ.Z.mermaidAPI);let iW=e=>{let t=new Blob([new TextEncoder().encode(e)],{type:"image/svg+xml;charset=utf-8"});return new Promise((e,n)=>{let r=new FileReader;r.onloadend=()=>e(r.result),r.onerror=n,r.readAsDataURL(t)})},iU=e=>{let{ref:t,...n}=e,[r,o]=(0,j.useState)(null),[a,i]=(0,j.useState)("classic"),l=(0,iH.Z)(n.PrimitiveCode),[c,u]=(0,j.useState)(!0),d=(0,j.useRef)(0),[f,p]=(0,j.useState)(""),[h,m]=(0,j.useState)(""),v=(0,j.useCallback)(async e=>{o(null),u(!0);try{if("undefined"!=typeof window&&s){let t=await s.render("flowchart",e),n=await iW(t.svg.replaceAll("<br>","<br/>"));o(n),u(!1)}}catch(e){l===n.PrimitiveCode&&(u(!1),p(e.message))}},[n.PrimitiveCode]);return(0,j.useEffect)(()=>{"undefined"!=typeof window&&(iZ.Z.initialize({startOnLoad:!0,theme:"neutral",look:a,flowchart:{htmlLabels:!0,useMaxWidth:!0}}),v(n.PrimitiveCode))},[a]),(0,j.useEffect)(()=>{d.current&&window.clearTimeout(d.current),d.current=window.setTimeout(()=>{v(n.PrimitiveCode)},300)},[n.PrimitiveCode]),(0,b.jsxs)("div",{ref:t,children:[(0,b.jsx)("div",{className:"msh-segmented msh-segmented-sm css-23bs09 css-var-r1",children:(0,b.jsx)("div",{className:"msh-segmented-group",children:(0,b.jsx)("label",{className:"msh-segmented-item m-2 flex w-[200px] items-center space-x-1",children:(0,b.jsxs)(iz.ZP.Group,{value:a,buttonStyle:"solid",optionType:"button",onChange:e=>{"handDrawn"===e.target.value?i("handDrawn"):i("classic")},children:[(0,b.jsx)(iz.ZP,{value:"classic",children:"经典"}),(0,b.jsx)(iz.ZP,{value:"handDrawn",children:"手绘"})]})})})}),r&&(0,b.jsx)("div",{className:"mermaid object-fit: cover h-auto w-full cursor-pointer",onClick:()=>m(r),children:r&&(0,b.jsx)("img",{src:r,alt:"mermaid_chart"})}),c&&(0,b.jsx)("div",{className:"px-[26px] py-4",children:(0,b.jsx)(iB.Z,{})}),f&&(0,b.jsxs)("div",{className:"px-[26px] py-4",children:[(0,b.jsx)(iD.Z,{className:"h-6 w-6 text-red-500"}),"\xa0",f]})]})};iU.displayName="Flowchart";let iG=e=>{var t;return"string"==typeof e?e.includes("[ENDTHINKFLAG]"):Array.isArray(e)?e.some(e=>iG(e)):null!=e&&null!=(t=e.props)&&!!t.children&&iG(e.props.children)},iK=e=>{var t;return"string"==typeof e?e.replace("[ENDTHINKFLAG]",""):Array.isArray(e)?e.map(e=>iK(e)):(null==e||null==(t=e.props)?void 0:t.children)?j.cloneElement(e,{...e.props,children:iK(e.props.children)}):e},iq=e=>{let[t]=(0,j.useState)(Date.now()),[n,r]=(0,j.useState)(0),[o,a]=(0,j.useState)(!1),i=(0,j.useRef)();return(0,j.useEffect)(()=>(i.current=setInterval(()=>{o||r(Math.floor((Date.now()-t)/100)/10)},100),()=>{i.current&&clearInterval(i.current)}),[t,o]),(0,j.useEffect)(()=>{iG(e)&&(a(!0),i.current&&clearInterval(i.current))},[e]),{elapsedTime:n,isComplete:o}},iX=e=>{let{children:t,...n}=e,{elapsedTime:r,isComplete:o}=iq(t),a=iK(t);return n["data-think"]?(0,b.jsxs)("details",{...!o&&{open:!0},className:"group",children:[(0,b.jsx)("summary",{className:"flex cursor-pointer select-none list-none items-center whitespace-nowrap font-bold text-gray-500",children:(0,b.jsxs)("div",{className:"flex shrink-0 items-center",children:[(0,b.jsx)("svg",{className:"mr-2 h-3 w-3 transition-transform duration-500 group-open:rotate-90",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,b.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})}),o?`已深度思考(${r.toFixed(1)}s)`:`深度思考中...(${r.toFixed(1)}s)`]})}),(0,b.jsx)("div",{className:"border-l mt-1 rounded-lg border-gray-300 text-gray-500 p-3 bg-gray-50",children:a})]}):(0,b.jsx)("details",{...n,children:t})};var iV=n(69267),iY=n(12737);let iQ=e=>{let{content:t}=e,n=(0,j.useRef)(null),[r,o]=(0,j.useState)(""),[a,i]=(0,j.useState)({width:"undefined"!=typeof window?window.innerWidth:0,height:"undefined"!=typeof window?window.innerHeight:0}),s=e=>{let t=new XMLSerializer().serializeToString(e),n=Buffer.from(t).toString("base64");return`data:image/svg+xml;base64,${n}`};return(0,j.useEffect)(()=>{let e=()=>{i({width:window.innerWidth,height:window.innerHeight})};return window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]),(0,j.useEffect)(()=>{if(n.current)try{n.current.innerHTML="";let e=(0,iV.Wj)().addTo(n.current),r=new DOMParser().parseFromString(t,"image/svg+xml").documentElement;if(!(r instanceof SVGElement))throw Error("Invalid SVG content");let a=Number.parseInt(r.getAttribute("width")||"400",10),i=Number.parseInt(r.getAttribute("height")||"600",10);e.viewbox(0,0,a,i),n.current.style.width=`${Math.min(a,298)}px`,e.svg(iY.Z.sanitize(t)).click(()=>{o(s(r))})}catch(e){n.current&&(n.current.innerHTML='<span style="padding: 1rem;">Error rendering SVG. Wait for the image content to complete.</span>')}},[t,a]),(0,b.jsx)(b.Fragment,{children:(0,b.jsx)("div",{ref:n,style:{maxHeight:"80vh",display:"flex",justifyContent:"center",alignItems:"center",cursor:"pointer",wordBreak:"break-word",whiteSpace:"normal",margin:"0 auto"}})})};var iJ=n(97727),i0=n(53075),i1=n(86679),i2=n(1274),i5=n(29981),i4=n.n(i5),i6=n(17813),i3=n(56366),i8=n(75510);let i7=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,i8.m6)(i4()(t))};(0,i6.Z)({html:!0,breaks:!0}).use(i2.Z).use(i3.Z,{delimiters:[{left:"\\[",right:"\\]",display:!0},{left:"\\(",right:"\\)",display:!1},{left:"$$",right:"$$",display:!1}]});let i9=e=>{let{isSVG:t,setIsSVG:n}=e;return(0,b.jsx)(W.ZP,{onClick:()=>{n(e=>!e)},children:(0,b.jsx)("div",{className:i7("h-4 w-4"),children:(0,b.jsx)(i1.Z,{})})})};var se=((m=se||{}).TEXT="text",m.PASSWORD="password",m.EMAIL="email",m.NUMBER="number",m.DATE="date",m.TIME="time",m.DATETIME="datetime",m.CHECKBOX="checkbox",m.SELECT="select",m);let st=e=>{let{node:t,onSend:n}=e,[r,o]=(0,j.useState)({});(0,j.useEffect)(()=>{let e={};t.children.forEach(t=>{["input","textarea"].includes(t.tagName)&&(e[t.properties.name]=t.properties.value)}),o(e)},[t.children]);let a=e=>{let t={};return e.forEach(e=>{["input","textarea"].includes(e.tagName)&&(t[e.properties.name]=r[e.properties.name])}),t},i=e=>{e.preventDefault();let r=t.properties.dataFormat||"text",o=a(t.children);if("json"===r)console.log("即将发送",r,o),null==n||n(JSON.stringify({...o,isFormSubmit:!0}));else{let e=Object.entries(o).map(e=>{let[t,n]=e;return`${t}: ${n}`}).join("\n");null==n||n(e)}};return(0,b.jsx)("form",{autoComplete:"off",className:"flex flex-col self-stretch pb-3",onSubmit:e=>{e.preventDefault(),e.stopPropagation()},children:t.children.filter(e=>"element"===e.type).map((e,t)=>{var n,a;if("label"===e.tagName)return(0,b.jsx)("label",{htmlFor:e.properties.for,className:"system-md-semibold my-2 text-text-secondary",children:(null==(n=e.children[0])?void 0:n.value)||""},t);if("input"===e.tagName&&Object.values(se).includes(e.properties.type))return(0,b.jsx)(it.Z,{type:e.properties.type,name:e.properties.name,placeholder:e.properties.placeholder,value:r[e.properties.name],onChange:t=>{o(n=>({...n,[e.properties.name]:t.target.value}))}},t);if("textarea"===e.tagName)return(0,b.jsx)(it.Z.TextArea,{name:e.properties.name,placeholder:e.properties.placeholder,value:r[e.properties.name],onChange:t=>{o(n=>({...n,[e.properties.name]:t.target.value}))}},t);if("button"===e.tagName){let n=e.properties.dataVariant;return e.properties.dataSize,(0,b.jsx)(W.ZP,{type:"primary",variant:n,className:"mt-4",onClick:i,children:(null==(a=e.children[0])?void 0:a.value)||""},t)}return(0,b.jsxs)("p",{children:["Unsupported tag: ",e.tagName]},t)})})};st.displayName="MarkdownForm";let sn={sql:"SQL",javascript:"JavaScript",java:"Java",typescript:"TypeScript",vbscript:"VBScript",css:"CSS",html:"HTML",xml:"XML",php:"PHP",python:"Python",yaml:"Yaml",mermaid:"Mermaid",markdown:"MarkDown",makefile:"MakeFile",echarts:"ECharts",shell:"Shell",powershell:"PowerShell",json:"JSON",latex:"Latex",svg:"SVG"},sr=e=>e?e in sn?sn[e]:e.charAt(0).toUpperCase()+e.substring(1):"Plain",so=e=>{if("string"!=typeof e)return e;let t=/```[\s\S]*?```/g,n=e.match(t)||[],r=e.replace(t,"CODE_BLOCK_PLACEHOLDER");return r=(0,i$.Z)([e=>e.replace(/\\\[(.*?)\\\]/g,(e,t)=>`$$${t}$$`),e=>e.replace(/\\\[([\s\S]*?)\\\]/g,(e,t)=>`$$${t}$$`),e=>e.replace(/\\\((.*?)\\\)/g,(e,t)=>`$$${t}$$`),e=>e.replace(/(^|[^\\])\$(.+?)\$/g,(e,t,n)=>`${t}$${n}$`)])(r),n.forEach(e=>{r=r.replace("CODE_BLOCK_PLACEHOLDER",e)}),r},sa=e=>(0,i$.Z)([e=>e.replace("<think>\n","<details data-think=true>\n"),e=>e.replace("\n</think>","\n[ENDTHINKFLAG]</details>")])(e),si=(0,j.memo)(e=>{let{inline:t,className:n,children:r,...o}=e,[a,i]=(0,j.useState)(!0),s=/language-(\w+)/.exec(n||""),l=null==s?void 0:s[1],c=sr(l||""),u=(0,j.useMemo)(()=>{if("echarts"===l)try{return JSON.parse(String(r).replace(/\n$/,""))}catch(e){}return JSON.parse('{"title":{"text":"ECharts error - Wrong JSON format."}}')},[l,r]),d=(0,j.useMemo)(()=>{let e=String(r).replace(/\n$/,"");return"mermaid"===l&&a?(0,b.jsx)(iU,{PrimitiveCode:e}):"echarts"===l?(0,b.jsx)("div",{style:{minHeight:"350px",minWidth:"100%",overflowX:"scroll"},children:(0,b.jsx)(sf,{children:(0,b.jsx)(iA.Z,{option:u,style:{minWidth:"700px"}})})}):"svg"===l&&a?(0,b.jsx)(sf,{children:(0,b.jsx)(iQ,{content:e})}):(0,b.jsx)(iL.Z,{...o,style:iF.Z,customStyle:{paddingLeft:12,borderBottomLeftRadius:"10px",borderBottomRightRadius:"10px",backgroundColor:"var(--color-components-input-bg-normal)"},language:null==s?void 0:s[1],showLineNumbers:!0,PreTag:"div",children:e})},[l,s,o,r,u,a]);return t||!s?(0,b.jsx)("code",{...o,className:n,children:r}):(0,b.jsxs)("div",{className:"relative",children:[(0,b.jsxs)("div",{className:"flex h-8 items-center justify-between rounded-t-[10px] border-b border-divider-subtle bg-components-input-bg-normal p-1 pl-3",children:[(0,b.jsx)("div",{className:"text-gray-700",children:c}),(0,b.jsxs)("div",{className:"flex items-center gap-1",children:[["mermaid","svg"].includes(l)&&(0,b.jsx)(i9,{isSVG:a,setIsSVG:i}),(0,b.jsx)(W.ZP,{children:(0,b.jsx)(iJ.Z,{onClick:async()=>{await (0,i0.v)(String(r).replace(/\n$/,"")),V.ZP.success("复制成功")}})})]})]}),d]})});si.displayName="CodeBlock";let ss=(0,j.memo)(e=>{var t;let{node:n}=e,r=(null==(t=n.children[0])?void 0:t.value)||"";return`<script>${r}</script>`});ss.displayName="ScriptBlock";let sl=e=>{let{node:t}=e,n=t.children;return n&&n[0]&&"tagName"in n[0]&&"img"===n[0].tagName?(0,b.jsx)(b.Fragment,{children:Array.isArray(e.children)?(0,b.jsx)("p",{children:e.children.slice(1)}):null}):(0,b.jsx)("p",{children:e.children})},sc=e=>{let{src:t}=e;return(0,b.jsx)("img",{src:t})},su=e=>{var t;let{node:n,...r}=e;return(0,b.jsx)("a",{...r,target:"_blank",className:"cursor-pointer underline !decoration-primary-700 decoration-dashed",children:n.children[0]?null==(t=n.children[0])?void 0:t.value:"Download"})};function sd(e){let{onSubmit:t}=e,n=(0,i$.Z)([sa,so])(e.markdownText);return(0,b.jsx)("div",{className:"text-default",children:(0,b.jsx)(iN.UG,{remarkPlugins:[iR.Z,[iM.Z,{singleDollarTextMath:!1}],iP.Z],rehypePlugins:[iT.Z,iI.Z,()=>e=>{let t=e=>{var n;"element"===e.type&&(null==(n=e.properties)?void 0:n.ref)&&delete e.properties.ref,"element"!==e.type||/^[a-z][a-z0-9]*$/i.test(e.tagName)||(e.type="text",e.value=`<${e.tagName}`),e.children&&e.children.forEach(t)};e.children.forEach(t)}],disallowedElements:["iframe","head","html","meta","link","style","body",...e.customDisallowedElements||[]],components:{code:si,img:sc,a:su,p:sl,form:e=>(0,b.jsx)(st,{...e,onSend:e=>{t(e)}}),script:ss,details:iX},children:n})})}class sf extends j.Component{componentDidCatch(e,t){this.setState({hasError:!0}),console.error(e,t)}render(){return this.state.hasError?(0,b.jsxs)("div",{children:["Oops! An error occurred. This could be due to an ECharts runtime error or invalid SVG content. ",(0,b.jsx)("br",{}),"(see the browser console for more information)"]}):this.props.children}constructor(e){super(e),this.state={hasError:!1}}}var sp=n(41077),sh=n(36822),sm=n(1219);function sv(e){let{text:t}=e;return t?(0,b.jsx)("pre",{className:"!m-0 !p-0 !bg-white !border-none",children:t}):"空"}function sg(e){let{uniqueKey:t,items:n,className:r}=e;if(!(null==n?void 0:n.length))return null;let o=n.map(e=>({title:(0,b.jsx)("div",{className:"text-base",children:e.tool?`已使用 ${e.tool}`:"暂无标题"}),status:"success",icon:(0,b.jsx)(sp.Z,{}),description:(0,b.jsxs)(b.Fragment,{children:[(0,b.jsx)(sm.Z,{className:"mt-3 min-w-chat-card",size:"small",items:[{key:`${t}-tool_input`,label:"输入",children:(0,b.jsx)(sv,{text:e.tool_input})},{key:`${t}-observation`,label:"输出",children:(0,b.jsx)(sv,{text:e.observation})}]}),(0,b.jsx)("pre",{className:"border-none",children:e.thought})]})}));return(null==o?void 0:o.length)?(0,b.jsx)(sh.Z,{className:r,items:o}):null}var sy=n(13564),sb=n(75835),sx=n(50325);function sj(e){let{files:t}=e;return(null==t?void 0:t.length)?t.every(e=>"image"===e.type)?(0,b.jsx)("div",{className:"flex flex-wrap",children:t.map(e=>(0,b.jsx)(sx.TV,{children:(0,b.jsx)(sx.HI,{src:e.url,children:(0,b.jsx)("img",{src:e.url,alt:e.filename,className:"w-24 h-24 cursor-zoom-in mr-2 rounded-lg",style:{objectFit:"cover"}},e.id)})},e.id))}):(0,b.jsx)(b.Fragment,{children:t.map(e=>(0,b.jsxs)("a",{title:"点击下载文件",href:e.url,target:"_blank",rel:"noreferrer",className:"p-3 bg-gray-50 rounded-lg w-60 flex items-center cursor-pointer no-underline mb-2",children:["image"===e.type?(0,b.jsx)(sy.Z,{className:"text-3xl text-gray-400 mr-2"}):(0,b.jsx)(sb.Z,{className:"text-3xl text-gray-400 mr-2"}),(0,b.jsxs)("div",{className:"overflow-hidden",children:[(0,b.jsx)("div",{className:"text-default truncate",children:e.filename}),e.size?(0,b.jsx)("div",{className:"text-desc truncate",children:iS(e.size)}):null]})]},e.id))}):null}n(30267);var sw=n(74804),sS=n(7257),sC=n(94956),sk=n(63732),sE=n(32092),sO=n(49684),s_=n(20819),sN=n(58064);let sA=e=>(0,b.jsx)(i_.Z,{title:e.title,arrow:!0,children:(0,b.jsxs)("div",{className:"flex items-center",children:[e.icon,(0,b.jsx)("span",{className:"ml-1 flex-1 truncate",children:e.value})]})}),sM=e=>{let t=L();return(0,b.jsx)("div",{className:"text-gray-600 flex items-center",title:e.name,children:(0,b.jsxs)(s_.Z,{trigger:["click"],classNames:{root:"max-w-[85vw] md:max-w-[50vw]",body:"max-h-[50vh] overflow-y-auto overflow-x-hidden"},title:(0,b.jsx)("div",{title:e.name,className:"w-full truncate",children:e.name}),placement:t?"top":"topLeft",content:(0,b.jsx)(er.Z,{className:"w-full overflow-hidden",split:(0,b.jsx)(sN.Z,{style:{margin:"10px 0"},variant:"dotted",type:"horizontal"}),direction:"vertical",children:e.items.map(e=>{var t;return(0,b.jsxs)("div",{className:"w-full overflow-hidden",children:[(0,b.jsx)("div",{className:"flex items-center justify-between",children:(0,b.jsx)("div",{className:"flex items-center",children:(0,b.jsxs)("span",{className:"text-desc",children:["#",e.segment_position]})})}),(0,b.jsxs)("div",{className:"mt-2 w-full overflow-hidden",children:[(0,b.jsx)("div",{children:e.content}),(0,b.jsx)(er.Z,{size:"middle",className:"flex items-center text-desc mt-2 flex-wrap w-full overflow-hidden",children:[{id:`${e.segment_id}_word_count`,icon:(0,b.jsx)(sw.Z,{}),title:`字符: ${e.word_count}`,value:e.word_count,visible:!!e.word_count},{id:`${e.segment_id}_hit_count`,icon:(0,b.jsx)(sS.Z,{}),title:`召回次数: ${e.hit_count}`,value:e.hit_count,visible:!!e.hit_count},{id:`${e.segment_id}_index_node_hash`,icon:(0,b.jsx)(sC.Z,{}),title:`向量哈希: ${e.index_node_hash}`,value:null==(t=e.index_node_hash)?void 0:t.substring(0,7),visible:!!e.index_node_hash},{id:`${e.segment_id}_score`,icon:(0,b.jsx)(sk.Z,{}),title:`召回得分: ${e.score}`,value:e.score,visible:!!e.score}].filter(e=>e.visible).map(e=>(0,b.jsx)(sA,{icon:e.icon,title:e.title,value:e.value},e.id))})]})]},e.id)})}),children:["website_crawl"===e.data_source_type?(0,b.jsx)(sE.Z,{}):"upload_file"===e.data_source_type?(0,b.jsx)(sO.Z,{}):null,(0,b.jsx)("span",{className:"cursor-pointer hover:underline ml-1",children:e.name})]})})};function sP(e){let{items:t}=e;if(!(null==t?void 0:t.length))return null;let n=t.reduce((e,t)=>{let n=t.document_id,r=e.find(e=>e.id===n);return r?r.items.push(t):e.push({id:t.document_id,name:t.document_name,data_source_type:t.data_source_type,items:[t]}),e},[]);return(0,b.jsxs)("div",{className:"pb-3",children:[(0,b.jsxs)("div",{className:"flex items-center text-gray-400",children:[(0,b.jsx)("span",{className:"mr-3 text-sm",children:"引用"}),(0,b.jsx)("div",{className:"flex-1 border-gray-400 border-dashed border-0 border-t h-0"})]}),n.map(e=>(0,b.jsx)("div",{className:"mt-2 truncate",children:(0,b.jsx)(sM,{...e})},e.id))]})}function sT(e){var t;let{appConfig:n,onSubmit:r,messageItem:{id:o,status:a,error:i,agentThoughts:s,workflows:l,files:c,content:u,retrieverResources:d,role:f}}=e,p=(0,j.useMemo)(()=>{let e=u.startsWith("{")&&u.endsWith("}");if("local"===f||"user"===f&&e){var t,r,o;if((null==(t=n.answerForm)?void 0:t.enabled)&&(null==(r=n.answerForm)?void 0:r.feedbackText))try{return JSON.parse(u).isFormSubmit?null==(o=n.answerForm)?void 0:o.feedbackText:u}catch(e){console.log("computedContent json 解析失败",e)}}return u},[u,null==n?void 0:n.answerForm,f]);return"error"===a?(0,b.jsxs)("p",{className:"text-red-700",children:[(0,b.jsx)(iE.Z,{className:"mr-2"}),(0,b.jsx)("span",{children:i})]}):"success"!==a||u||(null==c?void 0:c.length)||(null==s?void 0:s.length)||(null==l||null==(t=l.nodes)?void 0:t.length)||(null==d?void 0:d.length)?(0,b.jsxs)(b.Fragment,{children:[(0,b.jsx)(sg,{uniqueKey:o,items:s,className:"mt-3"}),(null==c?void 0:c.length)?(0,b.jsx)("div",{className:"mt-3",children:(0,b.jsx)(sj,{files:c})}):null,(0,b.jsx)("div",{className:"local"===f||"user"===f?"":"md:min-w-chat-card",children:(0,b.jsx)(sd,{markdownText:p,onSubmit:r})}),(0,b.jsx)(sP,{items:d})]}):(0,b.jsxs)("p",{className:"text-orange-600",children:[(0,b.jsx)(iE.Z,{className:"mr-2"}),(0,b.jsx)("span",{children:"消息内容为空"}),(0,b.jsx)(i_.Z,{title:"可能是用户在生成内容的过程中点击了停止响应按钮",children:(0,b.jsx)(iO.Z,{className:"ml-2"})})]})}var sR=n(68562),sI=n(58264),sL=n(89409),sF=n(42142),s$=n(25146),sZ=n(16017);function sH(e){let{icon:t,loading:n=!1,active:r=!1,onClick:o}=e,a=j.cloneElement(t,{className:r?"text-primary":""});return(0,b.jsxs)("div",{className:"relative",children:[(0,b.jsx)(W.ZP,{color:"default",variant:"text",size:"small",icon:a,onClick:o}),(0,b.jsx)(B.Z,{className:"absolute left-0 top-0 w-full h-full",spinning:n})]})}function sD(e){let{messageId:t,messageContent:n,feedback:{rating:r,callback:o},feedbackApi:a,ttsApi:i,ttsConfig:s}=e,l="like"===r,c="dislike"===r,[u,d]=(0,sZ.Z)({like:!1,dislike:!1}),[f,p]=(0,j.useState)(!1),[h,m]=(0,j.useState)(""),{runAsync:v}=(0,X.Z)(e=>a({messageId:t.replace("-answer",""),rating:e,content:""}),{manual:!0,onSuccess(){V.ZP.success("操作成功"),null==o||o()},onFinally(){d({like:!1,dislike:!1})}}),g=async e=>{let t=new Audio;t.src=e,t.play(),p(!0),t.addEventListener("ended",()=>{p(!1)})},{runAsync:y,loading:x}=(0,X.Z)(e=>i({text:e}).then(e=>e.blob()).then(e=>{let t=URL.createObjectURL(e);m(t),g(t)}),{manual:!0}),w=[{icon:(0,b.jsx)(sR.Z,{}),hidden:!0},{icon:(0,b.jsx)(iJ.Z,{}),onClick:async()=>{await (0,i0.v)(n),V.ZP.success("复制成功")},active:!1,loading:!1,hidden:!1},{icon:(0,b.jsx)(sI.Z,{}),onClick:()=>{d({like:!0}),v(l?null:"like")},active:l,loading:u.like,hidden:!1},{icon:(0,b.jsx)(sL.Z,{}),onClick:()=>{d({dislike:!0}),v(c?null:"dislike")},active:c,loading:u.dislike,hidden:!1},{icon:f?(0,b.jsx)(sF.Z,{}):(0,b.jsx)(s$.Z,{}),onClick:()=>{h?g(h):y(n)},active:f,loading:x,hidden:!(null==s?void 0:s.enabled)}];return(0,b.jsx)(er.Z,{children:w.map((e,t)=>!e.hidden&&(0,b.jsx)(sH,{icon:e.icon,onClick:e.onClick,active:e.active,loading:e.loading},t))})}var sB=n(54363),sz=n(45940),sW=n(70376),sU=n(59322),sG=n(46378),sK=n(11470),sq=n(52345),sX=n(75391),sV=n(71282),sY=n(84055),sQ=n(5324),sJ=n(83665);let s0=["text-input","select","number","paragraph"];function s1(e){let{user_input_form:t,conversationId:n,entryForm:r}=e,{currentConversationId:o,setConversations:a,currentConversationInfo:i}=k(),s=(0,D.k6)(),{appId:l}=(0,D.UO)(),c=(0,D.lr)(),[u,d]=(0,j.useState)([]),f=(0,j.useRef)(new URLSearchParams(c)),{mode:p}=A();return(0,j.useEffect)(()=>{r.resetFields()},[n]),(0,j.useEffect)(()=>{if(!(null==t?void 0:t.length))return void d([]);if(d((null==t?void 0:t.map(e=>{let t=Object.keys(e)[0],n=Object.values(e)[0],o={type:t,label:n.label,name:n.variable,options:n.options,max_length:n.max_length},a=f.current.get(n.variable);return a&&(r.setFieldValue(n.variable,R(a)),f.current.delete(n.variable)),n.required&&(o.required=!0,o.rules=[{required:!0,message:"请输入"}]),o}))||[]),c.size!==f.current.size){f.current.has("isNewCvst")&&f.current.delete("isNewCvst");let e=f.current.size?`?${f.current.toString()}`:"";"multiApp"===p?s.push(`/app/${l}${e}`):s.push(`/chat${e}`)}let e=(null==i?void 0:i.inputs)||{};e&&"object"==typeof e&&!Array.isArray(e)&&Object.keys(e).forEach(t=>{let n=e[t];void 0!==n&&r.setFieldValue(t,n)})},[t]),console.log("user_input_form",t),(0,b.jsx)(b.Fragment,{children:(null==t?void 0:t.length)?(0,b.jsx)(J.Z,{form:r,className:"mt-6",labelCol:{span:5},onValuesChange:(e,t)=>{a(e=>(console.log("setConversations: onValuesChange",e.map(e=>e.id===o?{...e,inputs:t}:e)),e.map(e=>e.id===o?{...e,inputs:t}:e)))},children:u.filter(e=>s0.includes(e.type)).map(e=>{var t;return(0,b.jsx)(J.Z.Item,{name:e.name,label:e.label,required:e.required,rules:e.rules,children:"text-input"===e.type?(0,b.jsx)(it.Z,{placeholder:"请输入",maxLength:e.max_length}):"select"===e.type?(0,b.jsx)(ir.Z,{placeholder:"请选择",options:(null==(t=e.options)?void 0:t.map(e=>({value:e,label:e})))||[]}):"paragraph"===e.type?(0,b.jsx)(it.Z.TextArea,{placeholder:"请输入",maxLength:e.max_length}):"number"===e.type?(0,b.jsx)(sJ.Z,{placeholder:"请输入",className:"w-full"}):`暂不支持的控件类型: ${e.type}`},e.name)})}):null})}function s2(e){var t;let{token:n}=sQ.Z.useToken(),[r,o]=(0,j.useState)([]),{conversationId:a}=e;if((0,j.useEffect)(()=>{T(a)?o(["1"]):o([])},[a]),!(null==(t=e.user_input_form)?void 0:t.length))return null;let i={color:n.colorText,borderRadius:"8px",border:"1px solid #eff0f5"};return(0,b.jsx)(sm.Z,{className:"mt-3 bg-primary",bordered:!1,activeKey:r,onChange:e=>o(e),expandIconPosition:"end",expandIcon:e=>{let{isActive:t}=e;return(0,b.jsx)(sY.Z,{rotate:90*!!t})},style:{background:n.colorBgContainer,borderColor:n.colorPrimary},items:[{key:"1",label:(0,b.jsx)("div",{className:"font-semibold text-base",children:"对话参数设置"}),children:(0,b.jsx)(s1,{formFilled:e.formFilled,onStartConversation:e.onStartConversation,user_input_form:e.user_input_form,conversationId:e.conversationId,entryForm:e.entryForm}),style:i}]})}let s5=(e,t)=>(0,b.jsxs)(er.Z,{align:"start",children:[e,(0,b.jsx)("span",{children:t})]}),s4=e=>{var t;let{onPromptItemClick:n,appParameters:r,showPrompts:o}=e,a=L(),i=(0,j.useMemo)(()=>{let e=[{key:"1",label:s5((0,b.jsx)(sB.Z,{style:{color:"#FF4D4F"}}),"Hot Topics"),description:"What are you interested in?",children:[{key:"1-1",description:"What's new in X?"},{key:"1-2",description:"What's AGI?"},{key:"1-3",description:"Where is the doc?"}]},{key:"2",label:s5((0,b.jsx)(sz.Z,{style:{color:"#1890FF"}}),"Design Guide"),description:"How to design a good product?",children:[{key:"2-1",icon:(0,b.jsx)(sW.Z,{}),description:"Know the well"},{key:"2-2",icon:(0,b.jsx)(sU.Z,{}),description:"Set the AI role"},{key:"2-3",icon:(0,b.jsx)(sG.Z,{}),description:"Express the feeling"}]}];return(null==r?void 0:r.suggested_questions)?[{key:"remote",label:s5((0,b.jsx)(sB.Z,{style:{color:"#FF4D4F"}}),"Hot Topics"),description:"What are you interested in?",children:r.suggested_questions.map((e,t)=>({key:"remote"+t,description:e}))}]:a?e.slice(0,1):e},[a]);return(0,b.jsx)("div",{className:"flex justify-center w-full px-3 box-border mx-auto",children:(0,b.jsxs)(er.Z,{direction:"vertical",className:i4()({"w-full md:!w-3/4":!0,"pb-6":!o&&(null==(t=e.user_input_form)?void 0:t.length),"pt-3":o}),children:[o?(0,b.jsx)(sX.Z,{variant:"borderless",icon:(0,b.jsx)("div",{className:"flex items-center justify-center rounded-[50%] w-16 h-16 border-gray-100 border-solid border-[1px] bg-[#eff0f5]",children:(0,b.jsx)(sK.Z,{className:"text-3xl text-primary"})}),title:"Hello, I'm MedSci xAI",description:(null==r?void 0:r.opening_statement)||"MedSci Chat is a web app that can interact with AI.",extra:(0,b.jsxs)(er.Z,{children:[(0,b.jsx)(W.ZP,{icon:(0,b.jsx)(sC.Z,{})}),(0,b.jsx)(W.ZP,{icon:(0,b.jsx)(sq.Z,{})})]})}):null,(0,b.jsx)(s2,{formFilled:e.formFilled,onStartConversation:e.onStartConversation,user_input_form:e.user_input_form,entryForm:e.entryForm,conversationId:e.conversationId}),o?(0,b.jsx)(sV.Z,{className:"mt-4",title:"问一问：",vertical:a,items:i,styles:{list:{width:"100%"},item:a?{width:"100%"}:{flex:1}},onItemClick:n}):null]})})};var s6=n(68406),s3=n(10601),s8=n(20023),s7=n(29647),s9=function(e){let{info:t}=e;return(0,b.jsxs)("div",{className:"text-default pt-3",children:[(0,b.jsxs)("div",{className:"flex items-center px-4 mt-3",children:[(0,b.jsx)("div",{className:"bg-[#ffead5] rounded-lg p-2",children:(0,b.jsx)(sK.Z,{className:"text-gray-700"})}),(0,b.jsx)("div",{className:"ml-3 text-default text-sm truncate",children:t.name})]}),t.tags?(0,b.jsx)("div",{className:"mt-3 px-4",children:t.tags.map(e=>(0,b.jsx)(s7.Z,{className:"mb-2",children:e},e))}):null]})},le=e=>{let{messageItems:t,isRequesting:n,nextSuggestions:r,onPromptsItemClick:o,onSubmit:a,onCancel:i,conversationId:s,feedbackCallback:l,difyApi:c,appParameters:u,appConfig:d,isFormFilled:f,onStartConversation:p,entryForm:h}=e,m=L(),v={ai:{placement:"start",avatar:m?void 0:{icon:(0,b.jsx)("img",{src:"https://static.medsci.cn/ai-write/robot-08128bd4.png",alt:"robot"}),style:{background:"#fde3cf"}},style:m?void 0:{maxWidth:"calc(100% - 44px)"}},user:{placement:"end",avatar:m?void 0:{icon:(0,b.jsx)(ip.Z,{}),style:{background:"#87d068"}},style:m?void 0:{maxWidth:"calc(100% - 44px)",marginLeft:"44px"}}},g=(0,j.useMemo)(()=>null==t?void 0:t.map(e=>{var t;return{key:`${e.id}-${e.role}`,content:e.content,messageRender:()=>(0,b.jsx)(sT,{appConfig:d,onSubmit:a,messageItem:e}),role:"local"===e.role?"user":e.role,footer:"ai"===e.role&&(0,b.jsxs)("div",{className:"flex items-center",children:[(0,b.jsx)(sD,{ttsConfig:null==u?void 0:u.text_to_speech,feedbackApi:e=>c.feedbackMessage(e),ttsApi:e=>c.text2Audio(e),messageId:e.id,messageContent:e.content,feedback:{rating:null==(t=e.feedback)?void 0:t.rating,callback:()=>{null==l||l(s)}}}),e.created_at&&(0,b.jsxs)("div",{className:"ml-3 text-sm text-desc",children:["回复时间：",e.created_at]})]})}}),[t,s,c,l,d,a]),y=(0,j.useRef)(null),x=(0,j.useDeferredValue)(g);(0,j.useEffect)(()=>{y.current&&y.current.scrollTo({behavior:"smooth",top:y.current.scrollHeight}),(async()=>{let e=new ie;try{w.token=(await e.getAsrToken()).data}catch(e){console.error("Failed to fetch ASR token:",e)}})()},[x]);let w={appKey:"yBmYduPetKnoomrH",token:"",url:"wss://nls-gateway.cn-shanghai.aliyuncs.com/ws/v1"};return(0,b.jsx)("div",{className:"w-full h-full overflow-hidden my-0 mx-auto box-border flex flex-col gap-4 relative bg-white",children:(0,b.jsxs)("div",{className:"w-full h-full overflow-auto pt-4 pb-48",ref:y,children:[(0,b.jsx)(s4,{showPrompts:!(null==g?void 0:g.length)&&T(s),appParameters:u,onPromptItemClick:o,formFilled:f,onStartConversation:p,user_input_form:null==u?void 0:u.user_input_form,conversationId:s,entryForm:h}),(0,b.jsxs)("div",{className:"flex-1 w-full md:!w-3/4 mx-auto px-3 md:px-0 box-border",children:[(0,b.jsx)(ih.Z.List,{items:g,roles:v}),(null==r?void 0:r.length)?(0,b.jsxs)("div",{className:"p-3 md:pl-[44px] mt-3",children:[(0,b.jsx)("div",{className:"text-desc",children:"\uD83E\uDD14 你可能还想问:"}),(0,b.jsx)("div",{children:null==r?void 0:r.map(e=>(0,b.jsx)("div",{className:"mt-3 cursor-pointer",children:(0,b.jsx)("div",{className:"p-2 rounded-lg border border-solid border-[#eff0f5] inline-block text-sm",onClick:()=>{o({data:{key:e,description:e}})},children:e})},e))})]}):null]}),(0,b.jsxs)("div",{className:"absolute bottom-0 bg-white w-full md:!w-3/4 left-1/2",style:{transform:"translateX(-50%)"},children:[(0,b.jsx)("div",{className:"px-3",children:(0,b.jsx)(ik,{appParameters:u,onSubmit:async function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return await h.validateFields(),a(...t)},isRequesting:n,className:"w-full",uploadFileApi:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return c.uploadFile(...t)},audio2TextApi:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return c.audio2Text(...t)},onCancel:i,asrConfig:w})}),(0,b.jsx)("div",{className:"text-gray-400 text-xs text-center h-8 leading-8",children:"内容由 AI 生成, 仅供参考"})]})]})})},lt=e=>{let{deleteConversationPromise:t,renameConversationPromise:n,items:r,activeKey:o,onActiveChange:a}=e,[i]=J.Z.useForm(),s=async e=>{await t(e),V.ZP.success("删除成功")},l=e=>{i.setFieldsValue({name:e.label}),s8.Z.confirm({destroyOnClose:!0,title:"会话重命名",content:(0,b.jsx)(J.Z,{form:i,className:"mt-3",children:(0,b.jsx)(J.Z.Item,{name:"name",children:(0,b.jsx)(it.Z,{placeholder:"请输入"})})}),onOk:async()=>{await i.validateFields();let t=await i.validateFields();await n(e.key,t.name),V.ZP.success("会话重命名成功")}})};return(0,b.jsx)(s3.Z,{className:"p-0",items:r,activeKey:o,onActiveChange:a,menu:e=>({items:[{label:"重命名",key:"rename",icon:(0,b.jsx)(s6.Z,{})},{label:"删除",key:"delete",icon:(0,b.jsx)(Q.Z,{}),danger:!0}],onClick:async t=>{switch(t.domEvent.stopPropagation(),t.key){case"delete":await s(e.key);break;case"rename":l(e)}}})})},ln=n(81655),lr=n(16483),lo=n.n(lr);function la(e){let t=(0,j.useRef)(e);return t.current=e,t}var li=n(55704),ls=n(45904),ll=n(90631),lc=n(40833);let lu=new class{get(e){var t,n,r;let{appId:o,conversationId:a,messageId:i,key:s}=e;return console.log("workflow data storage get",e,this.data),null==(r=this.data[o])||null==(n=r[a])||null==(t=n[i])?void 0:t[s]}set(e){let{appId:t,conversationId:n,messageId:r,key:o,value:a}=e;this.data[t]||(this.data[t]={}),this.data[t][n]||(this.data[t][n]={}),this.data[t][n][r]||(this.data[t][n][r]={}),this.data[t][n][r][o]=a,console.log("workflow data storage set",e,this.data)}constructor(){(0,lc._)(this,"data",{})}},ld=n.p+"static/image/loading.6383e003.gif",lf=e=>{let{latestProps:t,latestState:n,appParameters:r,getNextSuggestions:o,filesRef:a,abortRef:i,getConversationMessages:s,onConversationIdChange:l,difyApi:c}=e,{user:u}=A(),[d,f]=(0,j.useState)(""),[p]=(0,li.Z)({request:async(e,p)=>{let{message:h}=e,{onSuccess:m,onUpdate:v,onError:g}=p,y="",b=[],x={},j=[];v({content:`正在思考<img src="${ld}" style="width: 25px; height: 25px;" />`,files:b,workflows:x,agentThoughts:j});let w=await c.sendMessage({inputs:(null==h?void 0:h.inputs)||n.current.inputParams,conversation_id:T(t.current.conversationId)?void 0:t.current.conversationId,files:a.current||[],user:u,response_mode:"streaming",query:null==h?void 0:h.content});if(200!==w.status){let e=w.statusText||"请求对话接口失败";V.ZP.error(e),i.current=()=>{g({name:w.status.toString(),message:e})},i.current();return}let S=(0,ls.Z)({readableStream:w.body}).getReader();i.current=()=>{null==S||S.cancel(),g({name:"abort",message:"用户已取消"})};let C=t.current.conversationId||"",k="",E=async()=>{if((null==h?void 0:h.content)==="▶️开始训练"&&y.includes("医生介绍")){let e=y.match(/[\u4e00-\u9fa5]+医生/),t="医生";e&&(t=e[0],console.log("医生名称：",e));let n=new Date,r=n.getMonth()+1+"/"+n.getDate();await c.renameConversation({conversation_id:C,name:r+"-"+t})}};for(;S;){let{value:e,done:n}=await S.read();if(n){E(),lu.set({appId:t.current.appId||"",conversationId:C,messageId:k,key:"workflows",value:x}),m({content:y,files:b,workflows:x,agentThoughts:j}),s(C),l(C);break}if(e)if(e.data){var O,_;let t={};try{t=JSON.parse(e.data)}catch(e){console.error("解析 JSON 失败",e)}t.task_id&&t.task_id!==d&&f(t.task_id),t.event===a7.MESSAGE_END&&(null==r?void 0:r.suggested_questions_after_answer.enabled)&&o(t.message_id);let n=t.data;if(t.event===a7.WORKFLOW_STARTED?(C=t.conversation_id,k=t.message_id,x.status="running",x.nodes=[],v({content:y,files:b,workflows:x,agentThoughts:j})):t.event===a7.WORKFLOW_FINISHED?(console.log("工作流结束",t),x.status="finished",v({content:y,files:b,workflows:x,agentThoughts:j})):t.event===a7.WORKFLOW_NODE_STARTED?(console.log("节点开始",t),x.nodes=[...x.nodes||[],{id:n.id,status:"running",type:n.node_type,title:n.title}],v({content:`正在思考中${null==(O=t.data)?void 0:O.index} <img src="${ld}" class="think-loading" style="height: 25px;width: 25px;" />`,files:b,workflows:x,agentThoughts:j})):t.event===a7.WORKFLOW_NODE_FINISHED&&(x.nodes=null==(_=x.nodes)?void 0:_.map(e=>e.id===n.id?{...e,status:"success",inputs:n.inputs,outputs:n.outputs,process_data:n.process_data,elapsed_time:n.elapsed_time,execution_metadata:n.execution_metadata}:e),v({content:y,files:b,workflows:x,agentThoughts:j})),t.event===a7.MESSAGE_FILE&&v({content:y+=`<img src="${t.url}" />`,files:b,workflows:x,agentThoughts:j}),t.event===a7.MESSAGE){let e=t.answer;v({content:y+=e,files:b,workflows:x,agentThoughts:j})}if(t.event===a7.ERROR&&(g({name:`${t.status}: ${t.code}`,message:t.message}),s(t.conversation_id)),t.event===a7.AGENT_MESSAGE){let e=j[j.length-1];if(!e)continue;{let n=t.answer;e.thought+=n}v({content:y,files:b,workflows:x,agentThoughts:j})}if(t.event===a7.AGENT_THOUGHT){let e=j.findIndex(e=>e.position===t.position),n={conversation_id:t.conversation_id,id:t.id,task_id:t.task_id,position:t.position,tool:t.tool,tool_input:t.tool_input,observation:t.observation,message_files:t.message_files,message_id:t.message_id};-1!==e?j[e]=n:j.push(n),v({content:y,files:b,workflows:x,agentThoughts:j})}}else{console.log("没有数据",e);continue}}}}),{onRequest:h,messages:m,setMessages:v}=(0,ll.Z)({agent:p});return{agent:p,onRequest:h,messages:m,setMessages:v,currentTaskId:d}};function lp(e){let{appConfig:t,appInfo:n,appParameters:r,difyApi:o,conversationListLoading:a,onAddConversation:i,conversationItemsChangeCallback:s,appConfigLoading:l,handleStartConfig:c}=e,{currentConversationId:u,setCurrentConversationId:d,setConversations:f,currentConversationInfo:p}=k(),[h]=J.Z.useForm(),m=(0,j.useRef)(()=>{});(0,j.useEffect)(()=>()=>{m.current()},[]);let[v,g]=(0,j.useState)(!1),[y,x]=(0,j.useState)([]),[w,S]=(0,j.useState)([]),C=la({conversationId:u,appId:null==t?void 0:t.id}),E=la({inputParams:(null==p?void 0:p.inputs)||{}}),O=(0,j.useRef)([]),_=async e=>{S((await o.getNextSuggestions({message_id:e})).data)},N=e=>{f(t=>(console.log("setConversations: updateConversationInputs",t.map(t=>t.id===u?{...t,inputs:e}:t)),t.map(t=>t.id===u?{...t,inputs:e}:t)))},A=async e=>{var n,a,i,s,l,c;if(T(e))return;let u=await o.getConversationHistory(e);if(!(null==u||null==(n=u.data)?void 0:n.length))return;let d=[];(null==u||null==(a=u.data)?void 0:a.length)&&(null==(i=Object.values(null==(l=u.data)||null==(s=l[0])?void 0:s.inputs))?void 0:i.length)&&N((null==(c=u.data[0])?void 0:c.inputs)||{}),u.data.forEach(n=>{let r=lo()(1e3*n.created_at).format("YYYY-MM-DD HH:mm:ss");d.push({id:n.id,content:n.query,status:"success",isHistory:!0,files:n.message_files,role:"user",created_at:r},{id:n.id,content:n.answer,status:"error"===n.status?n.status:"success",error:n.error||"",isHistory:!0,feedback:n.feedback,workflows:lu.get({appId:(null==t?void 0:t.id)||"",conversationId:e,messageId:n.id,key:"workflows"})||[],agentThoughts:n.agent_thoughts||[],retrieverResources:n.retriever_resources||[],role:"ai",created_at:r})}),I([]),x(d),(null==d?void 0:d.length)&&(null==r?void 0:r.suggested_questions_after_answer.enabled)&&_(d[d.length-1].id)},{agent:M,onRequest:P,messages:R,setMessages:I,currentTaskId:L}=lf({latestProps:C,latestState:E,filesRef:O,getNextSuggestions:_,appParameters:r,abortRef:m,getConversationMessages:A,onConversationIdChange:e=>{console.log("setCurrentConversationId: agent",e),d(e),e!==C.current.conversationId&&s()},difyApi:o}),F=async()=>{u&&!T(u)&&await A(u),g(!1)};(0,j.useEffect)(()=>{g(!0),I([]),S([]),x([]),F()},[u]);let $=e=>{P({content:e.data.description})},Z=(0,j.useMemo)(()=>{var e;return null==r||null==(e=r.user_input_form)||!e.length||r.user_input_form.every(e=>{var t;let n=Object.values(e)[0];return!!(null==p||null==(t=p.inputs)?void 0:t[n.variable])})},[r,p]),H=(0,j.useMemo)(()=>R.map(e=>({id:e.id,status:e.status,error:e.message.error||"",workflows:e.message.workflows,agentThoughts:e.message.agentThoughts,retrieverResources:e.message.retrieverResources,files:e.message.files,content:e.message.content,role:"local"===e.status?"user":"ai"})),[R]);return a||l?(0,b.jsx)("div",{className:"w-full h-full flex items-center justify-center",children:(0,b.jsx)(B.Z,{spinning:!0})}):t?(0,b.jsx)("div",{className:"flex h-screen flex-col overflow-hidden flex-1",children:(0,b.jsxs)("div",{className:"flex-1 overflow-hidden relative",children:[v?(0,b.jsx)("div",{className:"absolute w-full h-full left-0 top-0 z-50 flex items-center justify-center",children:(0,b.jsx)(B.Z,{spinning:!0})}):null,u?(0,b.jsx)(le,{appInfo:n,appConfig:t,conversationId:u,appParameters:r,nextSuggestions:w,messageItems:[...y,...H],isRequesting:M.isRequesting(),onPromptsItemClick:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return S([]),$(...t)},onSubmit:(e,t)=>{O.current=(null==t?void 0:t.files)||[],P({content:e,files:null==t?void 0:t.files})},onCancel:async()=>{m.current(),L&&(await o.stopTask(L),A(u))},isFormFilled:Z,onStartConversation:e=>{N(e),u||i()},feedbackApi:o.feedbackMessage,feedbackCallback:e=>{A(e)},uploadFileApi:o.uploadFile,difyApi:o,entryForm:h}):(0,b.jsx)("div",{className:"w-full h-full flex items-center justify-center",children:(0,b.jsx)(B.Z,{spinning:!0})})]})}):(0,b.jsx)("div",{className:"w-full h-full flex items-center justify-center",children:(0,b.jsx)(ea.Z,{description:"请先配置 Dify 应用",children:(0,b.jsx)(W.ZP,{type:"primary",onClick:c,children:"开始配置"})})})}let lh="新对话";function lm(e){return(0,b.jsx)("div",{className:"flex h-full items-center flex-[3] overflow-hidden justify-center text-primary font-semibold",children:(0,b.jsx)("div",{className:"flex items-center rounded-3xl shadow-md py-2 px-4 text-sm bg-white",children:e.children})})}let lv=e=>(0,b.jsx)("div",{className:i4()({"flex-1 h-full flex items-center":!0,"justify-start":"left"===e.align,"justify-end":"right"===e.align}),children:e.children});function lg(e){let{title:t,rightIcon:n}=e,r=L();return(0,b.jsxs)("div",{className:"h-16 flex items-center justify-between px-4",children:[(0,b.jsx)(lv,{align:"left",children:(0,b.jsx)(G,{hideText:r,hideGithubIcon:!0})}),(0,b.jsx)(lm,{children:t}),(0,b.jsx)(lv,{align:"right",children:n})]})}let ly=(0,ln.kc)(e=>{let{token:t,css:n}=e;return{layout:n`
			font-family: AlibabaPuHuiTi, ${t.fontFamily}, sans-serif;
		`}}),lb=e=>{let{extComponents:t,appConfig:n,useAppInit:r,renderCenterTitle:o,handleStartConfig:a,initLoading:i}=e,{...s}=A(),[l,c]=(0,j.useState)([]),[u,d]=(0,j.useState)(""),f=(0,j.useMemo)(()=>l.find(e=>e.id===u),[l,u]),p=L(),{user:h}=s,{styles:m}=ly(),[v]=(0,j.useState)(a8({user:h,apiBase:"",apiKey:""})),g=(0,D.lr)(),[y,x]=(0,j.useState)(!1),[w,S]=(0,j.useState)(),[k,E]=(0,j.useState)(),[O,_]=(0,j.useState)(!1),N=la(u),M=async()=>{S(void 0),v&&(_(!0),S({...await v.getAppInfo()}),E(await v.getAppParameters()),_(!1))};r(v,()=>{c([]),console.log("setCurrentConversationId: useAppInit",""),d(""),S(void 0),M().then(()=>{P().then(()=>{console.log("ssss",g.get("isNewCvst")),"1"===g.get("isNewCvst")&&R()})})}),console.log("currentConversationId in render",u);let P=async()=>{x(!0);try{var e,t;let n=await (null==v?void 0:v.getConversationList()),r=(null==n||null==(e=n.data)?void 0:e.map(e=>({key:e.id,label:e.name})))||[];c(null==n?void 0:n.data),N.current||(r.length?d(null==(t=r[0])?void 0:t.key):R())}catch(e){console.error(e),V.ZP.error(`获取会话列表失败: ${e}`)}finally{x(!1)}},R=()=>{let e=`temp_${Math.random()}`;c(t=>(console.log("setConversations: onAddConversation",[{id:e,name:lh,created_at:lo()().valueOf(),inputs:{},introduction:"",status:"normal",updated_at:lo()().valueOf()},...t]),[{id:e,name:lh,created_at:lo()().valueOf(),inputs:{},introduction:"",status:"normal",updated_at:lo()().valueOf()},...t])),console.log("setCurrentConversationId: onAddConversation",e),d(e)};return(0,b.jsx)(id.ZP,{theme:{token:{colorPrimary:"#1669ee",colorText:"#333"}},children:(0,b.jsxs)(C,{value:{conversations:l,setConversations:c,currentConversationId:u,setCurrentConversationId:d,currentConversationInfo:f},children:[(0,b.jsxs)("div",{className:`w-full h-screen ${m.layout} flex flex-col overflow-hidden bg-[#eff0f5]`,children:[(0,b.jsx)(lg,{title:w?null==o?void 0:o(w):null,rightIcon:p?(0,b.jsx)(Y.Z,{menu:{items:[{key:"add_conversation",icon:(0,b.jsx)(il.Z,{}),label:"新建对话",onClick:()=>{R()}},{type:"divider"},{type:"group",label:"历史对话",children:(null==l?void 0:l.length)?l.map(e=>({key:e.id,label:e.name,onClick:()=>{d(e.id)}})):[{key:"no_conversation",label:"暂无历史对话",disabled:!0}]}]},children:(0,b.jsx)(ic.Z,{className:"text-xl"})}):null}),(0,b.jsx)("div",{className:"flex-1 overflow-hidden flex rounded-3xl bg-white",children:O||i?(0,b.jsx)("div",{className:"absolute w-full h-full left-0 top-0 z-50 flex items-center justify-center",children:(0,b.jsx)(B.Z,{spinning:!0})}):n?(0,b.jsxs)(b.Fragment,{children:[(0,b.jsxs)("div",{className:"hidden md:!flex w-72 h-full flex-col border-0 border-r border-solid border-r-[#eff0f5]",children:[w?(0,b.jsx)(s9,{info:w}):null,n?(0,b.jsx)(W.ZP,{onClick:()=>{R()},className:"h-10 leading-10 rounded-lg border border-solid border-gray-200 mt-3 mx-4 text-default ",icon:(0,b.jsx)(iu.Z,{}),children:"新增对话"}):null,(0,b.jsx)("div",{className:"px-4 mt-3",children:(0,b.jsx)(B.Z,{spinning:y,children:(null==l?void 0:l.length)?(0,b.jsx)(lt,{renameConversationPromise:async(e,t)=>{await (null==v?void 0:v.renameConversation({conversation_id:e,name:t})),P()},deleteConversationPromise:async e=>{if(!T(e))return await (null==v?void 0:v.deleteConversation(e)),P(),Promise.resolve();c(t=>{let n=t.filter(t=>t.id!==e);return e===u&&n.length&&(console.log("setCurrentConversationId: deleteConversationPromise",n[0].id),d(n[0].id)),n})},items:l.map(e=>({key:e.id,label:e.name})),activeKey:u,onActiveChange:e=>{console.log("setCurrentConversationId: onActiveChange",e),d(e)}}):(0,b.jsx)("div",{className:"w-full h-full flex items-center justify-center",children:(0,b.jsx)(ea.Z,{className:"pt-6",description:"暂无会话"})})})})]}),(0,b.jsx)("div",{className:"flex-1 min-w-0 flex flex-col overflow-hidden",children:(0,b.jsx)(lp,{appConfig:n,appConfigLoading:O,appInfo:w,difyApi:v,appParameters:k,conversationListLoading:y,onAddConversation:R,conversationItemsChangeCallback:P})})]}):(0,b.jsx)("div",{className:"w-full h-full flex items-center justify-center",children:(0,b.jsx)(ea.Z,{description:"暂无 Dify 应用配置",className:"text-base",children:(0,b.jsx)(W.ZP,{size:"large",type:"primary",onClick:a,children:"开始配置"})})})})]}),t]})})},lx=()=>{let{setCurrentAppConfig:e,...t}=A(),{user:n,appService:r,enableSetting:o}=t,a=(0,D.k6)(),[i,s]=(0,j.useState)(""),[l,c]=(0,j.useState)(!1),[u,d]=(0,j.useState)(!1),{appId:f}=(0,D.UO)();(0,j.useEffect)(()=>{s(f)},[f]);let{runAsync:p,data:h,loading:m}=(0,X.Z)(()=>r.getApps(),{manual:!0,onSuccess:e=>{if(g&&!(null==e?void 0:e.length))return a.replace("/apps"),Promise.resolve([]);if(f)s(f);else if(!i&&(null==e?void 0:e.length)){var t;s((null==(t=e[0])?void 0:t.id)||"")}d(!1)},onError:e=>{V.ZP.error(`获取应用列表失败: ${e}`),console.error(e),d(!1)}}),v=(0,j.useMemo)(()=>null==h?void 0:h.find(e=>e.id===i),[h,i]),g=L();return((0,H.Z)(()=>{p()}),u)?(0,b.jsx)("div",{className:"absolute w-full h-full left-0 top-0 z-50 flex items-center justify-center",children:(0,b.jsx)(B.Z,{spinning:!0})}):v?(0,b.jsx)(lb,{useAppInit:(t,r)=>{(0,j.useEffect)(()=>{let o=null==h?void 0:h.find(e=>e.id===i);o&&(t.updateOptions({user:n,...o.requestConfig}),e(o),r())},[i])},appConfig:v,initLoading:u,handleStartConfig:()=>{o&&c(!0)},extComponents:(0,b.jsx)(b.Fragment,{children:(0,b.jsx)(is,{open:l,onClose:()=>c(!1),activeAppId:i,appList:h,getAppList:p,appListLoading:m,onDeleteSuccess:e=>{e===i&&s("")}})}),renderCenterTitle:()=>{var e;return(0,b.jsxs)(b.Fragment,{children:[(0,b.jsx)(K.Z,{className:"mr-2"}),(0,b.jsx)("span",{className:"cursor-pointer",onClick:()=>{a.push("/apps")},children:"应用列表"}),i?(0,b.jsxs)(b.Fragment,{children:[(0,b.jsx)("div",{className:"mx-2 font-normal text-desc",children:"/"}),(0,b.jsx)(Y.Z,{arrow:!0,placement:"bottom",trigger:["click"],menu:{style:{},selectedKeys:[i],items:[...(null==h?void 0:h.map(e=>{let t=i===e.id;return{key:e.id,label:(0,b.jsx)("div",{className:t?"text-primary":"text-default",children:e.info.name}),onClick:()=>{a.push(`/app/${e.id}`)},icon:(0,b.jsx)(K.Z,{})}}))||[]]},children:(0,b.jsxs)("div",{className:"cursor-pointer",children:[(0,b.jsx)("span",{className:"cursor-pointer",children:null==v||null==(e=v.info)?void 0:e.name}),(0,b.jsx)(q.Z,{className:"ml-1"})]})})]}):null]})}}):null},lj=()=>{let e=A(),{user:t,appConfig:n}=e;return(0,b.jsx)(lb,{initLoading:!1,handleStartConfig:()=>{},useAppInit:(n,r)=>{let o=async()=>{n.updateOptions({user:t,apiBase:e.appConfig.requestConfig.apiBase,apiKey:e.appConfig.requestConfig.apiKey}),r()};(0,H.Z)(()=>{o()})},appConfig:n,renderCenterTitle:e=>(0,b.jsx)(b.Fragment,{children:null==e?void 0:e.name})})};function lw(){let{user:e,mode:t}=A();return e?"singleApp"===t?(0,b.jsx)(lj,{}):(0,b.jsx)(lx,{}):(0,b.jsx)("div",{className:"w-screen h-screen flex flex-col items-center justify-center",children:(0,b.jsxs)("div",{className:"absolute flex-col w-full h-full left-0 top-0 z-50 flex items-center justify-center",children:[(0,b.jsx)(G,{hideGithubIcon:!0}),(0,b.jsx)("div",{children:"授权登录中..."}),(0,b.jsx)("div",{className:"mt-6",children:(0,b.jsx)(B.Z,{spinning:!0})})]})})}!function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:I;(0,P.P)(e)}();let lS=[{path:"/chat",component:()=>(0,b.jsx)(lw,{})}],lC="ai.medsci.cn"===window.location.hostname?"https://ai.medsci.cn":"https://ai.medon.com.cn",lk=document.getElementById("root");lk&&x.createRoot(lk).render((0,b.jsx)(function(){let[e,t]=(0,j.useState)("");return(0,H.Z)(()=>{(async()=>{let e=await Z.ZP.load();t((await e.get()).visitorId)})()}),(0,b.jsx)(D.VK,{basename:"/yisan-chat",routes:lS,children:(0,b.jsx)(N,{value:{mode:"singleApp",user:e,appConfig:{requestConfig:{apiBase:lC+"/v1",apiKey:"app-P9vsUJJSTkMEAp6cAldilQ41"}}},children:(0,b.jsx)(D.AW,{})})})},{}))}},t={};function n(r){var o=t[r];if(void 0!==o)return o.exports;var a=t[r]={exports:{}};return e[r].call(a.exports,a,a.exports,n),a.exports}n.m=e,n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},(()=>{var e,t=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__;n.t=function(r,o){if(1&o&&(r=this(r)),8&o||"object"==typeof r&&r&&(4&o&&r.__esModule||16&o&&"function"==typeof r.then))return r;var a=Object.create(null);n.r(a);var i={};e=e||[null,t({}),t([]),t(t)];for(var s=2&o&&r;"object"==typeof s&&!~e.indexOf(s);s=t(s))Object.getOwnPropertyNames(s).forEach(e=>{i[e]=()=>r[e]});return i.default=()=>r,n.d(a,i),a}})(),n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.f={},n.e=e=>Promise.all(Object.keys(n.f).reduce((t,r)=>(n.f[r](e,t),t),[])),n.u=e=>"static/js/async/"+e+"."+({103:"38df19bd",113:"e6e53918",139:"fab3c4a9",174:"ecc3beae",18:"cc888385",194:"aca8ba59",205:"45736c37",208:"4bbdaf7d",219:"9951a8b3",254:"fcfa37af",33:"d3140ddd",354:"2ecd0882",360:"f01ca2dd",406:"bf0604ad",469:"5b88d3bd",476:"32a038b7",503:"d03a97e9",516:"e942ecc7",541:"e01b9e80",584:"bc8b4d38",66:"2ebf0419",672:"696afbca",714:"272bc6bb",733:"447a6500",751:"d1496734",774:"ca6d64f3",796:"e7c58007",8:"faf715eb",828:"253e6351",836:"41e72eca",854:"b745e13f",857:"79a042c5",867:"36010f96",951:"9ac27546",953:"2ffeeb4d",957:"2743cee0",976:"fffef8b2"})[e]+".js",n.miniCssF=e=>""+e+".css",n.h=()=>"356419980f112b06",n.g=(()=>{if("object"==typeof globalThis)return globalThis;try{return this||Function("return this")()}catch(e){if("object"==typeof window)return window}})(),n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{var e={},t="dify-chat-web:";n.l=function(r,o,a,i){if(e[r])return void e[r].push(o);if(void 0!==a)for(var s,l,c=document.getElementsByTagName("script"),u=0;u<c.length;u++){var d=c[u];if(d.getAttribute("src")==r||d.getAttribute("data-webpack")==t+a){s=d;break}}s||(l=!0,(s=document.createElement("script")).charset="utf-8",s.timeout=120,n.nc&&s.setAttribute("nonce",n.nc),s.setAttribute("data-webpack",t+a),s.src=r),e[r]=[o];var f=function(t,n){s.onerror=s.onload=null,clearTimeout(p);var o=e[r];if(delete e[r],s.parentNode&&s.parentNode.removeChild(s),o&&o.forEach(function(e){return e(n)}),t)return t(n)},p=setTimeout(f.bind(null,void 0,{type:"timeout",target:s}),12e4);s.onerror=f.bind(null,s.onerror),s.onload=f.bind(null,s.onload),l&&document.head.appendChild(s)}})(),n.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},(()=>{var e=[];n.O=(t,r,o,a)=>{if(r){a=a||0;for(var i=e.length;i>0&&e[i-1][2]>a;i--)e[i]=e[i-1];e[i]=[r,o,a];return}for(var s=1/0,i=0;i<e.length;i++){for(var[r,o,a]=e[i],l=!0,c=0;c<r.length;c++)(!1&a||s>=a)&&Object.keys(n.O).every(e=>n.O[e](r[c]))?r.splice(c--,1):(l=!1,a<s&&(s=a));if(l){e.splice(i--,1);var u=o();void 0!==u&&(t=u)}}return t}})(),n.p="/yisan-chat/",n.rv=()=>"1.3.5",(()=>{var e={980:0};n.f.j=function(t,r){var o=n.o(e,t)?e[t]:void 0;if(0!==o)if(o)r.push(o[2]);else{var a=new Promise((n,r)=>o=e[t]=[n,r]);r.push(o[2]=a);var i=n.p+n.u(t),s=Error();n.l(i,function(r){if(n.o(e,t)&&(0!==(o=e[t])&&(e[t]=void 0),o)){var a=r&&("load"===r.type?"missing":r.type),i=r&&r.target&&r.target.src;s.message="Loading chunk "+t+" failed.\n("+a+": "+i+")",s.name="ChunkLoadError",s.type=a,s.request=i,o[1](s)}},"chunk-"+t,t)}},n.O.j=t=>0===e[t];var t=(t,r)=>{var o,a,[i,s,l]=r,c=0;if(i.some(t=>0!==e[t])){for(o in s)n.o(s,o)&&(n.m[o]=s[o]);if(l)var u=l(n)}for(t&&t(r);c<i.length;c++)a=i[c],n.o(e,a)&&e[a]&&e[a][0](),e[a]=0;return n.O(u)},r=self.webpackChunkdify_chat_web=self.webpackChunkdify_chat_web||[];r.forEach(t.bind(null,0)),r.push=t.bind(null,r.push.bind(r))})(),n.ruid="bundler=rspack@1.3.5";var r=n.O(void 0,["361","7"],function(){return n(65681)});r=n.O(r)})();