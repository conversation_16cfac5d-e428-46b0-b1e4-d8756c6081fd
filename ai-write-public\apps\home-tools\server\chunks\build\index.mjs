import { _ as __nuxt_component_0 } from './client-only.mjs';
import { ref, resolveComponent, resolveDirective, mergeProps, unref, withCtx, createTextVNode, toDisplayString, useSSRContext } from 'vue';
import { ssrRenderAttrs, ssrRenderAttr, ssrInterpolate, ssrRenderStyle, ssrIncludeBooleanAttr, ssrRenderList, ssrRenderClass, ssrRenderComponent, ssrGetDirectiveProps } from 'vue/server-renderer';
import { showConfirmDialog, Checkbox, Toast } from 'vant';
import { ElMessage } from 'element-plus';
import { k as cancelSubscription, e as createSubscription, C as Cookies, n as createAliSub, o as freeLimit } from './requestDify.mjs';
import { g as getDefaultLanguageCode } from './commonJs.mjs';
import { e as useI18n, u as useRouter } from './server.mjs';
import { s as setInterval } from './interval.mjs';
import { _ as _export_sfc } from './_plugin-vue_export-helper.mjs';
import cookie from 'js-cookie';

const _sfc_main$1 = {
  __name: "index",
  __ssrInlineRender: true,
  props: {
    userInfo: {
      type: Object,
      default: () => ({})
    },
    currentItem: {
      type: Object,
      default: () => ({})
    },
    subStatusDetail: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  emits: ["close"],
  setup(__props, { emit: __emit }) {
    var _a;
    const { t, locale } = useI18n();
    const loading = ref(false);
    useRouter();
    ref(false);
    const props = __props;
    const userInfo = props.userInfo || {};
    const currentItem = ((_a = props.currentItem) == null ? void 0 : _a.feeTypes) ? props.currentItem : props.subStatusDetail;
    const activeItem = ref({});
    const active = ref();
    const emit = __emit;
    const payUrl = ref("");
    ref();
    const piId = ref();
    const time = ref();
    const avatar = ref((userInfo == null ? void 0 : userInfo.avatar) ? userInfo == null ? void 0 : userInfo.avatar : "https://img.medsci.cn/web/img/user_icon.png");
    const num = ref(0);
    const subStatusDetail = ref(props.subStatusDetail);
    const close = () => {
      clearInterval(time.value);
      emit("close");
    };
    const getStatus = () => {
      time.value = setInterval();
    };
    const cancelSub = () => {
      showConfirmDialog({
        title: "提示",
        zIndex: 4e3,
        confirmButtonColor: "#D7813F",
        message: `取消包月在${subStatusDetail.value.expireAt}号生效，再次使用需要重新订阅。是否确认取消？`
      }).then(async () => {
        await cancelSubscription();
        ElMessage.success("取消成功");
        close();
      }).catch(() => {
      });
    };
    const subscribe = async (item, appUuid) => {
      if (!activeItem.value.coinType) {
        ElMessage.warning("请选择订阅服务周期");
        return;
      }
      let language = await getDefaultLanguageCode(locale.value);
      if (!(userInfo == null ? void 0 : userInfo.userId)) {
        if (!language || language == "zh-CN") {
          (void 0).addLoginDom();
        } else {
          (void 0).href = (void 0).origin + "/" + locale.value + "/login";
        }
      } else {
        const subscriptionParams = {
          appUuid: appUuid || "",
          priceId: item.priceId,
          monthNum: item.monthNum,
          packageKey: item.packageKey,
          packageType: item.type
        };
        try {
          loading.value = true;
          let res = await createSubscription(subscriptionParams);
          if (res) {
            loading.value = false;
            if (item.coinType == "人民币" && item.feePrice != 0) {
              const payInfo = res;
              if ((void 0).origin.includes(".medsci.cn") || (void 0).origin.includes(".medon.com.cn")) {
                payUrl.value = (void 0).origin + "/payLink/" + encodeURIComponent(payInfo);
              } else {
                payUrl.value = (void 0).origin + "/payLink/" + encodeURIComponent(payInfo);
              }
              piId.value = JSON.parse(payInfo).piId;
              await getStatus();
            } else {
              ElMessage({
                type: "success",
                message: t("tool.sS")
              });
              setTimeout(() => {
                (void 0).href = res;
              }, 1e3);
            }
          }
        } catch (error) {
          loading.value = false;
        }
      }
    };
    return (_ctx, _push, _parent, _attrs) => {
      var _a2, _b, _c, _d, _e, _f, _g, _h, _i, _j, _k, _l, _m, _n, _o, _p, _q, _r, _s, _t, _u, _v;
      const _component_el_button = resolveComponent("el-button");
      const _component_ClientOnly = __nuxt_component_0;
      const _directive_loading = resolveDirective("loading");
      let _temp0, _temp1;
      _push(`<div${ssrRenderAttrs(mergeProps({ id: "app" }, _attrs))} data-v-be5719eb><div class="scale" data-v-be5719eb><div class="micro_header" data-v-be5719eb><div class="micro_left" data-v-be5719eb><div class="avatar" data-v-be5719eb><img${ssrRenderAttr("src", unref(avatar) ? unref(avatar) + "?t=" + Date.now() : "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII=")} alt="" data-v-be5719eb><span class="t1" data-v-be5719eb>${ssrInterpolate(((_a2 = unref(userInfo)) == null ? void 0 : _a2.realName) ? (_b = unref(userInfo)) == null ? void 0 : _b.realName : (_c = unref(userInfo)) == null ? void 0 : _c.userName)}</span></div></div><div class="micro_right" data-v-be5719eb><img src="https://static.medsci.cn/public-image/ms-image/78bf61f0-208d-11ee-ae3d-b3e9904fad92_关闭@2x.png" alt="" data-v-be5719eb></div></div><div class="micro_main" data-v-be5719eb><div class="micro_main_top" data-v-be5719eb><div class="micro_main-sp" data-v-be5719eb><div class="micro_main_temp" data-v-be5719eb>`);
      if (((_e = (_d = unref(currentItem)) == null ? void 0 : _d.feeTypes[0]) == null ? void 0 : _e.coinType) == "美元" || ((_g = (_f = unref(currentItem)) == null ? void 0 : _f.feeTypes) == null ? void 0 : _g.length) > 1) {
        _push(`<div class="swiper-vip"${ssrRenderAttr("showIndicator", false)}${ssrIncludeBooleanAttr(false) ? " autoplay" : ""} style="${ssrRenderStyle({ transform: `translate(${_ctx.translateVipVal}px)` })}" data-v-be5719eb><!--[-->`);
        ssrRenderList(unref(currentItem).feeTypes, (item, index) => {
          var _a3, _b2, _c2, _d2;
          _push(`<div class="${ssrRenderClass([((_a3 = unref(subStatusDetail)) == null ? void 0 : _a3.packageType) == "连续包月" && item.type == "免费" || ((_b2 = unref(subStatusDetail)) == null ? void 0 : _b2.packageType) == "连续包月" && item.type == "连续包年" || (((_c2 = unref(subStatusDetail)) == null ? void 0 : _c2.packageType) == "连续包年" && item.type == "免费" || ((_d2 = unref(subStatusDetail)) == null ? void 0 : _d2.packageType) == "连续包年" && item.type == "连续包月") ? "noClick" : "", "swiper-vip-item"])}" data-v-be5719eb><div class="newer" style="${ssrRenderStyle({
            left: index % 4 == 0 && index != 0 ? "6px" : "-1px"
          })}" data-v-be5719eb></div><div class="${ssrRenderClass([{ sactvie: unref(active) == index }, "swiper-vip-item-child"])}" data-v-be5719eb><div class="title" data-v-be5719eb>${ssrInterpolate(item.type == "免费" ? "免费" : _ctx.$t(`tool.${item.type}`))}</div><div class="price" data-v-be5719eb><span data-v-be5719eb>${ssrInterpolate(item.coinType == "人民币" ? "¥" : "$")}</span> ${ssrInterpolate(item.feePrice)}</div></div></div>`);
        });
        _push(`<!--]--></div>`);
      } else {
        _push(`<!---->`);
      }
      _push(`</div></div></div>`);
      if (unref(locale) == "zh-CN") {
        _push(`<div class="micro_main_middle" data-v-be5719eb><div class="micro_main_middle_banner" data-v-be5719eb><div class="micro_main_middle_title" data-v-be5719eb><img src="https://img.medsci.cn/202503/36f8fcc00cb841eaaa9ff81b3b225285-Ar3LIaiI1GQo.png" data-v-be5719eb>梅斯小智 订阅说明</div><div class="micro_main_middle_content" data-v-be5719eb><div data-v-be5719eb>免费：每个自然月内，每个智能体的使用上限${ssrInterpolate(unref(num))}次。次月开始重新计次。</div><div data-v-be5719eb>连续包月：订阅之日起一个月内，每个智能体不限使用次数。</div><div data-v-be5719eb>连续包年：订阅之日起一年内，每个智能体不限使用次数</div></div></div></div>`);
      } else {
        _push(`<div class="micro_main_middle" data-v-be5719eb><div class="micro_main_middle_banner" data-v-be5719eb><div class="micro_main_middle_title" data-v-be5719eb><img src="https://img.medsci.cn/202503/36f8fcc00cb841eaaa9ff81b3b225285-Ar3LIaiI1GQo.png" data-v-be5719eb>${ssrInterpolate((_h = unref(currentItem)) == null ? void 0 : _h.appName)}</div><div class="micro_main_middle_content" data-v-be5719eb>${ssrInterpolate(unref(currentItem).appDescription)}</div></div></div>`);
      }
      if (((_i = unref(activeItem)) == null ? void 0 : _i.coinType) && ((_j = unref(activeItem)) == null ? void 0 : _j.coinType) == "人民币" && unref(locale) == "zh-CN") {
        _push(`<div class="micro_main_bottom onborder" data-v-be5719eb>`);
        if ((unref(subStatusDetail).subStatus == "1" || unref(subStatusDetail).subStatus == "3") && unref(activeItem).type == unref(subStatusDetail).packageType) {
          _push(`<div class="result" data-v-be5719eb>${ssrInterpolate(unref(subStatusDetail).subAt)} 已订阅</div>`);
        } else {
          _push(`<!---->`);
        }
        if (unref(subStatusDetail).subStatus == "1" && unref(subStatusDetail).packageType == "免费" && unref(activeItem).type == "免费") {
          _push(`<div class="result" data-v-be5719eb>免费使用中…</div>`);
        } else {
          _push(`<!---->`);
        }
        if (unref(subStatusDetail).subStatus == "3") {
          _push(`<div class="result" data-v-be5719eb>${ssrInterpolate(unref(subStatusDetail).unSubAt)} 取消订阅</div>`);
        } else {
          _push(`<!---->`);
        }
        if (unref(subStatusDetail).subStatus == "3") {
          _push(`<div class="result" data-v-be5719eb>您的订阅可使用至 ${ssrInterpolate(unref(subStatusDetail).expireAt)}</div>`);
        } else {
          _push(`<!---->`);
        }
        if (unref(subStatusDetail).packageType == "连续包月" && unref(subStatusDetail).subStatus == "1") {
          _push(`<div class="result" data-v-be5719eb>连续包月中…</div>`);
        } else {
          _push(`<!---->`);
        }
        if (unref(subStatusDetail).packageType == "连续包年" && unref(subStatusDetail).subStatus == "1") {
          _push(`<div class="result" data-v-be5719eb>连续包年中…</div>`);
        } else {
          _push(`<!---->`);
        }
        _push(`<div class="btns" data-v-be5719eb>`);
        if (unref(subStatusDetail).packageType == "连续包月" && unref(subStatusDetail).subStatus == "1") {
          _push(ssrRenderComponent(_component_el_button, {
            type: "primary",
            onClick: ($event) => cancelSub()
          }, {
            default: withCtx((_, _push2, _parent2, _scopeId) => {
              if (_push2) {
                _push2(`取消包月`);
              } else {
                return [
                  createTextVNode("取消包月")
                ];
              }
            }),
            _: 1
          }, _parent));
        } else {
          _push(`<!---->`);
        }
        if (unref(subStatusDetail).packageType == "连续包年" && unref(subStatusDetail).subStatus == "1") {
          _push(ssrRenderComponent(_component_el_button, {
            type: "primary",
            onClick: ($event) => cancelSub()
          }, {
            default: withCtx((_, _push2, _parent2, _scopeId) => {
              if (_push2) {
                _push2(`取消包年`);
              } else {
                return [
                  createTextVNode("取消包年")
                ];
              }
            }),
            _: 1
          }, _parent));
        } else {
          _push(`<!---->`);
        }
        _push(`</div><div class="micro_pay" data-v-be5719eb>`);
        if (unref(activeItem).type != "免费" && unref(subStatusDetail).subStatus == "0" || unref(activeItem).type != "免费" && unref(subStatusDetail).subStatus == "2" || unref(subStatusDetail).packageType == "免费" && unref(activeItem).type != "免费") {
          _push(`<div class="micro_pay_right" data-v-be5719eb><div${ssrRenderAttrs(_temp0 = mergeProps({
            style: unref(loading) ? null : { display: "none" },
            class: "noQrCode"
          }, ssrGetDirectiveProps(_ctx, _directive_loading, unref(loading))))} data-v-be5719eb>${"textContent" in _temp0 ? ssrInterpolate(_temp0.textContent) : _temp0.innerHTML ?? ""}</div>`);
          _push(ssrRenderComponent(_component_ClientOnly, null, {}, _parent));
          _push(`<div class="price" data-v-be5719eb><div class="micro_way" data-v-be5719eb><div class="box" data-v-be5719eb><img src="https://static.medsci.cn/public-image/ms-image/19eef410-5893-11ec-8e2f-1389d01aad85_zfb.png" alt="" data-v-be5719eb></div><span data-v-be5719eb>${ssrInterpolate(_ctx.$t("tool.Support_Alipay_Payment"))}</span></div><span class="t1" data-v-be5719eb>${ssrInterpolate(_ctx.$t("tool.Support_Alipay_Payment"))}<span class="bd" data-v-be5719eb>${ssrInterpolate((_k = unref(activeItem)) == null ? void 0 : _k.feePrice)}</span>${ssrInterpolate(((_l = unref(activeItem)) == null ? void 0 : _l.coinType) == "人民币" ? "¥" : "$")}/${ssrInterpolate(((_m = unref(activeItem)) == null ? void 0 : _m.monthNum) == 3 ? _ctx.$t("tool.Quarter") : ((_n = unref(activeItem)) == null ? void 0 : _n.monthNum) == 12 ? _ctx.$t("tool.Year") : _ctx.$t("tool.Month"))}</span><span class="t2" data-v-be5719eb>${ssrInterpolate(_ctx.$t("tool.Meisi_Account"))}：${ssrInterpolate(unref(userInfo).userName)}</span><span class="t3" data-v-be5719eb>${ssrInterpolate(_ctx.$t("tool.Please_activate_after_reading_and_agreeing_to_the_agreement"))} <img src="https://static.medsci.cn/public-image/ms-image/17ae6920-5be4-11ec-8e2f-1389d01aad85_right.png" alt="" data-v-be5719eb></span></div></div>`);
        } else {
          _push(`<!---->`);
        }
        _push(`</div></div>`);
      } else {
        _push(`<!---->`);
      }
      if (((_o = unref(activeItem)) == null ? void 0 : _o.coinType) && ((_p = unref(activeItem)) == null ? void 0 : _p.coinType) == "人民币" && unref(activeItem).feePrice != 0 && unref(locale) != "zh-CN") {
        _push(`<div class="micro_main_bottom" data-v-be5719eb><div class="micro_pay" data-v-be5719eb>`);
        if (unref(subStatusDetail).subStatus == "0" || unref(subStatusDetail).subStatus == "2") {
          _push(`<div class="micro_pay_right" data-v-be5719eb><div${ssrRenderAttrs(_temp1 = mergeProps({
            style: unref(loading) ? null : { display: "none" },
            class: "noQrCode"
          }, ssrGetDirectiveProps(_ctx, _directive_loading, unref(loading))))} data-v-be5719eb>${"textContent" in _temp1 ? ssrInterpolate(_temp1.textContent) : _temp1.innerHTML ?? ""}</div>`);
          _push(ssrRenderComponent(_component_ClientOnly, null, {}, _parent));
          _push(`<div class="price" data-v-be5719eb><div class="micro_way" data-v-be5719eb><div class="box" data-v-be5719eb><img src="https://static.medsci.cn/public-image/ms-image/19eef410-5893-11ec-8e2f-1389d01aad85_zfb.png" alt="" data-v-be5719eb></div><span data-v-be5719eb>${ssrInterpolate(_ctx.$t("tool.Support_Alipay_Payment"))}</span></div><span class="t1" data-v-be5719eb>${ssrInterpolate(_ctx.$t("tool.Support_Alipay_Payment"))}<span class="bd" data-v-be5719eb>${ssrInterpolate((_q = unref(activeItem)) == null ? void 0 : _q.feePrice)}</span>${ssrInterpolate(((_r = unref(activeItem)) == null ? void 0 : _r.coinType) == "人民币" ? "¥" : "$")}/${ssrInterpolate(((_s = unref(activeItem)) == null ? void 0 : _s.monthNum) == 3 ? _ctx.$t("tool.Quarter") : ((_t = unref(activeItem)) == null ? void 0 : _t.monthNum) == 12 ? _ctx.$t("tool.Year") : _ctx.$t("tool.Month"))}</span><span class="t2" data-v-be5719eb>${ssrInterpolate(_ctx.$t("tool.Meisi_Account"))}：${ssrInterpolate(unref(userInfo).userName)}</span><span class="t3" data-v-be5719eb>${ssrInterpolate(_ctx.$t("tool.Please_activate_after_reading_and_agreeing_to_the_agreement"))} <img src="https://static.medsci.cn/public-image/ms-image/17ae6920-5be4-11ec-8e2f-1389d01aad85_right.png" alt="" data-v-be5719eb></span></div></div>`);
        } else {
          _push(`<!---->`);
        }
        _push(`</div></div>`);
      } else {
        _push(`<!---->`);
      }
      if (unref(activeItem).feePrice == 0 && (unref(subStatusDetail).subStatus == "0" || unref(subStatusDetail).subStatus == "2") && unref(locale) == "zh-CN") {
        _push(`<div class="btns" data-v-be5719eb>`);
        _push(ssrRenderComponent(_component_el_button, {
          type: "primary",
          onClick: ($event) => subscribe(unref(activeItem), unref(currentItem).appUuid)
        }, {
          default: withCtx((_, _push2, _parent2, _scopeId) => {
            if (_push2) {
              _push2(`${ssrInterpolate(_ctx.$t("tool.Free_Trial"))}`);
            } else {
              return [
                createTextVNode(toDisplayString(_ctx.$t("tool.Free_Trial")), 1)
              ];
            }
          }),
          _: 1
        }, _parent));
        _push(`</div>`);
      } else {
        _push(`<!---->`);
      }
      if (unref(activeItem).feePrice == 0 && unref(locale) != "zh-CN") {
        _push(`<div class="btns" data-v-be5719eb>`);
        _push(ssrRenderComponent(_component_el_button, {
          type: "primary",
          onClick: ($event) => subscribe(unref(activeItem), unref(currentItem).appUuid)
        }, {
          default: withCtx((_, _push2, _parent2, _scopeId) => {
            if (_push2) {
              _push2(`${ssrInterpolate(_ctx.$t("tool.Free_Trial"))}`);
            } else {
              return [
                createTextVNode(toDisplayString(_ctx.$t("tool.Free_Trial")), 1)
              ];
            }
          }),
          _: 1
        }, _parent));
        _push(`</div>`);
      } else {
        _push(`<!---->`);
      }
      if (((_u = unref(activeItem)) == null ? void 0 : _u.coinType) && ((_v = unref(activeItem)) == null ? void 0 : _v.coinType) == "美元" && unref(activeItem).feePrice > 0) {
        _push(`<div class="btns" data-v-be5719eb>`);
        _push(ssrRenderComponent(_component_el_button, {
          type: "primary",
          onClick: ($event) => subscribe(unref(activeItem), unref(currentItem).appUuid)
        }, {
          default: withCtx((_, _push2, _parent2, _scopeId) => {
            if (_push2) {
              _push2(`${ssrInterpolate(_ctx.$t("market.subscribe"))}`);
            } else {
              return [
                createTextVNode(toDisplayString(_ctx.$t("market.subscribe")), 1)
              ];
            }
          }),
          _: 1
        }, _parent));
        _push(`</div>`);
      } else {
        _push(`<!---->`);
      }
      _push(`</div></div></div>`);
    };
  }
};
const _sfc_setup$1 = _sfc_main$1.setup;
_sfc_main$1.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("components/pay/index.vue");
  return _sfc_setup$1 ? _sfc_setup$1(props, ctx) : void 0;
};
const pay = /* @__PURE__ */ _export_sfc(_sfc_main$1, [["__scopeId", "data-v-be5719eb"]]);

const _sfc_main = {
  name: "Vip",
  data() {
    return {
      isCheckW: true,
      isCheckZ: false,
      // userInfo: {},
      isLogin: false,
      activeItem: {},
      appId: "wx9096048917ec59ab",
      appOrderId: "",
      isClick: false,
      openId: "",
      isWx: false,
      choseUserVip: {},
      isFromMedsci: false,
      showAll: false,
      checkCount: 0,
      vipTypeList: [],
      activeType: 0,
      active: 0,
      radio: "",
      isShaking: false,
      avatar: "",
      time: null,
      piId: "",
      language: "",
      num: 0
    };
  },
  components: {
    VanCheckbox: Checkbox
  },
  props: {
    userInfo: {
      type: Object,
      default: () => ({})
    },
    currentItem: {
      type: Object,
      default: () => ({})
    },
    subStatusDetail: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  created() {
  },
  mounted() {
    var _a, _b, _c, _d, _e, _f, _g, _h, _i, _j, _k, _l, _m, _n, _o, _p, _q;
    this.getNum();
    if (((_b = (_a = this.subStatusDetail) == null ? void 0 : _a.feeTypes) == null ? void 0 : _b.length) > 0) {
      (_c = this.subStatusDetail) == null ? void 0 : _c.feeTypes.forEach((element, index) => {
        if (this.subStatusDetail.packageType == element.type) {
          this.active = index;
          this.activeItem = element;
        }
      });
    }
    this.language = Cookies.get("ai_apps_lang");
    const windowVH = (void 0).innerHeight / 100;
    (void 0).documentElement.style.setProperty("--vh", `${windowVH}px`);
    if (((_d = this.currentItem) == null ? void 0 : _d.appType) == "写作") {
      localStorage.setItem(
        "appWrite-" + ((_e = this.currentItem) == null ? void 0 : _e.appUuid),
        JSON.stringify({
          appUuid: (_f = this.currentItem) == null ? void 0 : _f.appUuid,
          directoryMd: (_g = this.currentItem) == null ? void 0 : _g.directoryMd
        })
      );
    }
    this.avatar = ((_h = this.userInfo) == null ? void 0 : _h.avatar) ? (_i = this.userInfo) == null ? void 0 : _i.avatar : "https://img.medsci.cn/web/img/user_icon.png";
    this.isUp = void 0;
    if (((_k = (_j = this.currentItem) == null ? void 0 : _j.feeTypes) == null ? void 0 : _k.length) == 1) {
      if (((_l = this.currentItem) == null ? void 0 : _l.feeTypes[0].feePrice) > 0) {
        this.CheckItem((_m = this.currentItem) == null ? void 0 : _m.feeTypes[0], (_n = this.currentItem) == null ? void 0 : _n.appUuid);
      }
      if (((_o = this.currentItem) == null ? void 0 : _o.feeTypes[0].feePrice) == 0) {
        this.CheckItem((_p = this.currentItem) == null ? void 0 : _p.feeTypes[0], (_q = this.currentItem) == null ? void 0 : _q.appUuid);
      }
    }
    if (cookie.get("userInfo") && JSON.parse(cookie.get("userInfo")).userId) {
      this.isLogin = true;
      this.initUser();
    }
    this.init();
    if (this.$route.query.source && this.$route.query.source == "medsci") {
      this.isFromMedsci = true;
    }
  },
  methods: {
    // 取消订阅
    // 取消订阅
    cancelSub() {
      showConfirmDialog({
        title: "提示",
        zIndex: 3002,
        confirmButtonColor: "#D7813F",
        message: `取消包月在${this.subStatusDetail.expireAt}号生效，再次使用需要重新订阅。是否确认取消？`
      }).then(async () => {
        await cancelSubscription();
        ElMessage.success("取消成功");
        this.$emit("close");
      }).catch(() => {
      });
    },
    async getNum() {
      const res = await freeLimit();
      this.num = res;
    },
    changeImg() {
      this.avatar = "https://img.medsci.cn/web/img/user_icon.png";
    },
    // 支付
    async subscribe(item, appUuid, type) {
      var _a;
      if (!this.radio && type) {
        this.isShaking = true;
        setTimeout(() => {
          this.isShaking = false;
        }, 500);
        return;
      }
      let language = Cookies.get("ai_apps_lang");
      if (!((_a = this.userInfo) == null ? void 0 : _a.userId)) {
        if (!language || language == "zh-CN") {
          (void 0).addLoginDom();
        } else {
          (void 0).href = (void 0).origin + "/" + language + "/login";
        }
      } else {
        const subscriptionParams = {
          appUuid: appUuid || "",
          priceId: item.priceId,
          monthNum: item.monthNum,
          packageKey: item.packageKey,
          packageType: item.type
        };
        try {
          let res = await createSubscription(subscriptionParams);
          if (res) {
            /* @__PURE__ */ console.log(item.coinType == "人民币" && item.feePrice != 0, "res");
            if (item.coinType == "人民币" && item.feePrice != 0) {
              this.piId = JSON.parse(res).piId;
              await this.getStatus();
              let resUrl = await createAliSub(JSON.parse(res));
              /* @__PURE__ */ console.log(resUrl, "resUrl");
              ElMessage({
                type: "success",
                message: this.$t("tool.sS")
              });
              setTimeout(() => {
                (void 0).href = resUrl;
              }, 1e3);
            } else {
              ElMessage({
                type: "success",
                message: this.$t("tool.sS")
              });
              setTimeout(() => {
                (void 0).href = res;
              }, 1e3);
            }
          }
        } catch (error) {
        }
      }
    },
    // 查询支付状态
    getStatus() {
      /* @__PURE__ */ console.log(this.piId, "piId");
      this.time = setInterval();
    },
    // 选择支付
    CheckItem(item, index) {
      this.activeItem = item;
      this.active = index;
    },
    openActivity(href) {
      if (href) {
        (void 0).location.href = href;
      }
    },
    login() {
      addLoginDom();
    },
    initUser() {
    },
    init() {
    },
    isMedSci() {
      const u = (void 0).userAgent;
      return u.includes("medsci_app");
    },
    goBack() {
      (void 0).history.back(-1);
    },
    checkFn1() {
      this.isCheckW = true;
      this.isCheckZ = false;
    },
    checkFn2() {
      this.isCheckW = false;
      this.isCheckZ = true;
    },
    goAgreent() {
      const url = process.env.VITE_MODE == "prod" ? `https://www.medsci.cn/about/index.do?id=27` : `https://portal-test.medon.com.cn/agreement/27`;
      if (this.isMedSci()) {
        (void 0).location.href = url;
        return;
      }
      (void 0).open(url);
    },
    // 订单
    createOrder() {
      if (this.isWx) {
        this.isCheckW = true;
        this.isCheckZ = false;
      }
      const params = {
        accessAppId: "college",
        appOrderId: this.appOrderId,
        payChannel: this.isCheckW ? "WX" : "ALI",
        // 根据userAgent判断 ALI WX
        paySource: "MEDSCI_WEB",
        payType: this.isWx ? "JSAPI" : "MWEB"
        // MWEB
      };
      this.$axios.post(api.payBuild, params).then((res) => {
        /* @__PURE__ */ console.log(res.data, "paybuild");
        this.orderList(res.data.payOrderId);
      });
    },
    // 流水
    orderList(id) {
      const extParam = {};
      if (this.$route.query.from) {
        extParam.from = "app";
      }
      if (this.isFromMedsci) {
        extParam.sourcefrom = "main";
        extParam.redirectUrl = this.$route.query.redirectUrl;
      }
      const params = {
        accessAppId: "college",
        openId: this.isWx ? this.openId : "",
        // 微信支付的时候才用
        payOrderId: id,
        // 支付订单返回的id
        extParam: JSON.stringify(extParam)
      };
      this.$axios.post(api.payOrder, params).then((res) => {
        if (!this.isCheckW) {
          if (res.data.aliH5.html) {
            const div = (void 0).createElement("div");
            div.innerHTML = res.data.aliH5.html;
            (void 0).body.appendChild(div);
            (void 0).forms[0].submit();
          }
        } else {
          if (this.isWx) {
            this.wxOrder(res.data.wechatJsapi);
            return;
          }
          (void 0).location.href = res.data.wechatH5.h5Url;
        }
      });
    },
    wxOrder(obj) {
      WeixinJSBridge.invoke(
        "getBrandWCPayRequest",
        {
          appId: obj.appId,
          timeStamp: obj.timeStamp,
          nonceStr: obj.nonceStr,
          package: obj.packageStr,
          signType: obj.signType,
          paySign: obj.paySign
        },
        function(res) {
          if (res.err_msg == "get_brand_wcpay_request:ok") {
            Toast.success("支付成功！");
            setTimeout(() => {
              (void 0).location.reload();
            }, 1e3);
          } else if (res.err_msg == "get_brand_wcpay_request:cancel") ;
        }
      );
    }
  }
};
function _sfc_ssrRender(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  var _a, _b, _c, _d, _e, _f, _g, _h, _i, _j, _k, _l, _m, _n, _o;
  const _component_el_button = resolveComponent("el-button");
  const _component_van_checkbox = resolveComponent("van-checkbox");
  _push(`<div${ssrRenderAttrs(mergeProps({
    class: ["vip", { sp: $options.isMedSci() }]
  }, _attrs))} data-v-a789531f>`);
  if (!$options.isMedSci()) {
    _push(`<div class="vip-head" data-v-a789531f>${ssrInterpolate((_a = $props.currentItem) == null ? void 0 : _a.appName)}</div>`);
  } else {
    _push(`<!---->`);
  }
  _push(`<div class="vip-introduce" data-v-a789531f><img class="crown" src="https://static.medsci.cn/public-image/ms-image/1dcd7d10-58af-11ec-8e2f-1389d01aad85_crown.png" alt="" data-v-a789531f><div class="box" data-v-a789531f>`);
  if (!$data.isLogin) {
    _push(`<div class="box-left-1" data-v-a789531f><img src="https://img.medsci.cn/web/img/user_icon.png" alt="" data-v-a789531f><div class="left2" data-v-a789531f><span class="t1" style="${ssrRenderStyle({ "cursor": "pointer" })}" data-v-a789531f>立即登录</span><span class="t2" data-v-a789531f>请登录后购买</span></div></div>`);
  } else {
    _push(`<div class="box-left" data-v-a789531f><img class="avatar"${ssrRenderAttr("src", $data.avatar)} alt="" data-v-a789531f><div class="box-word" data-v-a789531f><span class="t1" data-v-a789531f>${ssrInterpolate($props.userInfo.realName || $props.userInfo.userName)}</span></div></div>`);
  }
  _push(`</div></div> ${ssrInterpolate($props.subStatusDetail.subStatus == "3")} <div class="${ssrRenderClass([$data.language == "zh-CN" && ($props.subStatusDetail.subStatus == "3" || $props.subStatusDetail.subStatus == "1" && $props.subStatusDetail.packageType == "免费") && $data.activeItem.type == $props.subStatusDetail.packageType ? "vip-main-3" : "", "vip-main"])}" data-v-a789531f><div class="vip-one" data-v-a789531f><div class="big" data-v-a789531f>`);
  if ((_b = $props.currentItem) == null ? void 0 : _b.feeTypes) {
    _push(`<ul data-v-a789531f><!--[-->`);
    ssrRenderList((_c = $props.currentItem) == null ? void 0 : _c.feeTypes, (item, index) => {
      var _a2, _b2, _c2, _d2;
      _push(`<li class="${ssrRenderClass({ sactvie: item.type == $data.activeItem.type, noClick: ((_a2 = $props.subStatusDetail) == null ? void 0 : _a2.packageType) == "连续包月" && item.type == "免费" || ((_b2 = $props.subStatusDetail) == null ? void 0 : _b2.packageType) == "连续包月" && item.type == "连续包年" || (((_c2 = $props.subStatusDetail) == null ? void 0 : _c2.packageType) == "连续包年" && item.type == "免费" || ((_d2 = $props.subStatusDetail) == null ? void 0 : _d2.packageType) == "连续包年" && item.type == "连续包月") })}" data-v-a789531f><div class="title ellipsis-2-lines" data-v-a789531f>${ssrInterpolate(item.type == "免费" ? "免费" : _ctx.$t(`tool.${item.type}`))}</div><div class="price" data-v-a789531f><span data-v-a789531f>${ssrInterpolate(item.coinType == "人民币" ? "¥" : "$")}</span>${ssrInterpolate(item.feePrice)}</div>`);
      if (item.originalPrice) {
        _push(`<div class="isfava" data-v-a789531f>${ssrInterpolate(item.coinType == "人民币" ? "¥" : "$")}${ssrInterpolate(item.feePrice)}</div>`);
      } else {
        _push(`<!---->`);
      }
      _push(`</li>`);
    });
    _push(`<!--]--></ul>`);
  } else {
    _push(`<ul data-v-a789531f><!--[-->`);
    ssrRenderList((_d = $props.subStatusDetail) == null ? void 0 : _d.feeTypes, (item, index) => {
      _push(`<li class="${ssrRenderClass({ sactvie: item.type == $data.activeItem.type })}" data-v-a789531f><div class="title ellipsis-2-lines" data-v-a789531f>${ssrInterpolate(item.type == "免费" ? "免费" : _ctx.$t(`tool.${item.type}`))}</div><div class="price" data-v-a789531f><span data-v-a789531f>${ssrInterpolate(item.coinType == "人民币" ? "¥" : "$")}</span>${ssrInterpolate(item.feePrice)}</div>`);
      if (item.originalPrice) {
        _push(`<div class="isfava" data-v-a789531f>${ssrInterpolate(item.coinType == "人民币" ? "¥" : "$")}${ssrInterpolate(item.feePrice)}</div>`);
      } else {
        _push(`<!---->`);
      }
      _push(`</li>`);
    });
    _push(`<!--]--></ul>`);
  }
  _push(`</div></div>`);
  if ($data.language == "zh-CN") {
    _push(`<div data-v-a789531f><div class="vip-two" style="${ssrRenderStyle({ "padding-bottom": "0" })}" data-v-a789531f><div class="vip-two_banner" data-v-a789531f><div class="vip-two_title" data-v-a789531f><img src="https://img.medsci.cn/202503/36f8fcc00cb841eaaa9ff81b3b225285-Ar3LIaiI1GQo.png" data-v-a789531f>梅斯小智 订阅说明</div><div class="vip-two_content" data-v-a789531f><div data-v-a789531f>免费：每个自然月内，每个智能体的使用上限${ssrInterpolate($data.num)}次。次月开始重新计次。</div><div data-v-a789531f>连续包月：订阅之日起一个月内，每个智能体不限使用次数。</div><div data-v-a789531f>连续包年：订阅之日起一年内，每个智能体不限使用次数</div></div></div></div>`);
    if (($props.subStatusDetail.subStatus == "1" || $props.subStatusDetail.subStatus == "3") && $data.activeItem.type == $props.subStatusDetail.packageType) {
      _push(`<div style="${ssrRenderStyle({ "text-align": "center" })}" data-v-a789531f>${ssrInterpolate($props.subStatusDetail.subAt)} 已订阅</div>`);
    } else {
      _push(`<!---->`);
    }
    if ($props.subStatusDetail.subStatus == "1" && $props.subStatusDetail.packageType == "免费" && $data.activeItem.type == "免费") {
      _push(`<div style="${ssrRenderStyle({ "text-align": "center" })}" data-v-a789531f>免费使用中…</div>`);
    } else {
      _push(`<!---->`);
    }
    if ($props.subStatusDetail.subStatus == "3" && $data.activeItem.type == $props.subStatusDetail.packageType) {
      _push(`<div style="${ssrRenderStyle({ "text-align": "center" })}" data-v-a789531f>${ssrInterpolate($props.subStatusDetail.unSubAt)} 取消订阅</div>`);
    } else {
      _push(`<!---->`);
    }
    if ($props.subStatusDetail.subStatus == "3" && $data.activeItem.type == $props.subStatusDetail.packageType) {
      _push(`<div style="${ssrRenderStyle({ "text-align": "center" })}" data-v-a789531f>您的订阅可使用至 ${ssrInterpolate($props.subStatusDetail.expireAt)}</div>`);
    } else {
      _push(`<!---->`);
    }
    if ($props.subStatusDetail.packageType == "连续包月" && $props.subStatusDetail.subStatus == "1") {
      _push(`<div style="${ssrRenderStyle({ "text-align": "center" })}" data-v-a789531f>连续包月中…</div>`);
    } else {
      _push(`<!---->`);
    }
    if ($props.subStatusDetail.packageType == "连续包年" && $props.subStatusDetail.subStatus == "1") {
      _push(`<div style="${ssrRenderStyle({ "text-align": "center" })}" data-v-a789531f>连续包年中…</div>`);
    } else {
      _push(`<!---->`);
    }
    _push(`</div>`);
  } else {
    _push(`<div class="vip-two" data-v-a789531f><div class="vip-two_banner" data-v-a789531f><div class="vip-two_title" data-v-a789531f><img src="https://img.medsci.cn/202503/36f8fcc00cb841eaaa9ff81b3b225285-Ar3LIaiI1GQo.png" data-v-a789531f>${ssrInterpolate((_e = $props.currentItem) == null ? void 0 : _e.appName)}</div><div class="vip-two_content" data-v-a789531f>${ssrInterpolate((_f = $props.currentItem) == null ? void 0 : _f.appDescription)}</div></div></div>`);
  }
  if ($data.activeItem.feePrice > 0 && ((_g = $data.activeItem) == null ? void 0 : _g.coinType) == "人民币" && $data.language == "zh-CN" && ($data.activeItem.type != "免费" && $props.subStatusDetail.subStatus == "0") || $data.activeItem.type != "免费" && $props.subStatusDetail.subStatus == "2" || $props.subStatusDetail.packageType == "免费" && $data.activeItem.type != "免费") {
    _push(`<div class="vip-three" data-v-a789531f><div class="${ssrRenderClass([{ isWx: $data.isWx }, "pay"])}" data-v-a789531f><img src="https://static.medsci.cn/public-image/ms-image/d465fcf0-5c9c-11ec-8e2f-1389d01aad85_payway.png" alt="" data-v-a789531f><div class="item" data-v-a789531f><div class="item-left" data-v-a789531f><img src="https://static.medsci.cn/public-image/ms-image/19eef410-5893-11ec-8e2f-1389d01aad85_zfb.png" alt="" data-v-a789531f><span data-v-a789531f>支付宝支付</span></div><div class="item-right isCheck" data-v-a789531f><img src="https://static.medsci.cn/public-image/ms-image/e1bd35d0-5c9c-11ec-8e2f-1389d01aad85_checked.png" alt="" data-v-a789531f></div></div></div></div>`);
  } else {
    _push(`<!---->`);
  }
  if ($data.activeItem.feePrice > 0 && ((_h = $data.activeItem) == null ? void 0 : _h.coinType) == "人民币" && $data.language != "zh-CN") {
    _push(`<div class="vip-three" data-v-a789531f><div class="${ssrRenderClass([{ isWx: $data.isWx }, "pay"])}" data-v-a789531f><img src="https://static.medsci.cn/public-image/ms-image/d465fcf0-5c9c-11ec-8e2f-1389d01aad85_payway.png" alt="" data-v-a789531f><div class="item" data-v-a789531f><div class="item-left" data-v-a789531f><img src="https://static.medsci.cn/public-image/ms-image/19eef410-5893-11ec-8e2f-1389d01aad85_zfb.png" alt="" data-v-a789531f><span data-v-a789531f>支付宝支付</span></div><div class="item-right isCheck" data-v-a789531f><img src="https://static.medsci.cn/public-image/ms-image/e1bd35d0-5c9c-11ec-8e2f-1389d01aad85_checked.png" alt="" data-v-a789531f></div></div></div></div>`);
  } else {
    _push(`<!---->`);
  }
  _push(`</div>`);
  if ($data.language == "zh-CN" && ($props.subStatusDetail.subStatus == "1" && $props.subStatusDetail.packageType == "连续包月") || $data.language == "zh-CN" && ($props.subStatusDetail.subStatus == "1" && $data.activeItem.packageType == "连续包年")) {
    _push(`<div class="${ssrRenderClass([$props.subStatusDetail.packageType == "连续包月" && $props.subStatusDetail.subStatus == "1" || $props.subStatusDetail.packageType == "连续包年" && $props.subStatusDetail.subStatus == "1" ? "" : "CN_btns", "vip-pay btns"])}" data-v-a789531f>`);
    if ($props.subStatusDetail.packageType == "连续包月" && $props.subStatusDetail.subStatus == "1") {
      _push(ssrRenderComponent(_component_el_button, {
        onClick: ($event) => $options.cancelSub(),
        type: "primary"
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`取消包月`);
          } else {
            return [
              createTextVNode("取消包月")
            ];
          }
        }),
        _: 1
      }, _parent));
    } else {
      _push(`<!---->`);
    }
    if ($props.subStatusDetail.packageType == "连续包年" && $props.subStatusDetail.subStatus == "1") {
      _push(ssrRenderComponent(_component_el_button, {
        onClick: ($event) => $options.cancelSub(),
        type: "primary"
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`取消包年`);
          } else {
            return [
              createTextVNode("取消包年")
            ];
          }
        }),
        _: 1
      }, _parent));
    } else {
      _push(`<!---->`);
    }
    _push(`</div>`);
  } else {
    _push(`<!---->`);
  }
  if ($data.activeItem.feePrice >= 0 && $data.language == "zh-CN" && ($data.activeItem.type == "免费" && $props.subStatusDetail.subStatus == "0" || $data.activeItem.type != "免费" && $props.subStatusDetail.subStatus == "0" || $data.activeItem.type != "免费" && $props.subStatusDetail.subStatus == "2" || $props.subStatusDetail.packageType == "免费" && $data.activeItem.type != "免费")) {
    _push(`<div class="${ssrRenderClass([($props.subStatusDetail.subStatus == "1" || $props.subStatusDetail.subStatus == "3") && $data.activeItem.type == "免费" ? "CN_btns" : "", "vip-pay btns"])}" data-v-a789531f>`);
    if ($data.activeItem.feePrice != 0 && ((_i = $data.activeItem) == null ? void 0 : _i.coinType) == "人民币") {
      _push(`<div class="pay-left" data-v-a789531f><div class="t1" data-v-a789531f>${ssrInterpolate((_j = $props.currentItem) == null ? void 0 : _j.appName)}</div><div class="${ssrRenderClass([{ shake: $data.isShaking }, "t2"])}" data-v-a789531f>`);
      _push(ssrRenderComponent(_component_van_checkbox, {
        modelValue: $data.radio,
        "onUpdate:modelValue": ($event) => $data.radio = $event
      }, null, _parent));
      _push(`<span data-v-a789531f>请在阅读并同意<span data-v-a789531f>协议</span>后开通</span></div></div>`);
    } else {
      _push(`<!---->`);
    }
    if ($data.activeItem.feePrice != 0 && ((_k = $data.activeItem) == null ? void 0 : _k.coinType) == "人民币") {
      _push(`<div class="pay-right" data-v-a789531f><span data-v-a789531f>${ssrInterpolate($data.activeItem.feePrice)}元确认协议并支付</span></div>`);
    } else {
      _push(`<!---->`);
    }
    if ($data.activeItem.feePrice == 0 && ($props.subStatusDetail.subStatus == "0" || $props.subStatusDetail.subStatus == "2")) {
      _push(ssrRenderComponent(_component_el_button, {
        onClick: ($event) => {
          var _a2;
          return $options.subscribe($data.activeItem, (_a2 = $props.currentItem) == null ? void 0 : _a2.appUuid);
        },
        type: "primary"
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`${ssrInterpolate(_ctx.$t("tool.Free_Trial"))}`);
          } else {
            return [
              createTextVNode(toDisplayString(_ctx.$t("tool.Free_Trial")), 1)
            ];
          }
        }),
        _: 1
      }, _parent));
    } else {
      _push(`<!---->`);
    }
    _push(`</div>`);
  } else {
    _push(`<!---->`);
  }
  if ($data.activeItem.feePrice >= 0 && $data.language != "zh-CN") {
    _push(`<div class="vip-pay btns" data-v-a789531f>`);
    if ($data.activeItem.feePrice != 0 && ((_l = $data.activeItem) == null ? void 0 : _l.coinType) == "人民币") {
      _push(`<div class="pay-left" data-v-a789531f><div class="t1" data-v-a789531f>${ssrInterpolate((_m = $props.currentItem) == null ? void 0 : _m.appName)}</div><div class="${ssrRenderClass([{ shake: $data.isShaking }, "t2"])}" data-v-a789531f>`);
      _push(ssrRenderComponent(_component_van_checkbox, {
        modelValue: $data.radio,
        "onUpdate:modelValue": ($event) => $data.radio = $event
      }, null, _parent));
      _push(`<span data-v-a789531f>请在阅读并同意<span data-v-a789531f>协议</span>后开通</span></div></div>`);
    } else {
      _push(`<!---->`);
    }
    if ($data.activeItem.feePrice != 0 && ((_n = $data.activeItem) == null ? void 0 : _n.coinType) == "人民币") {
      _push(`<div class="pay-right" data-v-a789531f><span data-v-a789531f>${ssrInterpolate($data.activeItem.feePrice)}元确认协议并支付</span></div>`);
    } else {
      _push(`<!---->`);
    }
    if ($data.activeItem.feePrice == 0) {
      _push(ssrRenderComponent(_component_el_button, {
        onClick: ($event) => {
          var _a2;
          return $options.subscribe($data.activeItem, (_a2 = $props.currentItem) == null ? void 0 : _a2.appUuid);
        },
        type: "primary"
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`${ssrInterpolate(_ctx.$t("tool.Free_Trial"))}`);
          } else {
            return [
              createTextVNode(toDisplayString(_ctx.$t("tool.Free_Trial")), 1)
            ];
          }
        }),
        _: 1
      }, _parent));
    } else {
      _push(`<!---->`);
    }
    if ($data.activeItem.feePrice > 0 && ((_o = $data.activeItem) == null ? void 0 : _o.coinType) == "美元") {
      _push(ssrRenderComponent(_component_el_button, {
        onClick: ($event) => {
          var _a2;
          return $options.subscribe($data.activeItem, (_a2 = $props.currentItem) == null ? void 0 : _a2.appUuid);
        },
        type: "primary"
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`${ssrInterpolate(_ctx.$t("market.subscribe"))}`);
          } else {
            return [
              createTextVNode(toDisplayString(_ctx.$t("market.subscribe")), 1)
            ];
          }
        }),
        _: 1
      }, _parent));
    } else {
      _push(`<!---->`);
    }
    _push(`</div>`);
  } else {
    _push(`<!---->`);
  }
  _push(`</div>`);
}
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("components/payMobile/index.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const payMobile = /* @__PURE__ */ _export_sfc(_sfc_main, [["ssrRender", _sfc_ssrRender], ["__scopeId", "data-v-a789531f"]]);

const getAssetsFile = (path) => {
  return `/img/${encodeURIComponent(path)}`;
};

export { payMobile as a, getAssetsFile as g, pay as p };
//# sourceMappingURL=index.mjs.map
