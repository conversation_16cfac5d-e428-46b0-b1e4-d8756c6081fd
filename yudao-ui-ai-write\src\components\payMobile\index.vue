<template>
  <div class="vip" :class="{ sp: isMedSci() }">
    <div v-if="!isMedSci()" class="vip-head">
      {{ currentItem.appName }}
    </div>
    <div class="vip-introduce">
      <img
        class="crown"
        src="https://static.medsci.cn/public-image/ms-image/1dcd7d10-58af-11ec-8e2f-1389d01aad85_crown.png"
        alt=""
      />
      <div class="box">
        <div v-if="!isLogin" class="box-left-1">
          <img src="https://img.medsci.cn/web/img/user_icon.png" alt="" />
          <div class="left2">
            <span class="t1" style="cursor: pointer" @click="login"
              >立即登录</span
            >
            <span class="t2">请登录后购买</span>
          </div>
        </div>
        <div v-else class="box-left">
          <img class="avatar" :src="avatar" alt="" @error="changeImg" />
          <div class="box-word">
            <span class="t1">{{ userInfo.realName || userInfo.userName }}</span>
          </div>
        </div>
      </div>
    </div>
    <div class="vip-main">
      <div class="vip-one">
        <div class="big">
          <ul ref="scroll">
            <li
              v-for="(item, index) in currentItem.feeTypes"
              :key="index"
              :class="{ sactvie: item.type == activeItem.type }"
              @click="CheckItem(item)"
            >
              <div class="title ellipsis-2-lines">{{ $t(`tool.${item.type}`) }}</div>
              <div class="price"><span>{{ item.coinType == "人民币" ? "¥" : "$" }}</span>{{ item.feePrice }}</div>
              <div v-if="item.originalPrice" class="isfava">
                {{ item.coinType == "人民币" ? "¥" : "$" }}{{ item.feePrice }}
              </div>
            </li>
          </ul>
        </div>
      </div>
      <div class="vip-two">
          <div class="vip-two_banner">
            <div class="vip-two_title"><img src="https://img.medsci.cn/202503/36f8fcc00cb841eaaa9ff81b3b225285-Ar3LIaiI1GQo.png" />{{ currentItem.appName }}</div>
            <div class="vip-two_content">{{ currentItem.appDescription }} </div>
          </div>
      </div>
      <div class="vip-three" v-if="activeItem.feePrice > 0 && socialType == 0">
        <!-- <span class="head" v-if="activeItem.salePricedDescription">{{
          activeItem.salePricedDescription
        }}</span> -->
        <div class="pay" :class="{ isWx: isWx }">
          <img
            src="https://static.medsci.cn/public-image/ms-image/d465fcf0-5c9c-11ec-8e2f-1389d01aad85_payway.png"
            alt=""
          />
          <div class="item" @click="checkFn2">
            <div class="item-left">
              <img
                src="https://static.medsci.cn/public-image/ms-image/19eef410-5893-11ec-8e2f-1389d01aad85_zfb.png"
                alt=""
              />
              <span>支付宝支付</span>
            </div>
            <div class="item-right isCheck">
              <img
                src="https://static.medsci.cn/public-image/ms-image/e1bd35d0-5c9c-11ec-8e2f-1389d01aad85_checked.png"
                alt=""
              />
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="vip-pay btns" v-if="activeItem.feePrice >= 0">
      <div class="pay-left" v-if="activeItem.feePrice != 0 && socialType == 0">
        <div class="t1">{{ currentItem.appName }}</div>
        <div class="t2" :class="{ shake: isShaking }">
          <van-checkbox v-model="radio" /><span @click="goAgreent"
            >请在阅读并同意<span>协议</span>后开通</span
          >
        </div>
      </div>
      <div
        class="pay-right"
        v-if="activeItem.feePrice != 0 && socialType == 0"
        @click="subscribe(activeItem, currentItem.appUuid, 'ali')"
      >
        <span>{{ activeItem.feePrice }}元确认协议并支付</span>
      </div>
      <el-button
        v-if="activeItem.feePrice == 0"
        @click="subscribe(activeItem, currentItem.appUuid)"
        type="primary"
        >{{ $t("tool.Free_Trial") }}</el-button
      >
      <el-button
        v-if="activeItem.feePrice > 0 && socialType != 0"
        @click="subscribe(activeItem, currentItem.appUuid)"
        type="primary"
        >{{ $t("market.subscribe") }}</el-button
      >
    </div>
  </div>
</template>

<script>
import { Toast, Checkbox as VanCheckbox } from "vant";
import { createSubscription, createAliSub } from "@/api/dify";
import cookie from "js-cookie";
import { getDefaultLanguageCode } from "@/common/commonJs";
export default {
  name: "Vip",
  data() {
    return {
      isCheckW: true,
      isCheckZ: false,
      // userInfo: {},
      isLogin: false,
      activeItem: {},
      appId: "wx9096048917ec59ab",
      appOrderId: "",
      isClick: false,
      openId: "",
      isWx: false,
      choseUserVip: {},
      isFromMedsci: false,
      showAll: false,
      checkCount: 0,
      vipTypeList: [],
      activeType: 0,
      active: 0,
      radio: "",
      isShaking: false,
      avatar: "",
      socialType: localStorage.getItem("socialType"),
    };
  },
  components: {
    VanCheckbox,
  },
  props: {
    userInfo: {
      type: Object,
      default: () => ({}),
    },
    currentItem: {
      type: Object,
      default: () => ({}),
    },
  },

  created() {},
  mounted() {
    const windowVH = window.innerHeight / 100;
  document.documentElement.style.setProperty("--vh", `${windowVH}px`);
    if(this.currentItem.appType == "写作"){
    localStorage.setItem(
          "appWrite"+'-'+this.currentItem.appUuid,
          JSON.stringify({
            appUuid: this.currentItem.appUuid,
            directoryMd: this.currentItem.directoryMd,
          })
        );
      }
    this.avatar = this.userInfo?.avatar
      ? this.userInfo?.avatar
      : "https://img.medsci.cn/web/img/user_icon.png";
    this.isUp =
      location?.origin.includes("medon.com.cn") ||
      location?.origin.includes("medsci.cn");
    if (this.currentItem.feeTypes.length == 1) {
      if (
        this.currentItem.feeTypes[0].feePrice > 0 &&
        localStorage.getItem("socialType") == 0
      ) {
        this.CheckItem(this.currentItem.feeTypes[0], this.currentItem.appUuid);
      }
      if (this.currentItem.feeTypes[0].feePrice == 0) {
        this.CheckItem(this.currentItem.feeTypes[0], this.currentItem.appUuid);
      }
    }
    if (cookie.get("userInfo") && JSON.parse(cookie.get("userInfo")).userId) {
      this.isLogin = true;

      this.initUser();
    }
    this.init();
    if (this.$route.query.source && this.$route.query.source == "medsci") {
      this.isFromMedsci = true;
    }
  },
  methods: {
    changeImg() {
      this.avatar = "https://img.medsci.cn/web/img/user_icon.png";
    },
    // 支付
    async subscribe(item, appUuid, type) {
      if (!this.radio && type) {
        this.isShaking = true;
        setTimeout(() => {
          this.isShaking = false;
        }, 500);
        return;
      }
      let language = await getDefaultLanguageCode();
      if (!this.userInfo?.userId) {
        // country='xxxx' // 模拟国外
        // 判断是否是国内用户访问
        if (!language || language == "zh") {
          window.addLoginDom();
        } else {
          top.location.href = location.origin + '/' + language + "/login"
        }
      } else {
        const subscriptionParams = {
          appUuid: appUuid,
          priceId: item.priceId,
          monthNum: item.monthNum,
        };
        try {
          let res = await createSubscription(subscriptionParams);
          if (res) {
            if (item.coinType == "人民币"&& item.feePrice != 0) {
              let resUrl = await createAliSub(JSON.parse(res.data));
              ElMessage({
                type: "success",
                message: this.$t("tool.sS"),
              });
              setTimeout(() => {
                top.location.href = resUrl;
              }, 1000);
            } else {
              ElMessage({
                type: "success",
                message: this.$t("tool.sS"),
              });
              setTimeout(() => {
                top.location.href = res.data;
              }, 1000);
            }
          }
        } catch (error) {
          // loading.value = false;
        }
      }
    },
    // 选择支付
    CheckItem(item, index) {
      this.activeItem = item;
      this.active = index;
    },
    openActivity(href) {
      if (href) {
        window.top.location.href = href;
      }
    },
    async login() {
      let language = await getDefaultLanguageCode();
      if (!this.userInfo?.userId) {
        // country='xxxx' // 模拟国外
        // 判断是否是国内用户访问
        if (!language || language == "zh") {
          window.addLoginDom();
        } else {
          top.location.href = location.origin + '/' + language + "/login"
        }
      }
    },
    initUser() {},
    init() {},
    isMedSci() {
      const u = navigator.userAgent;
      return u.includes("medsci_app");
    },
    goBack() {
      window.history.back(-1);
    },
    checkFn1() {
      this.isCheckW = true;
      this.isCheckZ = false;
    },
    checkFn2() {
      this.isCheckW = false;
      this.isCheckZ = true;
    },
    goAgreent() {
      const url =
        process.env.NODE_ENV == "prod"
          ? `https://www.medsci.cn/about/index.do?id=27`
          : `https://portal-test.medon.com.cn/agreement/27`;
      if (this.isMedSci()) {
        window.top.location.href = url;
        return;
      }
      window.open(url);
    },
    // 订单
    createOrder() {
      if (this.isWx) {
        this.isCheckW = true;
        this.isCheckZ = false;
      }
      const params = {
        accessAppId: "college",
        appOrderId: this.appOrderId,
        payChannel: this.isCheckW ? "WX" : "ALI", // 根据userAgent判断 ALI WX
        paySource: "MEDSCI_WEB",
        payType: this.isWx ? "JSAPI" : "MWEB", // MWEB
      };
      console.log(params, "params");
      this.$axios.post(api.payBuild, params).then((res) => {
        console.log(res.data, "paybuild");
        this.orderList(res.data.payOrderId);
      });
    },
    // 流水
    orderList(id) {
      const extParam = {};
      if (this.$route.query.from) {
        extParam.from = "app";
      }
      if (this.isFromMedsci) {
        extParam.sourcefrom = "main";
        extParam.redirectUrl = this.$route.query.redirectUrl;
      }
      const params = {
        accessAppId: "college",
        openId: this.isWx ? this.openId : "", // 微信支付的时候才用
        payOrderId: id, // 支付订单返回的id
        extParam: JSON.stringify(extParam),
      };
      this.$axios.post(api.payOrder, params).then((res) => {
        if (!this.isCheckW) {
          if (res.data.aliH5.html) {
            const div = document.createElement("div");
            div.innerHTML = res.data.aliH5.html;
            document.body.appendChild(div);
            document.forms[0].submit();
          }
        } else {
          if (this.isWx) {
            this.wxOrder(res.data.wechatJsapi);
            return;
          }
          window.top.location.href = res.data.wechatH5.h5Url;
        }
      });
    },
    wxOrder(obj) {
      WeixinJSBridge.invoke(
        "getBrandWCPayRequest",
        {
          appId: obj.appId,
          timeStamp: obj.timeStamp,
          nonceStr: obj.nonceStr,
          package: obj.packageStr,
          signType: obj.signType,
          paySign: obj.paySign,
        },
        function (res) {
          if (res.err_msg == "get_brand_wcpay_request:ok") {
            Toast.success("支付成功！");
            setTimeout(() => {
              window.location.reload();
            }, 1000);
          } else if (res.err_msg == "get_brand_wcpay_request:cancel") {
            console.log(111);
          }
        }
      );
    },
  },
};
</script>

<style scoped lang="scss">


@font-face {
  font-family: YouSheBiaoTiHei;
  src: url("https://static.medsci.cn/font/ys.ttf");
}

.isClick {
  background: #d4d4d4;
  span {
    color: #ffffff;
  }
}

.sp {
  margin-top: -15px;
}
.vip {
  background-color: #fff;
  .vip-head {
    padding-top: 7.5px;
    padding-bottom: 7.5px;
    width: 100%;
    height: 36px;
    text-align: center;
    position: relative;
    color: #333333;

    img {
      position: absolute;
      width: 24px;
      height: 24px;
      left: 19.5px;
      top: 7.5px;
    }

    span {
      width: 60.5px;
      height: 24px;
      font-family: PingFangSC-Medium;
      font-weight: Medium;
      font-size: 17px;
      color: #333333;
      letter-spacing: 0;
      text-align: center;
    }
  }

  .vip-introduce {
    width: 100%;
    height: 146.5px;
    background: #25242b;
    position: relative;
    padding-top: 37px;

    .crown {
      position: absolute;
      right: 2.5px;
      top: 0;
      height: 38px;
      object-fit: contain;
    }

    .lun {
      padding: 2.5px 10.5px;
      position: absolute;
      top: 15.5px;
      right: 14px;
      height: 24px;
      opacity: 0.53;
      overflow: hidden;
      border-radius: 9.5px;

      .ul-scoll {
        li {
          .con {
            overflow: hidden;
            border-radius: 7px;
            font-family: PingFangSC-Medium;
            font-weight: Medium;
            font-size: 11px;
            color: #ffffff;
            letter-spacing: 0;
            margin-bottom: 7px;
            text-align: center;
            padding: 2px 5px;
          }
        }
      }
    }

    .box {
      padding: 12px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 94%;
      margin: 0 auto;
      background: url(https://static.medsci.cn/public-image/ms-image/21ad8b80-58b1-11ec-8e2f-1389d01aad85_vip-h5.png)
        no-repeat;
      background-size: 100%;
      position: relative;

      .box-left-1,
      .box-left,
      .box-left-3 {
        display: flex;
        align-items: center;

        img,
        .avatar {
          width: 33px;
          height: 33px;
          border-radius: 50%;
          margin-right: 12px;
        }

        .left2,
        .box-word,
        .left3 {
          display: flex;
          flex-direction: column;

          .t1 {
            max-width: 175px;
            height: 24px;
            font-family: PingFangSC-Medium;
            font-weight: Medium;
            font-size: 17px;
            color: #58342c;
            letter-spacing: 0;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }

          .t2,
          .sp {
            margin-top: 2.5px;
            height: 16.5px;
            font-family: PingFangSC-Regular;
            font-weight: Regular;
            font-size: 12px;
            color: #7f5947;
            letter-spacing: 0;
          }
        }
      }

      .box-right {
        width: 89.5px;
        height: 26px;
        line-height: 26px;
        text-align: center;
        background: #58342c;
        border-radius: 16px;
        color: #fff;
        font-size: 14px;
      }
    }
  }

  .vip-main {
    border-radius: 14px 14px 0 0;
    position: relative;
    width: 100%;
    margin-top: -52.5px;
    background: #fff;
    overflow-y: auto;
    z-index: 9999;
    padding-bottom: 95.5px;
    .micro_type {
      padding: 0 5px;
      border-bottom: 0.5px solid #f9f9f9;
      display: flex;
      justify-content: start;
      align-items: center;
      background: #fff;
      width: 100%;
      overflow-x: scroll;
      overflow-y: hidden;

      .micro_type_item {
        padding: 0 5px;
        height: 45px;
        line-height: 45px;
        flex: none;
        color: #58342c;
        text-align: center;

        &.item-color {
          height: 45px;
          position: relative;

          &::before {
            content: "";
            display: inline-block;
            position: absolute;
            width: 30px;
            height: 5px;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            background-color: #553028;
          }
        }
      }
    }

    .vip-one {
      padding-left: 11px;
      z-index: 9;

      .head {
        height: 16.5px;
        font-family: PingFangSC-Regular;
        font-weight: Regular;
        font-size: 13px;
        color: #b2b2b2;
        letter-spacing: 0;
      }

      .big {
        margin-top: 5px;
        overflow: auto;
        margin-right: 10px;
      }

      ul {
        margin-top: 9.5px;
        margin-bottom: 2px;
        display: flex;
        align-items: center;

        li {
          min-width: 135px;
          width: 135px;
          background: #ffffff;
          border: 0.5px solid #d6d6d6;
          border-radius: 7px;
          margin-right: 13px;
          position: relative;
          text-align: center;

          .newer {
            position: absolute;
            left: -0.5px;
            top: -6px;

            .box {
              position: relative;
              background-image: linear-gradient(
                99deg,
                #ff9d49 12%,
                #ff6c3b 99%
              );
              border-radius: 4px 0 4px 0;
              z-index: 20;
              padding: 0px 7.5px;

              span {
                font-family: YouSheBiaoTiHei;
                font-weight: 500;
                font-size: 11px;
                color: #ffffff;
                letter-spacing: 0;
                text-align: center;
              }
            }
          }
          .ellipsis-2-lines {
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            overflow: hidden;
            text-overflow: ellipsis;
          }
          .title {
            max-width: 102.5px;
            font-family: PingFangSC-Medium;
            font-weight: Medium;
            font-size: 14px;
            color: #333333;
            letter-spacing: 0;
            margin: 0 auto;
            margin-top: 25px;
          }

          .price {
            height: 33.5px;
            font-family: NotoSansKannada-Bold;
            font-weight: Bold;
            font-size: 25px;
            color: #efa439;
            letter-spacing: 0;
            margin-bottom:25px;
            span{
              font-size: 18px;
            }
          }
          ::-webkit-scrollbar{
            overflow-x:unset
          }
          .isfava {
            position: relative;
            height: 12.5px;
            font-family: PingFangSC-Regular;
            font-weight: Regular;
            font-size: 12px !important;
            color: #999999;
            letter-spacing: 0;
            margin: 7.5px auto;

            &::after {
              position: absolute;
              top: 8px;
              left: 20%;
              content: "";
              width: 10%;
              height: 0.5px;
              background: #999999;
            }
          }

          .pre {
            width: 33%;
            position: absolute;
            bottom: 2.5px;
            left: 8.5%;
            height: 25px;
            font-family: PingFangSC-Regular;
            font-weight: Regular;
            font-size: 12px;
            color: #999999;
            letter-spacing: 0;
          }
        }

        .sactvie {
          background: #fcf7e6;
          border: 0.5px solid #fe9641;
          box-sizing: border-box;
        }
      }
    }

    .vip-three {
      margin-top: 10px;

      .head {
        height: 16.5px;
        font-family: PingFangSC-Regular;
        font-weight: Regular;
        font-size: 12px;
        color: #b2b2b2;
        letter-spacing: 0;
        margin-bottom: 19px;
        padding-left: 14px;
      }

      .pay {
        margin: 15px auto;
        padding: 17.5px 10px;
        margin: 0 10px;
        background-image: linear-gradient(
          0deg,
          rgba(255, 250, 246, 0.64) 0%,
          #fff8f0 100%
        );
        border-radius: 6.5px;

        & > img {
          width: 60px;
          object-fit: contain;
          margin-bottom: 14px;
        }

        .item {
          margin: 0 auto;
          height: 39px;
          background: #ffffff;
          border: 0.5px solid #feb980;
          border-radius: 5px;
          margin-bottom: 11px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding-left: 22px;
          padding-right: 29.5px;

          .item-left {
            display: flex;
            align-items: center;

            img {
              width: 19px;
              height: 19px;
              margin-right: 7px;
            }

            span {
              height: 18.5px;
              font-family: PingFangSC-Regular;
              font-weight: Regular;
              font-size: 13px;
              color: #232323;
              letter-spacing: 0;
            }
          }

          .item-right {
            width: 19px;
            height: 19px;
            border-radius: 50%;
            border: 1.5px solid #c4c4c4;

            img {
              width: 100%;
            }
          }

          .isCheck {
            width: 19px;
            height: 19px;
            position: relative;
            border: none;
          }
        }
      }

      .isWx {
        height: 106px;
      }
    }
    .vip-two {
      border: 1px solid #E0E0E0;
      border-radius: 4px;
      margin: 10px 11px;
      padding:8.5px 7.5px 23px;
      .vip-two_banner{
        width:100%;
        background:url('https://img.medsci.cn/202503/48a49f899b4544e39a864d1821b3555a-lRXRIQx3q1VB.png') no-repeat;
        background-size: 100%  90px;
        padding:0 12px  ;
        box-sizing: border-box;
      }
      .vip-two_title{
        padding: 7px 7.5px;
        font-weight: 500;
        font-size: 14px;
        color: #D7813F;
        letter-spacing: 0;
        display: flex;
        align-items: center;
        img{
          width:8px;
          height: 8px;
          margin:0 2px;
        }
      }
      .vip-two_content{
        background: #fff;
        width:100%;
        padding: 12px 12px;
        font-weight: 400;
        font-size: 13px;
        color: #594210;
        letter-spacing: 0;
        line-height: 18.5px;
        max-height:  calc(var(--vh) * 20);
        overflow: auto;
        border-radius: 4px;
      }
    
    }
  }
  .btns {
    text-align: center;
    margin-top: 10px;
    button {
      width: 194px;
      height: 49px;
      background-image: linear-gradient(270deg, #ffc85e 9%, #fca315 93%);
      border: none;
      border-radius: 26.25px;
    }
  }
  .vip-pay {
    z-index: 9999;
    box-shadow: 0 1px 6px 6px rgba(44, 37, 37, 0.03);
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 95.5px;
    display: flex;
    justify-content: space-around;
    align-items: center;
    background: #fff;

    .pay-left {
      margin-left: 7px;

      .t1 {
        height: 20px;
        font-family: PingFangSC-Medium;
        font-weight: Medium;
        font-size: 14px;
        color: #efa439;
        letter-spacing: 0;
        margin-bottom: 4px;
        text-align: left;
      }

      .t2 {
        height: 15px;
        font-family: PingFangSC-Regular;
        font-weight: Regular;
        font-size: 10px;
        color: #b2b2b2;
        letter-spacing: 0;
        span {
          color: #5ea7ff;
          line-height: 1.2;
        }
        ::v-deep {
          .van-icon-success {
            width: 0.75rem;
            height: 0.75rem;
          }
          .van-checkbox {
            align-items: flex-start;
            display: inline-block;
            margin-right: 2px;
            line-height: 1.2;
            height: 0.75rem;
            vertical-align: middle;
          }
          .van-checkbox__label {
            margin-left: 2px;
            line-height: 1.2;
          }
          .van-checkbox__icon--checked .van-icon {
            width: 14px;
            height: 14px;
            position: relative;
          }
          .van-icon-success::before {
            font-size: 12px; // 调整对号大小
            position: absolute;
            left: -1px;
            top: 19%;
          }

          .van-checkbox__icon .van-icon {
            width: 0.75rem;
            height: 0.75rem;
            line-height: 0.5;
          }
        }
      }
    }

    .pay-right {
      margin-right: 7px;
      width: 183px;
      height: 47px;
      background-image: linear-gradient(90deg, #fde39b 5%, #fbd786 100%);
      border-radius: 26.25px;
      text-align: center;

      span {
        font-family: PingFangSC-Medium;
        font-weight: Medium;
        font-size: 14px;
        color: #614018;
        letter-spacing: 0;
        line-height: 47px;
      }
    }
  }
}
.shake {
  animation: shake 0.5s;
}

@keyframes shake {
  0% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-5px);
  }
  50% {
    transform: translateX(5px);
  }
  75% {
    transform: translateX(-5px);
  }
  100% {
    transform: translateX(0);
  }
}
</style>
