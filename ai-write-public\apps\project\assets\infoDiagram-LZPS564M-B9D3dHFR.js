import{_ as e,l as s,H as n,e as i,I as p}from"./index-DP5_VPip.js";import{p as g}from"./radar-VG2SY3DT-CMfTJXkH.js";import"./_baseUniq-BKS-qVNY.js";import"./_basePickBy-B_0H3py7.js";import"./clone-DLidW0wj.js";var v={parse:e(async r=>{const a=await g("info",r);s.debug(a)},"parse")},d={version:p.version+""},m=e(()=>d.version,"getVersion"),c={getVersion:m},l=e((r,a,o)=>{s.debug(`rendering info diagram
`+r);const t=n(a);i(t,100,400,!0),t.append("g").append("text").attr("x",100).attr("y",40).attr("class","version").attr("font-size",32).style("text-anchor","middle").text(`v${o}`)},"draw"),f={draw:l},S={parser:v,db:c,renderer:f};export{S as diagram};
