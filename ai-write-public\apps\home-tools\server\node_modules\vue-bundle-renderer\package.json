{"name": "vue-bundle-renderer", "version": "2.1.1", "description": "Bundle Renderer for Vue 3.0", "repository": "nuxt-contrib/vue-bundle-renderer", "license": "MIT", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}, "./runtime": {"types": "./dist/runtime.d.ts", "import": "./dist/runtime.mjs", "require": "./dist/runtime.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["dist", "runtime.d.ts"], "scripts": {"build": "unbuild", "dev": "vitest", "lint": "eslint src", "prepack": "unbuild", "release": "pnpm test && pnpm build && changelogen --release --push && npm publish", "test": "pnpm lint && pnpm vitest run --coverage && tsc --noEmit"}, "dependencies": {"ufo": "^1.5.4"}, "devDependencies": {"@types/node": "^20.16.6", "@vitest/coverage-v8": "^2.1.1", "changelogen": "^0.5.7", "eslint": "^9.11.1", "typescript": "^5.6.2", "unbuild": "^2.0.0", "vite": "^5.4.7", "vitest": "2.1.1", "vue": "3.5.8", "@nuxt/eslint-config": "^0.5.7"}, "packageManager": "pnpm@9.11.0"}