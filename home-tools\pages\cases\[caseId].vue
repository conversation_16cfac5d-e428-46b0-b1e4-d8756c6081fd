<template>
  <div class="relative h-screen" :style="{ backgroundColor: 'var(--bg-main)' }">
    <div class="h-screen flex flex-col">
      <!-- 移动端头部 - 固定在顶部 -->
      <div class="md:hidden fixed top-0 left-0 right-0 z-50 bg-white border-b border-gray-200 px-4 py-3 shadow-sm">
        <div class="flex items-center">
          <button
            @click="handleGoBack"
            class="flex items-center justify-center w-8 h-8 rounded-full hover:bg-gray-100 transition-colors mr-3"
          >
            <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
            </svg>
          </button>
          <div class="flex items-center flex-1">
            <div class="w-8 h-8 bg-gradient-to-r rounded-full flex items-center justify-center mr-3">
              <img
                :src="appName=='Novax Base'? 'https://ai.medon.com.cn/dev-api/admin-api/infra/file/4/get/6c1534c1fdbea7aeedad03bd65aff86bc2dccb2347f8889745efc725538c7f36.png':appName.value=='ElavaX Base'?'https://ai.medon.com.cn/dev-api/admin-api/infra/file/4/get/fbf91c551baa82e54b413ccec5f166d2f810db5f8fb928919f0d9f8a344d5664.png':'https://img.medsci.cn/202412/8a5663cb16a84c45b9c8c5db4eda0075-6pyGtTcj0asd.png'"
                :alt="appName"
                class="w-5 h-5 rounded-full"
              />
            </div>
            <span class="text-lg font-semibold text-gray-800">{{ appName}}</span>
          </div>

          <!-- 价格 - 移动端 -->
          <button
            @click="handleViewResults"
            class="flex items-center justify-center px-3 py-1.5 bg-blue-500 hover:bg-blue-600 text-white text-sm rounded-xl transition-colors duration-200"
            :title="isInstantMode ? $t('case.viewReplay') : $t('case.viewResults')"
          >
            <span>{{ isInstantMode ? $t('case.viewReplay') : $t('case.viewResults') }}</span>
          </button>
        </div>
      </div>

      <!-- 桌面端案例标题 -->
      <div class="hidden md:block bg-white border-b border-gray-200 px-6 py-4 shadow-sm">
        <div class="max-w-4xl mx-auto">
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-3">
              <div class="flex flex-col items-center">
                <div class="w-10 h-10 bg-gradient-to-r rounded-full flex items-center justify-center text-white font-medium mb-1">
                  <img
                    :src="appName=='Novax Base'? 'https://ai.medon.com.cn/dev-api/admin-api/infra/file/4/get/6c1534c1fdbea7aeedad03bd65aff86bc2dccb2347f8889745efc725538c7f36.png':appName.value=='ElavaX Base'?'https://ai.medon.com.cn/dev-api/admin-api/infra/file/4/get/fbf91c551baa82e54b413ccec5f166d2f810db5f8fb928919f0d9f8a344d5664.png':'https://img.medsci.cn/202412/8a5663cb16a84c45b9c8c5db4eda0075-6pyGtTcj0asd.png'"
                    :alt="appName"
                    class="w-6 h-6 rounded-full"
                  />
                </div>
                <span class="text-xs text-gray-600 font-medium text-center block w-full">{{ appName }}</span>
              </div>
              <div>
                <h1 class="text-lg font-semibold text-gray-800">
                  {{ caseTitle }}
                </h1>
                <p class="text-sm">
                  {{ $t('case.caseId') }} <span class="text-blue-600 font-medium">{{ caseId }}</span>
                </p>
              </div>
            </div>

            <!-- 按钮 - 桌面端 -->
            <button
              @click="handleViewResults"
              class="flex items-center justify-center px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white text-sm rounded-xl transition-colors duration-200 shadow-sm hover:shadow-md"
              :title="isInstantMode ? $t('case.viewReplay') : $t('case.viewResults')"
            >
              <span>{{ isInstantMode ? $t('case.viewReplay') : $t('case.viewResults') }}</span>
            </button>
          </div>
        </div>
      </div>

      <!-- 消息列表区域 -->
      <div
        ref="messagesContainer"
        class="flex-1 px-4 py-6 pb-48 overflow-y-auto bg-gray-50 pt-16 md:pt-6"
      >
        <div class="max-w-4xl mx-auto">
          <div
            v-for="message in messages"
            :key="message.id"
            class="mx-auto max-w-4xl relative group mb-6"
          >
            <!-- 用户消息 -->
            <div
              v-if="message.type === 'user'"
              class="ml-auto max-w-2xl bg-gray-0 border border-gray-200 rounded-2xl rounded-br-md p-4 shadow-sm hover:shadow-md transition-all duration-200"
            >
              <div class="text-gray-800 text-base leading-relaxed">
                <AiResponseRenderer
                  :content="message.content"
                  font-size="base"
                />
              </div>
            </div>
            
            <!-- AI消息 -->
            <div
              v-else
              class="mr-auto max-w-4xl"
            >
              <div class="text-base text-gray-800 leading-relaxed prose prose-base max-w-none">
                <!-- 打字机效果 -->
                <div v-if="message.isGenerating && !isInstantMode && typingStates[message.id]">
                  <AiResponseRenderer
                    :content="(displayTexts[message.id] || '') + (typingStates[message.id] ? '▊' : '')"
                    font-size="lg"
                    :is-typing="true"
                  />
                </div>
                <!-- 最终渲染 -->
                <div v-else>
                  <AiResponseRenderer
                    :content="message.content"
                    font-size="lg"
                  />
                </div>
              </div>
            </div>
          </div>
          <div ref="messagesEndRef" />
        </div>
      </div>
    </div>

    <!-- 悬浮按钮 - 固定在屏幕左侧，不显示新建会话按钮 -->
    <!-- <div class="hidden md:block">
      <FloatingButtons :show-new-chat="false" />
    </div> -->

    <!-- SEO专用的qa-list内容 - 对搜索引擎可见，对用户隐藏 -->
    <SeoQaList
      :case-id="caseId"
      :case-title="seoTitle || caseTitle"
      :qa-data="seoQaData"
    />
  </div>
</template>

<script setup lang="ts">
// 导入必要的组件和工具
import AiResponseRenderer from '~/components/AiResponseRenderer/index.vue'
import SeoQaList from '~/components/SeoQaList.vue'
import { getAppByUuid, qaList } from "@/api/base"
import { useSmartScroll } from '~/composables/useSmartScroll'

interface ChatMessage {
  id: string
  type: 'user' | 'assistant'
  content: string
  timestamp: Date
  isGenerating?: boolean
}

interface QAItem {
  query: string
  answer: string
}

interface Props {
  currentAppUuid?: string
}

// 定义props
const props = withDefaults(defineProps<Props>(), {
  currentAppUuid: ''
})

// 获取路由和国际化
const route = useRoute()
const router = useRouter()
const { t } = useI18n()

// 路由参数
const caseId = computed(() => route.params.caseId as string)
const lang = computed(() => route.params.lang as string)

// 响应式数据
const messages = ref<ChatMessage[]>([])
const isProcessing = ref(false)
const hasInitialized = ref(false)
const caseTitle = ref(t('case.title'))
const isInstantMode = ref(false)
const hasShownInstant = ref(false)
const currentApp = ref<any>(null)
const messagesEndRef = ref<HTMLDivElement>()
const messagesContainer = ref<HTMLDivElement>()
//应用
const appName = ref('')
// 用于存储定时器引用
const timersRef = ref<ReturnType<typeof setTimeout>[]>([])
const isRequestingRef = ref(false)
const currentMessageIndexRef = ref(0)
const allMessagesRef = ref<ChatMessage[]>([])

// 打字机效果相关
const displayTexts = ref<Record<string, string>>({})
const typingStates = ref<Record<string, boolean>>({})

// 智能滚动功能
const {
  smartScroll,
  initScrollContainer,
  isAutoScrollEnabled,
  isUserScrolling,
  enableAutoScroll,
  scrollToBottom
} = useSmartScroll({
  bottomThreshold: 50,
  scrollBehavior: 'smooth',
  userScrollDebounce: 150,
  debug: false // 生产环境设为false
})

// SEO相关数据
const seoQaData = ref<QAItem[]>([])
const seoTitle = ref('')

// 服务端数据预取 - 用于SEO
const { data: seoData } = await useAsyncData(
  `case-seo-${caseId.value}`,
  async () => {
    try {
      const event = useRequestEvent()
      console.log('服务端预取SEO数据，案例ID:', caseId.value)

      // 调用qaList API获取完整数据
      const response = await qaList({
        articleId: "",
        encryptionId: caseId.value
      }, event)

      if (response && Array.isArray(response) && response.length > 0) {
        const firstItem = response[0]
        let qaItems: QAItem[] = []
        let title = ''

        // 提取标题
        if (firstItem && firstItem.question) {
          title = firstItem.question
        }
      appName.value = firstItem.userName
      console.log('appName:', appName.value)
        // 解析answer数据
        if (firstItem && firstItem.answer) {
          let answerData = firstItem.answer

          if (typeof answerData === 'string') {
            try {
              answerData = JSON.parse(answerData)
            } catch (parseError) {
              console.error('解析answer数据失败:', parseError)
              return { qaItems: [], title: '' }
            }
          }

          if (Array.isArray(answerData) && answerData.length > 0) {
            qaItems = answerData
          }
        }

        console.log('服务端预取成功，QA项目数量:', qaItems.length)
        return { qaItems, title }
      }

      return { qaItems: [], title: '' }
    } catch (error) {
      console.error('服务端预取SEO数据失败:', error)
      return { qaItems: [], title: '' }
    }
  },
  {
    server: true, // 确保只在服务端执行
    default: () => ({ qaItems: [], title: '' })
  }
)

// 设置SEO数据
if (seoData.value) {
  seoQaData.value = seoData.value.qaItems || []
  seoTitle.value = seoData.value.title || ''

  // 如果服务端获取到了标题，更新caseTitle
  if (seoData.value.title) {
    caseTitle.value = seoData.value.title
  }
}

// 返回上一页
const handleGoBack = () => {
  router.back()
}

// 注意：scrollToBottom 现在由智能滚动系统提供

// 清理定时器的函数
const clearAllTimers = () => {
  timersRef.value.forEach(timer => clearTimeout(timer))
  timersRef.value = []
}

// 打字机效果
const startTypewriter = (messageId: string, text: string) => {
  displayTexts.value[messageId] = ''
  typingStates.value[messageId] = true

  // 启用自动滚动（打字机开始时）
  enableAutoScroll()

  let currentIndex = 0
  const speed = 0

  const typeNextChar = () => {
    if (currentIndex < text.length) {
      displayTexts.value[messageId] += text[currentIndex]
      currentIndex++

      // 使用智能滚动：每5个字符触发一次
      if (currentIndex % 5 === 0) {
        nextTick(() => {
          smartScroll()
        })
      }

      const timer = setTimeout(typeNextChar, speed)
      timersRef.value.push(timer)
    } else {
      typingStates.value[messageId] = false

      // 打字机完成时最后滚动一次
      nextTick(() => {
        smartScroll()
      })

      // 延迟后触发完成回调
      const timer = setTimeout(() => {
        handleTypingComplete(messageId)
      }, 0)
      timersRef.value.push(timer)
    }
  }

  typeNextChar()
}

// 案例不存在时的处理函数
const handleCaseNotFound = () => {
  // 这里需要根据您的消息提示组件来调整
  setTimeout(() => {
    router.push(lang.value ? `/${lang.value}` : '/')
  }, 3000)
}


// 获取案例数据
const fetchCaseData = async () => {
  if (isRequestingRef.value) return

  isRequestingRef.value = true
  try {
    // 调用qaList API获取案例数据
    const response = await qaList({
      articleId: "",
      encryptionId: caseId.value
    })

    if (response && Array.isArray(response) && response.length > 0) {
      hasInitialized.value = true

      // 提取question字段作为标题
      const firstItem = response[0]
      if (firstItem && firstItem.question) {
        caseTitle.value = firstItem.question
      }

      generateMessagesFromCaseData(response)
    } else {
      handleCaseNotFound()
    }
  } catch (error) {
    handleCaseNotFound()
  } finally {
    isRequestingRef.value = false
  }
}

// 根据案例数据生成对话消息
const generateMessagesFromCaseData = (caseData: any) => {
  if (isProcessing.value) {
    return
  }

  clearAllTimers()
  messages.value = []
  isProcessing.value = true

  let qaItems: QAItem[] = []

  try {
    if (caseData && Array.isArray(caseData) && caseData.length > 0) {
      const firstItem = caseData[0]
      if (firstItem && firstItem.answer) {
        let answerData = firstItem.answer

        if (typeof answerData === 'string') {
          try {
            answerData = JSON.parse(answerData)
          } catch (parseError) {
            handleCaseNotFound()
            return
          }
        }

        if (Array.isArray(answerData) && answerData.length > 0) {
          qaItems = answerData
        } else {
          handleCaseNotFound()
          return
        }
      } else {
        handleCaseNotFound()
        return
      }
    } else {
      handleCaseNotFound()
      return
    }

    if (qaItems.length === 0) {
      handleCaseNotFound()
      return
    }
  } catch (error) {
    handleCaseNotFound()
    return
  }

  generateQAMessages(qaItems)
}

// 生成问答消息
const generateQAMessages = (qaItems: QAItem[]) => {
  isProcessing.value = true

  const allMessages: ChatMessage[] = []

  qaItems.forEach((item, index) => {
    if (item.query && item.query.trim()) {
      const userMessage: ChatMessage = {
        id: `user_${index}_${Date.now()}`,
        type: 'user',
        content: item.query.trim(),
        timestamp: new Date()
      }
      allMessages.push(userMessage)
    }

    if (item.answer && item.answer.trim()) {
      const assistantMessage: ChatMessage = {
        id: `assistant_${index}_${Date.now()}`,
        type: 'assistant',
        content: item.answer.trim(),
        timestamp: new Date(),
        isGenerating: true
      }
      allMessages.push(assistantMessage)
    }
  })

  showMessagesSequentially(allMessages)
}

// 逐条显示消息的函数
const showMessagesSequentially = (allMessages: ChatMessage[]) => {
  if (allMessages.length === 0) {
    isProcessing.value = false
    return
  }

  currentMessageIndexRef.value = 0
  allMessagesRef.value = allMessages
  messages.value = []

  showNextMessage()
}

// 显示下一条消息
const showNextMessage = () => {
  const currentIndex = currentMessageIndexRef.value
  const allMessages = allMessagesRef.value

  if (currentIndex >= allMessages.length) {
    isProcessing.value = false
    return
  }

  const nextMessage = allMessages[currentIndex]
  currentMessageIndexRef.value++

  messages.value = [...messages.value, nextMessage]

  // 使用智能滚动
  nextTick(() => {
    smartScroll()
  })

  if (nextMessage.type === 'user') {
    const timer = setTimeout(() => {
      showNextMessage()
    }, 300)
    timersRef.value.push(timer)
  } else if (nextMessage.type === 'assistant' && !isInstantMode.value) {
    // 启动打字机效果
    startTypewriter(nextMessage.id, nextMessage.content)
  }
}

// 处理查看结果按钮点击
const handleViewResults = () => {
  if (isInstantMode.value) {
    isInstantMode.value = false
    if (allMessagesRef.value.length > 0) {
      showMessagesSequentially(allMessagesRef.value)
    }
  } else {
    isInstantMode.value = true
    hasShownInstant.value = true
    clearAllTimers()
    isProcessing.value = false

    if (allMessagesRef.value.length > 0) {
      const instantMessages = allMessagesRef.value.map(msg => ({
        ...msg,
        isGenerating: false
      }))
      messages.value = instantMessages
    }
  }
}

// 处理打字机效果完成的回调
const handleTypingComplete = (messageId: string) => {
  messages.value = messages.value.map(msg =>
    msg.id === messageId
      ? { ...msg, isGenerating: false }
      : msg
  )

  const timer = setTimeout(() => {
    showNextMessage()
  }, 300)
  timersRef.value.push(timer)
}

// 监听路由参数变化
watch(() => caseId.value, (newCaseId) => {
  if (!newCaseId) {
    handleCaseNotFound()
    return
  }

  if (isRequestingRef.value) {
    return
  }

  if (hasInitialized.value && messages.value.length > 0) {
    return
  }

  clearAllTimers()
  isProcessing.value = false
  messages.value = []
  hasInitialized.value = false
  isInstantMode.value = false
  hasShownInstant.value = false

  fetchCaseData()
}, { immediate: true })



// 监听消息变化，使用智能滚动
watch(() => messages.value.length, () => {
  nextTick(() => {
    smartScroll()
  })
})

// 组件挂载时初始化智能滚动
onMounted(() => {
  if (messagesContainer.value) {
    initScrollContainer(messagesContainer.value)
  }
})

// 组件卸载时清理
onUnmounted(() => {
  clearAllTimers()
  isRequestingRef.value = false
})

// 计算SEO描述
const seoDescription = computed(() => {
  if (seoQaData.value && seoQaData.value.length > 0) {
    const firstQuestion = seoQaData.value[0]?.query || ''
    const baseDesc = t('case.description')
    return firstQuestion
      ? `${firstQuestion} - ${baseDesc} - 梅斯小智医学AI智能对话案例`
      : `${baseDesc} - 梅斯小智医学AI智能对话案例`
  }
  return `${t('case.description')} - 梅斯小智医学AI智能对话案例`
})

// 计算SEO关键词
const seoKeywords = computed(() => {
  const baseKeywords = `${t('case.caseAnalysis')},${t('case.medicalConsultation')},${t('case.intelligentQA')}`
  if (seoQaData.value && seoQaData.value.length > 0) {
    const questions = seoQaData.value.map(item => item.query).filter(Boolean)
    const questionKeywords = questions.slice(0, 1).join(', ')
    return questionKeywords ? `${questionKeywords}, ${baseKeywords}` : baseKeywords
  }
  return baseKeywords
})

// 设置页面元数据
useSeoMeta({
  title: () => `${caseTitle.value} - ${appName.value} ${t('case.intelligentDialogue')}`,
  description: () => `${caseTitle.value}`,
  keywords: () => seoKeywords.value,
  ogTitle: () => `${caseTitle.value} - ${appName.value} ${t('case.intelligentDialogue')}`,
  ogDescription: () => `${caseTitle.value}`,
  ogType: 'article',
  ogUrl: () => `https://ai.medon.com.cn/cases/${caseId.value}`,
  twitterCard: 'summary',
  twitterTitle: `${caseTitle.value} - ${appName.value} ${t('case.intelligentDialogue')}`,
  twitterDescription: () => `${caseTitle.value}`
})

// 设置页面头部
useHead({
  title: () => `${caseTitle.value} - ${appName.value} ${t('case.intelligentDialogue')}`,
  
  
})
</script>

<style scoped>
.typewriter-container {
  @apply relative;
}

.prose {
  @apply text-gray-800;
}

.prose h1,
.prose h2,
.prose h3,
.prose h4,
.prose h5,
.prose h6 {
  @apply text-gray-900 font-semibold;
}

.prose p {
  @apply mb-4 leading-relaxed;
}

.prose ul,
.prose ol {
  @apply mb-4;
}

.prose li {
  @apply mb-2;
}

.prose code {
  @apply bg-gray-100 px-1 py-0.5 rounded text-sm;
}

.prose pre {
  @apply bg-gray-100 p-4 rounded-lg overflow-x-auto;
}

.prose blockquote {
  @apply border-l-4 border-blue-500 pl-4 italic text-gray-700;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
