const SeoQaList_vue_vue_type_style_index_0_scoped_901223f0_lang = ".seo-qa-content[data-v-901223f0]{height:1px!important;left:-9999px!important;overflow:hidden!important;position:absolute!important;top:-9999px!important;width:1px!important;clip:rect(0,0,0,0)!important;border:0!important;margin:0!important;padding:0!important;white-space:nowrap!important}.qa-conversation h1[data-v-901223f0]{font-size:24px;font-weight:700;margin-bottom:20px}.qa-conversation h2[data-v-901223f0]{color:#333;font-size:18px;font-weight:700;margin:15px 0 10px}.qa-conversation h3[data-v-901223f0]{color:#666;font-size:16px;font-weight:700;margin:10px 0 8px}.qa-item[data-v-901223f0]{border-bottom:1px solid #eee;margin-bottom:30px;padding-bottom:20px}.answer[data-v-901223f0],.question[data-v-901223f0]{margin-bottom:15px}.answer div[data-v-901223f0],.question p[data-v-901223f0]{line-height:1.6;margin:8px 0}.seo-metadata[data-v-901223f0]{border-top:1px solid #eee;margin-top:30px;padding-top:20px}.seo-metadata p[data-v-901223f0]{color:#888;font-size:14px;margin:5px 0}";

const SeoQaListStyles_DJPkhFfJ = [SeoQaList_vue_vue_type_style_index_0_scoped_901223f0_lang];

export { SeoQaListStyles_DJPkhFfJ as default };
//# sourceMappingURL=SeoQaList-styles.DJPkhFfJ.mjs.map
