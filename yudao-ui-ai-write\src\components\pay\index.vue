<template>
  <div id="app">
    <div class="scale">
      <div class="micro_header">
        <div class="micro_left">
          <div class="avatar">
            <img  :src="avatar" @error="changeImg" alt="" />
            <span class="t1">{{ userInfo?.realName ? userInfo?.realName : userInfo?.userName }}</span>
          </div>

        </div>
        <div class="micro_right">
          <img
            src="https://static.medsci.cn/public-image/ms-image/78bf61f0-208d-11ee-ae3d-b3e9904fad92_关闭@2x.png"
            alt=""
            @click="close"
          />
        </div>
      </div>
      <!-- <div v-else class="micro_title"> 选择一种方案付费后，可继续查看患者报告</div> -->
      <div class="micro_main">
        <!-- :style="{
          height:
            !!choseVip && choseVip.rights && choseVip.rights.length > 0
              ? `654px`
              : `509px`
        }" -->
        <div class="micro_main_top">
          <div class="micro_main-sp">
            <div class="micro_main_temp">
              <div
                class="swiper-vip"
                v-if="currentItem.feeTypes[0].coinType == '美元'  || currentItem.feeTypes.length > 1"
                :showIndicator="false"
                :autoPlay="false"
                :style="{ transform: `translate(${translateVipVal}px)` }"
              >
                <div
                  class="swiper-vip-item"
                  v-for="(item, index) in currentItem.feeTypes"
                  :key="index"
                  :class="((subStatusDetail?.packageType=='连续包月'&&item.type=='免费')||(subStatusDetail?.packageType=='连续包月'&&item.type=='连续包年'))||((subStatusDetail?.packageType=='连续包年'&&item.type=='免费')||(subStatusDetail?.packageType=='连续包年'&&item.type=='连续包月'))?'noClick':''"
                  @click="CheckItem(item, index)"
                >
                  <div
                    class="newer"
                    :style="{
                      left: index % 4 == 0 && index != 0 ? '6px' : '-1px',
                    }"
                  ></div>
                  <div
                    class="swiper-vip-item-child"
                    :class="{ sactvie: active == index }"
                  >
                    <div class="title">{{item.type =='免费'?'免费': $t(`tool.${item.type}`) }}</div>
                    <div class="price">
                     <span> {{ item.coinType == "人民币" ? "¥" : "$"
                    }}</span> {{ item.feePrice }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="micro_main_middle" v-if="getDefaultLanguageCode()  == 'zh'">
          <div class="micro_main_middle_banner">
            <div class="micro_main_middle_title"><img src="https://img.medsci.cn/202503/36f8fcc00cb841eaaa9ff81b3b225285-Ar3LIaiI1GQo.png" />梅斯小智 订阅说明</div>
            <div class="micro_main_middle_content">
              <div>免费：每个自然月内，每个智能体的使用上限{{num}}次。次月开始重新计次。</div>
              <div>连续包月：订阅之日起一个月内，每个智能体不限使用次数。</div>
              <div>连续包年：订阅之日起一年内，每个智能体不限使用次数</div>
            </div>
          </div>
        </div>
        <div class="micro_main_middle" v-else>
          <div class="micro_main_middle_banner">
            <div class="micro_main_middle_title"><img src="https://img.medsci.cn/202503/36f8fcc00cb841eaaa9ff81b3b225285-Ar3LIaiI1GQo.png" />{{ currentItem.appName }}</div>
            <div class="micro_main_middle_content">{{ currentItem.appDescription }} </div>
          </div>
        </div>
        <div
          class="micro_main_bottom onborder"
          v-if="getDefaultLanguageCode()  == 'zh'"
        >
        <div class="result" v-if="(subStatusDetail.subStatus=='1'||subStatusDetail.subStatus=='3')&&activeItem.type==subStatusDetail.packageType ">{{ subStatusDetail.subAt }} 已订阅</div>
          <div class="result"  v-if="subStatusDetail.subStatus=='1'&&subStatusDetail.packageType=='免费'&&activeItem.type== '免费'">免费使用中…</div>
          <div class="result"  v-if="(subStatusDetail.subStatus=='3')">{{ subStatusDetail.unSubAt }} 取消订阅</div>
          <div class="result"  v-if="(subStatusDetail.subStatus=='3')">您的订阅可使用至 {{ subStatusDetail.expireAt }}</div>
          <div class="result" v-if="subStatusDetail.packageType=='连续包月'&&(subStatusDetail.subStatus=='1')">连续包月中…</div>
          <div class="result" v-if="subStatusDetail.packageType=='连续包年'&&(subStatusDetail.subStatus=='1')">连续包年中…</div>
          <div class="btns">
            <el-button
            type="primary"
            v-if="subStatusDetail.packageType=='连续包月'&&subStatusDetail.subStatus=='1'"
            @click="cancelSub()"
            >取消包月</el-button
          >
          <el-button
            type="primary"
            v-if="subStatusDetail.packageType=='连续包年'&&subStatusDetail.subStatus=='1'"
            @click="cancelSub()"
            >取消包年</el-button
          >
          </div>
          <div class="micro_pay" >
            <div class="micro_pay_right" v-if="activeItem.feePrice>0&& (activeItem.type!= '免费'&&subStatusDetail.subStatus=='0')||(activeItem.type!= '免费'&&subStatusDetail.subStatus=='2')||(subStatusDetail.packageType== '免费'&&activeItem.type!='免费')">
              <div v-loading="loading" v-show="loading" class="noQrCode"></div>
              <ClientOnly>
              <vue-qr
                v-show="!loading"
                ref="qrcode"
                class="qr-code"
                id="qrcode"
                :correctLevel="3"
                :autoColor="false"
                colorDark="#000000"
                :text="payUrl"
                :size="95"
                :margin="0"
                :logoMargin="3"
              />
            </ClientOnly>
              <div class="price">
                <div class="micro_way">
                  <div class="box">
                    <img
                      src="https://static.medsci.cn/public-image/ms-image/19eef410-5893-11ec-8e2f-1389d01aad85_zfb.png"
                      alt=""
                    />
                  </div>
                  <span>{{$t("tool.Support_Alipay_Payment")}}</span>
                </div>
                <span class="t1"
                  >{{$t("tool.Support_Alipay_Payment")}}<span class="bd">{{ activeItem?.feePrice }}</span
                  >{{activeItem?.coinType == "人民币" ? "¥" : "$"}}/{{activeItem?.monthNum==3? $t("tool.Quarter"):activeItem?.monthNum==12?$t("tool.Year"):$t("tool.Month")}}
                </span>
                <span class="t2">{{$t("tool.Meisi_Account")}}：{{ userInfo.userName }}</span>
                <span class="t3" @click="toAgreement"
                  >{{$t("tool.Please_activate_after_reading_and_agreeing_to_the_agreement")}}
                  <img
                    @click="toAgreement"
                    src="https://static.medsci.cn/public-image/ms-image/17ae6920-5be4-11ec-8e2f-1389d01aad85_right.png"
                    alt=""
                /></span>
              </div>
            </div>
          </div>
        </div>
        <div
          class="micro_main_bottom"
          v-if="activeItem?.coinType && activeItem?.coinType == '人民币' && activeItem.feePrice != 0&& getDefaultLanguageCode() != 'zh'"
        >
          <div class="micro_pay">
            <div class="micro_pay_right">
              <div v-loading="loading" v-show="loading" class="noQrCode"></div>
              <vue-qr
                v-show="!loading"
                ref="qrcode"
                class="qr-code"
                id="qrcode"
                :correctLevel="3"
                :autoColor="false"
                colorDark="#000000"
                :text="payUrl"
                :size="95"
                :margin="0"
                :logoMargin="3"
              />
              <div class="price">
                <div class="micro_way">
                  <div class="box">
                    <img
                      src="https://static.medsci.cn/public-image/ms-image/19eef410-5893-11ec-8e2f-1389d01aad85_zfb.png"
                      alt=""
                    />
                  </div>
                  <span>{{$t("tool.Support_Alipay_Payment")}}</span>
                </div>
                <span class="t1"
                  >{{$t("tool.Support_Alipay_Payment")}}<span class="bd">{{ activeItem?.feePrice }}</span
                  >{{activeItem?.coinType == "人民币" ? "¥" : "$"}}/{{activeItem?.monthNum==3? $t("tool.Quarter"):activeItem?.monthNum==12?$t("tool.Year"):$t("tool.Month")}}
                </span>
                <span class="t2">{{$t("tool.Meisi_Account")}}：{{ userInfo.userName }}</span>
                <span class="t3" @click="toAgreement"
                  >{{$t("tool.Please_activate_after_reading_and_agreeing_to_the_agreement")}}
                  <img
                    @click="toAgreement"
                    src="https://static.medsci.cn/public-image/ms-image/17ae6920-5be4-11ec-8e2f-1389d01aad85_right.png"
                    alt=""
                /></span>
              </div>
            </div>
          </div>
        </div>
        <div
          class="btns"
          v-if="activeItem?.feePrice == 0&&(subStatusDetail.subStatus=='0'||subStatusDetail.subStatus=='2')&&getDefaultLanguageCode() == 'zh'"
        >
          <el-button
            type="primary"
            @click="subscribe(activeItem, currentItem.appUuid)"
            >{{$t("tool.Free_Trial")}}</el-button
          >
        </div>
        <div
          class="btns"
          v-if="activeItem?.feePrice == 0&&getDefaultLanguageCode() != 'zh'"
        >
          <el-button
            type="primary"
            @click="subscribe(activeItem, currentItem.appUuid)"
            >{{$t("tool.Free_Trial")}}</el-button
          >
        </div>
        <div
          class="btns"
          v-if="activeItem?.coinType && activeItem?.coinType== '美元' && activeItem.feePrice > 0"
        >
          <el-button
            type="primary"
            @click="subscribe(activeItem, currentItem.appUuid)"
            >{{$t("market.subscribe")}}</el-button
          >
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ElMessage } from "element-plus";
import {  onBeforeUnmount, onMounted, ref,onUnmounted  } from "vue";
import VueQr from "vue-qr/src/packages/vue-qr.vue";
import { createSubscription, getSubOrder,freeLimit,cancelSubscription } from "@/api/dify";
import ajax from "@/utils/request";
import { showConfirmDialog } from 'vant';
import {
  getDefaultLanguageCode
} from "@/common/commonJs";
import { useI18n } from "vue-i18n";
import { useRouter } from "vue-router";
const { t } = useI18n();
const loading = ref(false);
const router = useRouter();
const isUp = location?.origin.includes('medon.com.cn')||location?.origin.includes('medsci.cn');
const isPc = ref(false)
const props = defineProps({
  userInfo: {
    type: Object,
    default: () => ({}),
  },
  currentItem: {
    type: Object,
    default: () => ({}),
  },
  subStatusDetail: {
    type: Object,
    default: () => {
      return {};
    },
  },
});


const userInfo = props.userInfo || {};
const currentItem = props.currentItem;
const activeItem = ref({});
const subStatusDetail = ref(props.subStatusDetail);
const active = ref();
const emit = defineEmits([ "close"]);
const payUrl = ref("");
const num  = ref(0);
const imgZ = ref();
const piId = ref();
const time =ref()
const avatar = ref(userInfo?.avatar?userInfo?.avatar:'https://img.medsci.cn/web/img/user_icon.png')
//头像报错给个默认头像
const changeImg = ()=>{
  avatar.value = "https://img.medsci.cn/web/img/user_icon.png"
}
// 跳转到协议
const toAgreement = () => {
  window.open("https://www.medsci.cn/about/index.do?id=27")
};
const checkScreenWidth = () => {
  const width = window.innerWidth
  if (width > 768) {
    isPc.value = true
  } else {
    isPc.value = false
  }
}
// 关闭
const close = () => {
  clearInterval(time.value)
  emit("close");
};
// 选择支付
const CheckItem = (item, index) => {
  activeItem.value = item;
  active.value = index;
   if (item.coinType == '人民币' && item.feePrice != 0) {
    subscribe(item, currentItem.appUuid);
  }
};

onBeforeUnmount(()=>{
  clearInterval(time.value)
})
// 查询支付状态
const getStatus =  (val)=>{
  time.value = setInterval(async() => {
    const res =await ajax.get(getSubOrder+'?piId='+val)
    console.log('查询支付状态',res)
    if (res.data.payStatus === 'PAID') {
        location.reload()
        clearInterval(time.value)
    }
  },2000)
}
// 取消订阅
const cancelSub =  () => { 
  showConfirmDialog({
  title: '提示',
  zIndex: 3002,
  confirmButtonColor:'#D7813F',
  message:
    `取消包月在${subStatusDetail.value.expireAt}号生效，再次使用需要重新订阅。是否确认取消？`,
})
  .then(async() => {
    console.log('取消订阅',showConfirmDialog)
    const res =  await ajax.post(cancelSubscription);
    ElMessage.success("取消成功");
    emit("close");
  })
  .catch(() => {
    ElMessage.success("已取消");
    emit("close");
    // on cancel
  });
};
// 支付
const subscribe = async (item, appUuid) => {
  if (!activeItem.value.coinType) {
    ElMessage.warning("请选择订阅服务周期");
    return;
  }
  let language = await getDefaultLanguageCode();

  if (!userInfo?.userId) {
    console.log("请先登录",userInfo);
    // country='xxxx' // 模拟国外
    // 判断是否是国内用户访问
    if (!language || language == "zh") {
      window.addLoginDom();
    } else {
      // 跳转到获取授权页
      router.push(isUp ? "/login" : "/login");
    }
  } else {
    const subscriptionParams = {
      appUuid: appUuid,
      priceId: item.priceId,
      monthNum: item.monthNum,
      packageKey:item.packageKey,
      packageType:item.type
    };
    try {
      loading.value = true;
      let res = await ajax.post(createSubscription,subscriptionParams);
      if (res.data) {
        loading.value = false;
        if (item.coinType == "人民币" && item.feePrice != 0) {
          const payInfo = res.data;
          if (
            location.origin.includes(".medsci.cn") ||
            location.origin.includes(".medon.com.cn")
          ) {
            payUrl.value =
              location.origin + "/apps/payLink/" + encodeURIComponent(payInfo);
          } else {
            payUrl.value =
              location.origin + "/payLink/" + encodeURIComponent(payInfo);
          }
          piId.value = JSON.parse(payInfo).piId
          clearInterval(time.value)
          await getStatus(piId.value);
        } else {
          ElMessage({
            type: "success",
            message: t("tool.sS"),
          });
          setTimeout(() => {
            top.location.href = res.data;
          }, 1000);
        }
      }
    } catch (error) {
      loading.value = false;
    }
  }
};
// 获取展位图
// const getImg = async () => {
//   const res = await getConfigPageImg("homePayImg");
//   imgZ.value = res.list[0].value;
// };
// 组件卸载时移除监听器
onUnmounted(() => {
  window.removeEventListener('resize', checkScreenWidth)
})
onMounted(async() => {
  const res =await ajax
    .get(freeLimit)
  num.value = res.data;
  if(subStatusDetail.value?.feeTypes?.length >0){
      subStatusDetail.value?.feeTypes.forEach((element,index) => {
      if(subStatusDetail.value.packageType == element.type){
        active.value = index
        activeItem.value = element
      }
    });
    }
  if(currentItem.appType == "写作"){
    localStorage.setItem(
      "appWrite"+'-'+currentItem.appUuid,
      JSON.stringify({
        appUuid: currentItem.appUuid,
        directoryMd: currentItem.directoryMd,
      })
    );
  }
  checkScreenWidth()
  window.addEventListener('resize', checkScreenWidth)
  if(isPc.value){
    if (currentItem.feeTypes.length == 1) {
    if (
      currentItem.feeTypes[0].feePrice > 0 &&
      currentItem.feeTypes[0].coinType == "人民币"
    ) {
      CheckItem(currentItem.feeTypes[0], 0);
    }
    if (
      currentItem.feeTypes[0].feePrice > 0 &&
      currentItem.feeTypes[0].coinType == "美元"
    ) {
      CheckItem(currentItem.feeTypes[0], 0);
    }
    if (currentItem.feeTypes[0].feePrice == 0) {
      CheckItem(currentItem.feeTypes[0], 0);
    }
  }
  }
  
});
</script>

<style lang="scss" scoped>
#app {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #ffffff;
  z-index: 99999;
  .scale {
    // transform: scale(0.8) !important;
    max-height: 96vh;
    width: 679px;
    overflow-y: auto;
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
    &::-webkit-scrollbar {
      display: none;
    }
  }
  .micro_header {
    width: 679px;
    height: 44px;
    opacity: 0.96;
    background: url(https://static.medsci.cn/public-image/ms-image/f2611180-5892-11ec-8e2f-1389d01aad85_vip-background.png)
      no-repeat;
    background-size: cover;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: 10px 10px 0px 0px;

    .micro_left {
      margin-left: 29px;
      

      .avatar {
        margin-top: 4px;
        display: flex;
        align-items: center;
        .t1{
          font-weight: 500;
          font-size: 16px;
          color: #58342C;
          letter-spacing: 0;
        }
        img {
          width: 27px;
          height: 27px;
          border-radius: 50%;
          margin-right: 10px;
        }
      }

      .info {
        display: flex;
        align-items: center;
        flex-direction: column;
        align-items: flex-start;

        .t1 {
          font-family: PingFangSC-Medium;
          font-weight: Medium;
          letter-spacing: 0;
          line-height: 44px;
          font-weight: 500;
          font-size: 16px;
          color: #58342C;
          letter-spacing: 0;
        }

        ul {
          margin-top: 4px;
          display: flex;

          li {
            margin-right: 10px;
            justify-content: space-between;

            .t2 {
              height: 20px;
              font-family: PingFangSC-Medium;
              font-weight: Medium;
              font-size: 14px;
              color: #553424;
              letter-spacing: 0;
            }
          }
        }
      }
    }

    .micro_right {
      position: relative;
      margin-right: 29px;
      display: flex;
      align-items: center;
      width: 100px;
      height: 32px;
      img {
        position: absolute;
        cursor: pointer;
        width: 16px;
        height: 16px;
        right: -15px;
        top: 8px;
      }
    }
  }
  .micro_title {
    font-weight: 400;
    font-size: 14px;
    color: #664b39;
    letter-spacing: 0;
    background: #fff;
    padding: 20px 24px 0;
    width: 830px;
  }
  .micro_type {
    padding: 0 10px;
    width: 830px;
    height: 60px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    background: #fff;
    border-bottom: 2px solid #f6f6f6;
    overflow-x: scroll;
    overflow-y: hidden;
    &::-webkit-scrollbar {
      display: none;
    }
    .micro_type_item {
      display: flex;
      flex-direction: column;
      padding: 0 10px;
      // width: 33.3333333%;
      flex: none;
      color: #58342c;
      font-weight: 600;
      text-align: center;
      justify-content: center;
      align-items: flex-start;
      cursor: pointer;
      .micro-top {
        margin-left: calc(50% - 70px);
      }

      .text {
        font-size: 16px;
        &.item-color {
          height: 30px;
          // background: url('https://static.medsci.cn/public-image/ms-image/088e1b20-21c8-11ed-a1b8-6123b3ff61ea_hy01.png') no-repeat;
          // background-size: 100% 100%;
          background-color: #ff9346;
          padding: 0 10px;
          color: #fff;
          text-align: center;
          line-height: 30px;
          border-radius: 15px;
        }
      }

      .text-icon {
        width: 15px;
        height: 15px;
        margin-right: 5px;
      }

      .text-des {
        margin-left: calc(50% - 50px);
        font-size: 15px;
      }

      // &.item-color1 {
      //   height: 60px;
      //   background: url('https://static.medsci.cn/public-image/ms-image/3dbc4020-46ac-11ed-b66b-937b834e3ef9_white2.png') no-repeat;
      //   background-size: 100% 100%;
      // }

      // &.item-color2 {
      //   height: 60px;
      //   background: url('https://static.medsci.cn/public-image/ms-image/cd1d1b10-21d9-11ed-a1b8-6123b3ff61ea_hy02.png') no-repeat;
      //   background-size: 100% 100%;
      // }
    }
  }

  .micro_main {
    margin-top: -1px;
    width: 679px;
    // height: 648px;
    background: #ffffff;
    padding: 20px 20px 10px 20px;
    position: relative;
    border-radius: 0px 0px 7px 7px;
    box-sizing: border-box;

    .micro_main_top {
      position: relative;
      margin-left: -3px;
      margin-bottom: 14px;

      .micro_main-sp {
        width: 100%;
        display: flex;
        align-items: center;

        .micro_main_temp {
          overflow-x: auto;

          .swiper-vip {
            display: flex;
            align-items: center;

            .swiper-vip-item:first-child {
              margin-left: 3px;
            }

            .swiper-vip-item {
              width: 180px !important;
              min-width: 180px !important;
              // height: 116px;
              height: 80px;
              cursor: pointer;
              background: #ffffff;
              margin-right: 6px;
              margin-left: 9px;
              position: relative;
              text-align: center;
              box-sizing: border-box;

              &.zhinan-box {
                height: 163px;
              }

              // padding-top: 10px;
              .swiper-vip-item-child {
                border: 1px solid #e0e0e0;
                height: 98%;
                border-radius: 6px;
                box-sizing: border-box;

                &.zhinan-wrap {
                  width: 180px;
                  height: 163px;
                  display: flex;
                  flex-direction: column;
                  align-items: center;
                }
              }

              .sactvie {
                background: #fcf7e6;
                border: 1px solid #ffe3cc;
              }

              .newer {
                position: absolute;
                left: -1px;
                top: 0px;

                .box {
                  position: relative;
                  z-index: 20;
                  padding: 0px 8px;
                  background-image: linear-gradient(
                    99deg,
                    #ff9d49 12%,
                    #ff6c3b 99%
                  );
                  border-radius: 8px 0 8px 0;

                  span {
                    font-family: YouSheBiaoTiHei;
                    font-size: 14px;
                    color: #ffffff;
                    letter-spacing: 0;
                    text-align: center;
                  }
                }
              }

              .title {
                width: 100%;
                padding-left: 15px;
                // max-width: 150px;
                // margin-left: 15px;
                font-family: PingFangSC-Medium;
                font-weight: 400;
                font-size: 12px;
                color: #333333;
                letter-spacing: 0;
                margin-top: 17px;
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box; //作为弹性伸缩盒子模型显示。
                -webkit-box-orient: vertical; //设置伸缩盒子的子元素排列方式--从上到下垂直排列
                -webkit-line-clamp: 1; //显示的行
                text-align: left;
              }

              .price {
                width: 100%;
                padding-left: 15px;
                font-family: NotoSansKannada-Bold;
                font-weight: Bold;
                font-size: 24px;
                color: #efa439;
                letter-spacing: 0;
                text-align: left;
                span{
                  font-size: 20px;
                }
              }

              .isfava {
                position: relative;
                height: 17px;
                font-family: PingFangSC-Regular;
                font-weight: 400;
                font-size: 12px;
                color: #999999;
                letter-spacing: 0;
                margin: 0 auto;
                text-decoration: line-through;
              }

              .isfava2 {
                position: relative;
                height: 17px;
                font-family: PingFangSC-Regular;
                font-weight: 400;
                font-size: 12px;
                color: #999999;
                letter-spacing: 0;
                margin: 0 auto;
                text-decoration: line-through;
              }

              .isfava::after {
                position: absolute;
                top: 8.5px;
                left: 42%;
                content: "";
                width: 16%;
                height: 1px;
                background: #999999;
              }

              .pre {
                // max-width: 100px;
                width: 100%;
                padding-left: 15px;
                margin: 0 auto;
                font-family: PingFangSC-Regular;
                font-weight: Regular;
                font-size: 12px;
                color: #999999;
                letter-spacing: 0;
                text-align: left;
                white-space: nowrap;
                overflow-y: hidden;
                overflow-x: scroll;
                &::-webkit-scrollbar {
                  display: none;
                }
              }
            }
          }
        }

        .micro_main_temp::-webkit-scrollbar {
          display: none;
        }

        .micro_main-arrow {
          z-index: 99;
          cursor: pointer;
          display: flex;
          justify-content: center;
          align-items: center;
          width: 20px;
          height: 106px;
          background: #f6f6f6;
          position: absolute;
          // top: 10px;
          top: 0;
          right: -4px;

          img {
            width: 8px;
            height: 14px;
          }
        }

        .micro_main-arrow2 {
          z-index: 99;
          cursor: pointer;
          display: flex;
          justify-content: center;
          align-items: center;
          min-width: 20px;
          height: 106px;
          background: #f6f6f6;
          position: absolute;
          // top: 10px;
          top: 0;
          left: 0px;

          img {
            width: 8px;
            height: 14px;
          }
        }
      }
    }

    .micro_main_content {
      font-family: PingFangSC-Regular;
      font-weight: Regular;
      font-size: 12px;
      color: #b2b2b2;
      letter-spacing: 0;
      margin-top: 20px;
      padding-left: 5px;
    }

    .vip-banner-left {
      position: absolute;
      left: 20px;
      bottom: 194px;
      width: 160px;
    }

    .vip-banner-right {
      position: absolute;
      right: 33px;
      bottom: 194px;
      width: 160px;
    }

    .micro_main_middle {
      position: relative;
      border: 1px solid #E0E0E0;
      border-radius: 4px;
      padding:9px 9px 23px 9px;
      .micro_main_middle_banner{
        width:100%;
        background:url('https://img.medsci.cn/202503/66dbd3e5d4b9430ab2c67f01a7fa3bea-EKkZ0W8E3PrU.png') no-repeat;
        background-size: 100% 88px;
        padding:0 12px  ;
        box-sizing: border-box;
     
      }
      .micro_main_middle_title{
        padding: 9px 13px;
        font-weight: 500;
        font-size: 14px;
        color: #D7813F;
        letter-spacing: 0;
        display: flex;
        align-items: center;
        img{
          width:8px;
          height: 8px;
          margin:0 2px;
        }
      }
      .micro_main_middle_content{
        background: #fff;
        width:100%;
        padding: 12px 12px;
        font-weight: 400;
        font-size: 12px;
        color: #594210;
        letter-spacing: 0;
        border-radius: 4px;
        box-sizing: border-box;
      }
    
    }
    .btns {
      text-align: center;
      margin-top: 10px;
      button {
        background-image: linear-gradient(270deg, #ffc85e 9%, #fca315 93%);
        border: none;
        font-size: 12px;
      }
    }
    .onborder{
        border:none !important; 
      }
    .micro_main_bottom {
      // padding: 26px 37px;
      padding: 20px;
      box-sizing: border-box;
      margin-left: 0;
      // margin-left: 5px;
      margin-right: 5px;
      margin-top: 10px;
      // margin-top: 25px;
      // width: 770px;
      width: 100%;
      // height: 234px;
      background: #ffffff;
      border: 1px solid #e0e0e0;
      border-radius: 6px;
      .result{
        text-align: center;
        color: #666666;
      }
      .micro_pay {
        display: flex;
        justify-content: space-between;
        align-items: center;
        // margin-bottom: 23px;
        .micro_pay_right {
          display: flex;
          align-items: center;

          .qr-code {
            width: 131px;
            object-fit: contain;
            margin-right: 19px;
          }

          #qrcode {
            width: 131px;
            object-fit: contain;
            margin-right: 19px;
          }
          .noQrCode {
            width: 131px;
            object-fit: contain;
            margin-right: 19px;
          }

          .price {
            height: 131px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;

            .t1 {
              // height: 20px;
              font-family: PingFangSC-Medium;
              font-weight: Medium;
              font-size: 12px;
              color: #333333;
              letter-spacing: 0;

              .bd {
                font-family: PingFangSC-Semibold;
                font-weight: Semibold;
                font-size: 32px;
                color: #efa439;
                letter-spacing: 0;
                margin-left: 6px;
                margin-right: 6px;
              }
            }

            .t2 {
              margin-top: 4px;
              height: 20px;
              font-family: PingFangSC-Regular;
              font-weight: Regular;
              font-weight: 400;
            font-size: 12px;
            color: #B2B2B2;
            letter-spacing: 0;
            }

            .t3 {
              cursor: pointer;
              font-family: PingFangSC-Regular;
              font-weight: Regular;
              font-weight: 400;
              font-size: 12px;
              color: #666666;
              letter-spacing: 0;
              img {
                margin-left: 8px;
                width: 6px;
                height: 10px;
              }
            }
          }
        }

        .micro_pay_left {
          border-radius: 14px;
          margin-top: 20px;
          height: 110px;
          overflow: hidden;

          .ul-scoll {
            li {
              .con {
                background: #f6f6f6;
                border-radius: 14px;
                font-family: PingFangSC-Medium;
                font-weight: Medium;
                font-size: 14px;
                color: #a5a4a4;
                letter-spacing: 0;
                margin-bottom: 14px;
                text-align: center;
                padding-top: 4px;
                padding-left: 10px;
                padding-right: 10px;
                padding-bottom: 4px;
              }
            }
          }
        }
      }

      .micro_way {
        display: flex;
        align-items: center;
        .box_next {
          width: 67px;
        }
        .box {
          height: 28px;
          background: #f5f5f5;
          border-radius: 14px;
          margin-right: 10px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding-left: 9px;
          padding-right: 9px;

          img {
            width: 17px;
            height: 17px;
            object-fit: contain;
          }
        }

        span {
          height: 18px;
          font-family: PingFangSC-Regular;
          font-weight: Regular;
          font-weight: 400;
          font-size: 12px;
          color: #666666;
          letter-spacing: 0;
        }
      }
    }
  }
}
.noClick{
    pointer-events: none;
  }
  .noClick .swiper-vip-item-child{
    background: rgb(242, 242, 242);
  } 
</style>