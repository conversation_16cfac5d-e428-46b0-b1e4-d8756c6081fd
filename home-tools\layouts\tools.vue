<template>
    <div>
      <HeaderToolNav @isZHChange="isZHChange" :subStatusDetail="subStatusDetail" @subScript="subScripts"/>
      <customerService v-if="isZH"/>
      <div class="h-full flex p-6" :style="{ height: `${clientHeight}px` }">
        <main class="flex-1 flex flex-col ">
          <div>
            <div class="text-gray-500 mb-2" v-if="nodeInfo">
              <a class="cursor-pointer hover:text-[#5298FF]" :href="hrefUrl" title="梅斯小智">{{ $t('tool.home') }}</a>
              <span class="line" v-text="'/'"></span>
              <span v-text="nodeInfo.appType ? $t(`${appTypes[nodeInfo.appType]}`) : ''"></span>
              <span class="line" v-text="'/'"></span>
              <span v-text="nodeInfo.appName"></span>
            </div>
            <div class="flex items-center my-2">
              <img class="w-[38px] h-[38px] mr-4" :src="nodeInfo?.appIcon || 'https://img.medsci.cn/web/prod/img/user_icon.png'" alt="这是icon" />
              <h3  class="text-lg font-bold text-[#666666] text-dark-200 mr-4">
                {{ nodeInfo?.appName }}
              </h3>
              <el-button plain size="small" @click="onDialog">{{ $t('tool.intro') }}</el-button>
            </div>
          </div>
          <div class="flex-1">
            <NuxtPage :current-item="nodeInfo" :subStatusDetail="subStatusDetail" ref="pageRef"/>
          </div>
        </main>
        <el-dialog v-model="visible" :show-close="false" width="500">
          <template #header>
            <div class="flex items-center my-2">
              <img class="w-[38px] h-[38px] mr-4" :src="nodeInfo?.appIcon || 'https://img.medsci.cn/web/prod/img/user_icon.png'" alt="这是icon" />
              <div class="text-xl font-bold text-dark-200">
                {{ nodeInfo?.appName }}
              </div>
            </div>
          </template>
          <div class="text-dark-500 mb-6">{{ nodeInfo?.appDescription }}</div>
          <div class="flex justify-center">
            <el-button @click="visible = false">我知道了</el-button>
          </div>
        </el-dialog>
      </div>
    </div>
</template>

<script setup>
import ModuleList from "@/common/moduleList";
import { appTypes } from "@/common/commonJs";
import { getAppByUuid,getPackageByKey } from "@/api/base";
import { useI18n } from 'vue-i18n';
import customerService from "@/components/customerService/index.vue"

const { locale } = useI18n();
const route = useRoute();
const treeList = ref([]);
const treeRef = ref(null);
const nodeInfo = ref(null);
const visible = ref(false);
const clientHeight = ref(0);
const subStatusDetail = ref(null);
const isZH = ref(false);
const defaultLanguage = process.client ? (navigator.browserLanguage || navigator.language) : 'en';
const lang = route.params?.lang || defaultLanguage;
const hrefUrl = ref('');
const pageRef = ref(null);
const { data: appListData } = await useAsyncData('getAppList', async () => {
  const header = useRequestHeaders();
  const route = useRoute();
  const event = process.server ? useRequestEvent() : null;
  let PackageByKey
  if(locale.value == "zh"){
    PackageByKey = await getPackageByKey(event);
    console.log(PackageByKey)
  }
  const res = await getAppByUuid(route.params.appUuid, event);
  if (res ) {
    return [res,PackageByKey];
  }
  return null;
}, { server: true });
nodeInfo.value = appListData.value[0];
subStatusDetail.value = appListData.value[1]
// 工具简介弹窗
const onDialog = () => {
  visible.value = true;
};
const subScripts = () => {
  pageRef.value.pageRef.subScript()
};
// 重置小助手显示
const isZHChange = (val) => {
  isZH.value = val;
};

// // 使用watchEffect替代watch以简化代码
// watchEffect(() => {
//   if (process.client && sessionStorage.getItem("nodeInfo")) {
//     try {
//       nodeInfo.value = JSON.parse(sessionStorage.getItem("nodeInfo"));
//       if (treeRef.value) {
//         treeRef.value.setCurrentKey(nodeInfo.value.name);
//       }
//     } catch (error) {
//       console.error('解析nodeInfo失败:', error);
//     }
//   }
// });



// 生命周期钩子
onMounted(async () => {
  setTimeout(() => {
    console.log("appUuid", pageRef.value);
  }, 1000);
  const ai_apps_lang =  useCookie("ai_apps_lang");
  // 设置首页链接
  hrefUrl.value = import.meta.env.VITE_MODE == 'development' 
    ? 'http://localhost:3000'+'/'+locale.value 
    : import.meta.env.VITE_MODE == 'test' 
      ? 'https://ai.medon.com.cn'+'/' +locale.value 
      : import.meta.env.VITE_MODE == 'prd' 
        ? 'https://ai.medsci.cn'+'/' +locale.value 
        : '';
  
  // 获取应用列表
  // await getAppListData();
  
  // 检查语言设置
  if (process.client && ai_apps_lang.value == "zh") {
    isZH.value = true;
  }
  

  
  // 设置客户端高度
  if (process.client) {
    clientHeight.value = document.body.clientHeight - 56;
    
    // 添加窗口大小变化监听
    window.addEventListener('resize', () => {
      clientHeight.value = document.body.clientHeight - 56;
    });
  }
});

// 在组件卸载时移除事件监听
onUnmounted(() => {
  if (process.client) {
    window.removeEventListener('resize', () => {
      clientHeight.value = document.body.clientHeight - 56;
    });
  }
});
</script>

<style lang="scss" scoped>
:deep(.header){
  padding: unset;
}
.nav {
  border-right: 1px solid #d2d2d2;
}
.line{
  line-height: 1;
  margin: 0 3px;
}
.content-te {
  max-width: 1378px;
  margin: 0 auto;
}

:deep(.el-tree-node__content) {
  height: 48px;
  color: #000;

  .el-tree-node__expand-icon {
    font-size: 18px;
    color: #000;
  }
}

:deep(.el-tree--highlight-current) {
  .el-tree-node.is-current>.el-tree-node__content>.node-item {
    color: #2f92ee;
    font-weight: bold;
  }
}
@media screen and (max-width: 768px) {
    :deep(){
        .p-6{
        padding:  1.5rem 0.75rem;
    }
    .el-dialog{
      width:80%;
    }
    .p-2{
      padding-top:0;
    }
    .p-3{
            padding: 0 0 0.75rem;
        }
    }}
</style>