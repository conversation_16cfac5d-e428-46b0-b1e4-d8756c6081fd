import { ref, withAsyncContext, unref, useSSRContext } from 'vue';
import { ssrRenderAttrs, ssrRenderAttr, ssrInterpolate, ssrRenderStyle } from 'vue/server-renderer';
import { _ as _imports_0, a as _imports_1 } from './qrcode.png.mjs';
import { h as home, a as appuuid } from './lang.mjs';
import { h as getAppByUuid, i as getParameters } from './requestDify.mjs';
import { e as useI18n, g as useCookie, f as useRoute, k as useRequestHeaders, i as useRequestEvent } from './server.mjs';
import { u as useAsyncData } from './asyncData.mjs';
import { u as useHead } from './v3.mjs';
import { _ as _export_sfc } from './_plugin-vue_export-helper.mjs';
import 'axios';
import 'js-cookie';
import '../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'node:fs';
import 'node:url';
import 'consola';
import 'node:path';
import 'node:crypto';
import 'vue-router';
import 'element-plus';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import 'unhead/server';
import 'unhead/plugins';
import 'unhead/utils';
import 'devalue';

const _sfc_main = {
  __name: "[appUuid]",
  __ssrInlineRender: true,
  async setup(__props) {
    var _a, _b, _c, _d, _e, _f;
    let __temp, __restore;
    const { t, locale } = useI18n();
    const url = ref("");
    const appLang = ref({});
    const title = ref("");
    const ai_apps_lang = useCookie("ai_apps_lang", { domain: ".medon.com.cn", maxAge: 30 * 24 * 60 * 60 * 12 });
    const route = useRoute();
    const { data: parameters } = ([__temp, __restore] = withAsyncContext(async () => useAsyncData(
      "parameters",
      async () => {
        let res;
        let appLang2;
        try {
          url.value = "https://ai.medon.com.cn/ai-chat/" + route.params.appUuid;
          const userInfo = useCookie("userInfo");
          const header = useRequestHeaders();
          header["accept-language"] = header["accept-language"] ? header["accept-language"] : "zh-CN,zh;q=0.9,en;q=0.8";
          ai_apps_lang.value = locale.value;
          const event = useRequestEvent();
          appLang2 = await getAppByUuid(route.params.appUuid, event);
          title.value = `${route.query.appName == "梅斯简问" ? route.query.query : appLang2 == null ? void 0 : appLang2.appName}-${appuuid[ai_apps_lang.value]}`;
          res = await getParameters(
            {
              appId: appLang2.dAppUuid,
              user: userInfo == null ? void 0 : userInfo.userName
            },
            event
          );
        } catch (error) {
        }
        return [res, appLang2];
      },
      { server: true }
    )), __temp = await __temp, __restore(), __temp);
    appLang.value = parameters.value[1];
    useHead({
      title: title.value,
      meta: [
        {
          name: "keywords",
          content: `${home["chatKeywords"][ai_apps_lang.value]},${(_a = appLang.value) == null ? void 0 : _a.appName}`
        },
        {
          name: "description",
          content: ((_b = appLang.value) == null ? void 0 : _b.appDescription) + "-" + (route.query.appName == "梅斯简问" ? route.query.query : "")
        },
        { property: "og:type", content: "website" },
        { property: "og:title", content: title.value },
        { property: "og:description", content: ((_c = appLang.value) == null ? void 0 : _c.appDescription) + "-" + (route.query.appName == "梅斯简问" ? route.query.query : "") },
        { property: "og:image", content: (_d = appLang.value) == null ? void 0 : _d.appIcon },
        { name: "twitter:card", content: "summary_large_image" },
        // 注意：根据常见用法推断 content 为 'summary_large_image'
        { name: "twitter:title", content: title.value },
        { name: "twitter:description", content: ((_e = appLang.value) == null ? void 0 : _e.appDescription) + "-" + (route.query.appName == "梅斯简问" ? route.query.query : "") },
        { name: "twitter:image", content: (_f = appLang.value) == null ? void 0 : _f.appIcon }
      ]
    });
    return (_ctx, _push, _parent, _attrs) => {
      var _a2, _b2, _c2;
      _push(`<div${ssrRenderAttrs(_attrs)} data-v-efd336b0><iframe${ssrRenderAttr("src", unref(url))} data-v-efd336b0></iframe><div id="root" data-v-efd336b0><div class="w-full h-screen acss-2nuys4 flex flex-col overflow-hidden bg-[#eff0f5]" data-v-efd336b0><div class="assistant-container" data-v-efd336b0><div class="assistant-icon" data-v-efd336b0><img${ssrRenderAttr("src", _imports_0)} class="fas fa-user-astronaut" alt="客服" data-v-efd336b0></div><div class="qr-codes" data-v-efd336b0><img${ssrRenderAttr("src", _imports_1)} alt="QR Code" data-v-efd336b0>扫码添加小助手 </div></div><div class="hidden md:!flex items-center justify-between px-6" data-v-efd336b0><div class="flex-1 overflow-hidden" data-v-efd336b0><div class="flex h-16 items-center justify-start py-0 box-border" data-v-efd336b0><div class="h-full flex items-center flex-1 overflow-hidden" data-v-efd336b0><a href="https://ai.medsci.cn" data-v-efd336b0><img class="h-6 inline-block hover:cursor-pointer" src="https://img.medsci.cn/202412/8a5663cb16a84c45b9c8c5db4eda0075-6pyGtTcj0asd.png" draggable="false" alt="logo" data-v-efd336b0></a></div></div></div><div class="flex h-full items-center flex-[3] overflow-hidden justify-center text-primary font-semibold" data-v-efd336b0><div class="flex items-center rounded-3xl shadow-md py-2 px-4 text-sm bg-white" data-v-efd336b0>${ssrInterpolate((_a2 = unref(parameters)[1]) == null ? void 0 : _a2.appName)}</div></div><div class="flex-1 overflow-hidden" data-v-efd336b0><div class="flex items-center justify-end text-sm" data-v-efd336b0><div class="ant-space css-1gj8yzb ant-space-horizontal ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small" data-v-efd336b0><div class="ant-space-item" data-v-efd336b0><a data-v-4517b9f6="" class="backImg" href="https://ai.medsci.cn" style="${ssrRenderStyle({ "border-radius": "4px", "background": "rgb(241, 245, 249)", "padding": "6px 10px", "font-size": "12px", "color": "rgb(102, 102, 102)" })}" data-v-efd336b0>返回首页</a></div><span class="ant-space-item-split" data-v-efd336b0><div class="ant-divider css-1gj8yzb ant-divider-vertical" role="separator" data-v-efd336b0></div></span><div class="ant-space-item" data-v-efd336b0><div class="hover:cursor-pointer" data-v-efd336b0>登录</div></div><span class="ant-space-item-split" data-v-efd336b0><div class="ant-divider css-1gj8yzb ant-divider-vertical" role="separator" data-v-efd336b0></div></span><div class="ant-space-item" data-v-efd336b0><a href="#" aria-describedby=":r1:" data-v-efd336b0><div class="img-area" data-v-efd336b0></div></a></div></div></div></div></div><div class="flex-1 overflow-hidden flex rounded-3xl bg-white" data-v-efd336b0><div class="acss-puryd4 hidden md:!flex w-72 h-full flex-col" data-v-efd336b0><button type="button" class="ant-btn css-1gj8yzb ant-btn-default ant-btn-color-default ant-btn-variant-outlined h-10 leading-10 border border-solid border-gray-200 w-[calc(100%-24px)] mt-3 mx-3 text-default" data-v-efd336b0><span class="ant-btn-icon" data-v-efd336b0><span role="img" aria-label="plus" class="anticon anticon-plus" data-v-efd336b0><svg viewBox="64 64 896 896" focusable="false" data-icon="plus" width="1em" height="1em" fill="currentColor" aria-hidden="true" data-v-efd336b0><path d="M482 152h60q8 0 8 8v704q0 8-8 8h-60q-8 0-8-8V160q0-8 8-8z" data-v-efd336b0></path><path d="M192 474h672q8 0 8 8v60q0 8-8 8H160q-8 0-8-8v-60q0-8 8-8z" data-v-efd336b0></path></svg></span></span><span data-v-efd336b0>新增对话</span></button><div class="px-3" data-v-efd336b0><div class="ant-spin-nested-loading css-1gj8yzb" data-v-efd336b0><div class="ant-spin-container" data-v-efd336b0><ul class="ant-conversations p-0 css-10lvmvq" data-v-efd336b0><li class="ant-conversations-item ant-conversations-item-active" aria-describedby=":r3:" data-v-efd336b0><span class="ant-typography ant-typography-ellipsis ant-conversations-label css-1gj8yzb" aria-label="新对话" data-v-efd336b0>新对话</span><span role="img" aria-label="ellipsis" tabindex="-1" class="anticon anticon-ellipsis ant-dropdown-trigger ant-conversations-menu-icon" data-v-efd336b0><svg viewBox="64 64 896 896" focusable="false" data-icon="ellipsis" width="1em" height="1em" fill="currentColor" aria-hidden="true" data-v-efd336b0><path d="M176 511a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0z" data-v-efd336b0></path></svg></span></li></ul></div></div></div></div><div class="flex-1 min-w-0 flex flex-col overflow-hidden" data-v-efd336b0><div class="flex h-screen flex-col overflow-hidden flex-1" data-v-efd336b0><div class="flex-1 overflow-hidden relative" data-v-efd336b0><div class="w-full h-full flex items-center justify-center -mt-5" data-v-efd336b0><div class="max-w-[80vw] w-3/5 px-10 rounded-3xl bg-gray-100 box-border" data-v-efd336b0><div class="text-default" data-v-efd336b0><div class="flex items-center justify-center flex-col" data-v-efd336b0><span role="img" aria-label="robot" class="anticon anticon-robot text-2xl text-primary" data-v-efd336b0><svg viewBox="64 64 896 896" focusable="false" data-icon="robot" width="1em" height="1em" fill="currentColor" aria-hidden="true" data-v-efd336b0><path d="M300 328a60 60 0 10120 0 60 60 0 10-120 0zM852 64H172c-17.7 0-32 14.3-32 32v660c0 17.7 14.3 32 32 32h680c17.7 0 32-14.3 32-32V96c0-17.7-14.3-32-32-32zm-32 660H204V128h616v596zM604 328a60 60 0 10120 0 60 60 0 10-120 0zm250.2 556H169.8c-16.5 0-29.8 14.3-29.8 32v36c0 4.4 3.3 8 7.4 8h729.1c4.1 0 7.4-3.6 7.4-8v-36c.1-17.7-13.2-32-29.7-32zM664 508H360c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h304c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z" data-v-efd336b0></path></svg></span><div class="text-2xl font-bold mt-3" data-v-efd336b0>${ssrInterpolate((_b2 = unref(parameters)[1]) == null ? void 0 : _b2.appName)}</div><div class="text-desc text-base max-w-96 mt-3 text-center" data-v-efd336b0>${ssrInterpolate((_c2 = unref(parameters)[1]) == null ? void 0 : _c2.appDescription)}</div><div class="mt-3 text-center" data-v-efd336b0></div></div></div><form class="ant-form ant-form-horizontal css-1gj8yzb mt-6" data-v-efd336b0><div class="ant-form-item css-1gj8yzb" data-v-efd336b0><div class="ant-row ant-form-item-row css-1gj8yzb" data-v-efd336b0><div class="ant-col ant-col-5 ant-form-item-label css-1gj8yzb" data-v-efd336b0><label for="outputLanguage" class="" title="outputLanguage" data-v-efd336b0>outputLanguage</label></div><div class="ant-col ant-form-item-control css-1gj8yzb" data-v-efd336b0><div class="ant-form-item-control-input" data-v-efd336b0><div class="ant-form-item-control-input-content" data-v-efd336b0><input placeholder="请输入" id="outputLanguage" class="ant-input css-1gj8yzb ant-input-outlined" type="text" value="" data-v-efd336b0></div></div></div></div></div></form><div class="mt-3 w-full flex justify-center" data-v-efd336b0><button type="button" class="ant-btn css-1gj8yzb ant-btn-primary ant-btn-color-primary ant-btn-variant-solid" data-v-efd336b0><span class="ant-btn-icon" data-v-efd336b0><span role="img" aria-label="message" class="anticon anticon-message" data-v-efd336b0><svg viewBox="64 64 896 896" focusable="false" data-icon="message" width="1em" height="1em" fill="currentColor" aria-hidden="true" data-v-efd336b0><path d="M464 512a48 48 0 1096 0 48 48 0 10-96 0zm200 0a48 48 0 1096 0 48 48 0 10-96 0zm-400 0a48 48 0 1096 0 48 48 0 10-96 0zm661.2-173.6c-22.6-53.7-55-101.9-96.3-143.3a444.35 444.35 0 00-143.3-96.3C630.6 75.7 572.2 64 512 64h-2c-60.6.3-119.3 12.3-174.5 35.9a445.35 445.35 0 00-142 96.5c-40.9 41.3-73 89.3-95.2 142.8-23 55.4-34.6 114.3-34.3 174.9A449.4 449.4 0 00112 714v152a46 46 0 0046 46h152.1A449.4 449.4 0 00510 960h2.1c59.9 0 118-11.6 172.7-34.3a444.48 444.48 0 00142.8-95.2c41.3-40.9 73.8-88.7 96.5-142 23.6-55.2 35.6-113.9 35.9-174.5.3-60.9-11.5-120-34.8-175.6zm-151.1 438C704 845.8 611 884 512 884h-1.7c-60.3-.3-120.2-15.3-173.1-43.5l-8.4-4.5H188V695.2l-4.5-8.4C155.3 633.9 140.3 574 140 513.7c-.4-99.7 37.7-193.3 107.6-263.8 69.8-70.5 163.1-109.5 262.8-109.9h1.7c50 0 98.5 9.7 144.2 28.9 44.6 18.7 84.6 45.6 119 80 34.3 34.3 61.3 74.4 80 119 19.4 46.2 29.1 95.2 28.9 145.8-.6 99.6-39.7 192.9-110.1 262.7z" data-v-efd336b0></path></svg></span></span><span data-v-efd336b0>开始对话</span></button></div></div></div></div></div></div></div></div></div></div>`);
    };
  }
};
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/chat/[appUuid].vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const _appUuid_ = /* @__PURE__ */ _export_sfc(_sfc_main, [["__scopeId", "data-v-efd336b0"]]);

export { _appUuid_ as default };
//# sourceMappingURL=_appUuid_.vue.mjs.map
