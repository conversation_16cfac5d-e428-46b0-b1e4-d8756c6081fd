const interopDefault = r => r.default || r || [];
const styles = {
  "node_modules/nuxt/dist/app/entry.js": () => import('./entry-styles.B7wB9zFz.mjs').then(interopDefault),
  "app.vue": () => import('./app-styles.zn-_uojD.mjs').then(interopDefault),
  "pages/index.vue": () => import('./index-styles.CltK9xGF.mjs').then(interopDefault),
  "pages/sign-up.vue": () => import('./sign-up-styles.BJOF85MO.mjs').then(interopDefault),
  "pages/login/index.vue": () => import('./index-styles.BA9kbtpA.mjs').then(interopDefault),
  "pages/article/[id].vue": () => import('./_id_-styles.D4CavV-G.mjs').then(interopDefault),
  "pages/tool/destroy.vue": () => import('./destroy-styles.CE4ATQhm.mjs').then(interopDefault),
  "pages/chat/[appUuid].vue": () => import('./_appUuid_-styles.CzdrXPm3.mjs').then(interopDefault),
  "pages/tool/[appUuid].vue": () => import('./_appUuid_-styles.2ErCBi2A.mjs').then(interopDefault),
  "pages/write/[appUuid].vue": () => import('./_appUuid_-styles.D8vr_AVP.mjs').then(interopDefault),
  "pages/payLink/[payInfo].vue": () => import('./_payInfo_-styles.CGvK44UY.mjs').then(interopDefault),
  "pages/login/[socialType].vue": () => import('./_socialType_-styles.BP9Wfho8.mjs').then(interopDefault),
  "pages/cases/[caseId].vue": () => import('./_caseId_-styles.Dl6uzNjM.mjs').then(interopDefault),
  "node_modules/nuxt/dist/app/components/error-404.vue": () => import('./error-404-styles.BNmKahfp.mjs').then(interopDefault),
  "node_modules/nuxt/dist/app/components/error-500.vue": () => import('./error-500-styles.Cx3AJg0u.mjs').then(interopDefault),
  "app.vue?vue&type=style&index=0&lang.css": () => import('./app-styles.mkR0zTjK.mjs').then(interopDefault),
  "components/headerNav/index.vue": () => import('./index-styles.HebZcV_w.mjs').then(interopDefault),
  "components/footerNavZH/index.vue": () => import('./index-styles.DlI-xQEm.mjs').then(interopDefault),
  "components/footerNav/index.vue": () => import('./index-styles.PX8mZBxm.mjs').then(interopDefault),
  "components/headerNav/index.vue?vue&type=style&index=1&lang.css": () => import('./index-styles.BIl5SkX_.mjs').then(interopDefault),
  "components/customerService/index.vue": () => import('./index-styles.D-5hPTap.mjs').then(interopDefault),
  "components/pay/index.vue": () => import('./index-styles.qd7tA904.mjs').then(interopDefault),
  "components/payMobile/index.vue": () => import('./index-styles.D0PqvyUA.mjs').then(interopDefault),
  "layouts/tools.vue": () => import('./tools-styles.BRTSAvOF.mjs').then(interopDefault),
  "layouts/default.vue": () => import('./default-styles.ieL6qKRG.mjs').then(interopDefault),
  "layouts/default.vue?vue&type=style&index=0&lang.css": () => import('./default-styles.CVsBy2Vl.mjs').then(interopDefault),
  "components/headerToolNav/index.vue": () => import('./index-styles.D9O35YAw.mjs').then(interopDefault),
  "components/headerToolNav/index.vue?vue&type=style&index=1&lang.css": () => import('./index-styles.DQzh3vrv.mjs').then(interopDefault),
  "components/SeoQaList.vue": () => import('./SeoQaList-styles.DJPkhFfJ.mjs').then(interopDefault),
  "components/AiResponseRenderer/index.vue": () => import('./index-styles.Dn5wZ0Ze.mjs').then(interopDefault)
};

export { styles as default };
//# sourceMappingURL=styles.mjs.map
