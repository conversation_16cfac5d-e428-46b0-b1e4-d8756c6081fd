const client_manifest = {
  "_C0D7e8xQ.js": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "C0D7e8xQ.js",
    "name": "index",
    "imports": [
      "_DLNSr_r7.js",
      "_DlAUqK2U.js",
      "node_modules/nuxt/dist/app/entry.js"
    ],
    "css": [
      "index.CNboY1JO.css"
    ]
  },
  "index.CNboY1JO.css": {
    "file": "index.CNboY1JO.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "_CtB2OUJd.js": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "CtB2OUJd.js",
    "name": "ssr",
    "imports": [
      "node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "_D9cfu-kP.js": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "D9cfu-kP.js",
    "name": "index",
    "imports": [
      "_Dz3KiqLz.js",
      "node_modules/nuxt/dist/app/entry.js",
      "_DlAUqK2U.js",
      "_x_rD_Ya3.js"
    ],
    "css": [
      "index.5fpZC7iX.css"
    ]
  },
  "index.5fpZC7iX.css": {
    "file": "index.5fpZC7iX.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "_DLNSr_r7.js": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "DLNSr_r7.js",
    "name": "qrcode",
    "imports": [
      "node_modules/nuxt/dist/app/entry.js"
    ],
    "assets": [
      "kefu.B84d3bIM.png",
      "qrcode.DKObs6E2.png"
    ]
  },
  "kefu.B84d3bIM.png": {
    "file": "kefu.B84d3bIM.png",
    "resourceType": "image",
    "prefetch": true,
    "mimeType": "image/png"
  },
  "qrcode.DKObs6E2.png": {
    "file": "qrcode.DKObs6E2.png",
    "resourceType": "image",
    "prefetch": true,
    "mimeType": "image/png"
  },
  "_DU85YH7X.js": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "DU85YH7X.js",
    "name": "lang"
  },
  "_DlAUqK2U.js": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "DlAUqK2U.js",
    "name": "_plugin-vue_export-helper"
  },
  "_Dvr7li8E.js": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "Dvr7li8E.js",
    "name": "v3",
    "imports": [
      "node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "_Dz3KiqLz.js": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "Dz3KiqLz.js",
    "name": "client-only",
    "imports": [
      "node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "_index.5fpZC7iX.css": {
    "resourceType": "style",
    "prefetch": true,
    "preload": true,
    "file": "index.5fpZC7iX.css",
    "src": "_index.5fpZC7iX.css"
  },
  "_index.CNboY1JO.css": {
    "resourceType": "style",
    "prefetch": true,
    "preload": true,
    "file": "index.CNboY1JO.css",
    "src": "_index.CNboY1JO.css"
  },
  "_x_rD_Ya3.js": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "x_rD_Ya3.js",
    "name": "interval"
  },
  "assets/imgs/Bitmap.png": {
    "resourceType": "image",
    "prefetch": true,
    "mimeType": "image/png",
    "file": "Bitmap.BV-s4dwZ.png",
    "src": "assets/imgs/Bitmap.png"
  },
  "assets/imgs/kefu.png": {
    "resourceType": "image",
    "prefetch": true,
    "mimeType": "image/png",
    "file": "kefu.B84d3bIM.png",
    "src": "assets/imgs/kefu.png"
  },
  "assets/imgs/qrcode.png": {
    "resourceType": "image",
    "prefetch": true,
    "mimeType": "image/png",
    "file": "qrcode.DKObs6E2.png",
    "src": "assets/imgs/qrcode.png"
  },
  "assets/imgs/right.png": {
    "resourceType": "image",
    "prefetch": true,
    "mimeType": "image/png",
    "file": "right.C7PU2hTH.png",
    "src": "assets/imgs/right.png"
  },
  "i18n.config.js": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "B-jIqtY2.js",
    "name": "i18n.config",
    "src": "i18n.config.js",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "layouts/default.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "BgNWQPTN.js",
    "name": "default",
    "src": "layouts/default.vue",
    "isDynamicEntry": true,
    "imports": [
      "_DlAUqK2U.js",
      "node_modules/nuxt/dist/app/entry.js"
    ],
    "css": []
  },
  "default.q2gX9DjU.css": {
    "file": "default.q2gX9DjU.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "layouts/tools.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "DFOSE8SI.js",
    "name": "tools",
    "src": "layouts/tools.vue",
    "isDynamicEntry": true,
    "imports": [
      "_Dz3KiqLz.js",
      "node_modules/nuxt/dist/app/entry.js",
      "_CtB2OUJd.js",
      "_x_rD_Ya3.js",
      "_DlAUqK2U.js",
      "_C0D7e8xQ.js",
      "_DLNSr_r7.js"
    ],
    "css": []
  },
  "tools.-D2PhBxO.css": {
    "file": "tools.-D2PhBxO.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "node_modules/highlight.js/es/index.js": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "CBZdzrfx.js",
    "name": "index",
    "src": "node_modules/highlight.js/es/index.js",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "node_modules/highlight.js/styles/vs2015.css": {
    "resourceType": "style",
    "prefetch": true,
    "preload": true,
    "file": "vs2015.LLWOf3w5.css",
    "src": "node_modules/highlight.js/styles/vs2015.css"
  },
  "node_modules/marked-highlight/src/index.js": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "C6O5mKp1.js",
    "name": "index",
    "src": "node_modules/marked-highlight/src/index.js",
    "isDynamicEntry": true
  },
  "node_modules/marked/lib/marked.esm.js": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "BlqX5hTP.js",
    "name": "marked.esm",
    "src": "node_modules/marked/lib/marked.esm.js",
    "isDynamicEntry": true
  },
  "node_modules/nuxt/dist/app/components/error-404.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "jKDHtdgT.js",
    "name": "error-404",
    "src": "node_modules/nuxt/dist/app/components/error-404.vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js",
      "_DlAUqK2U.js",
      "_Dvr7li8E.js"
    ],
    "css": []
  },
  "error-404.aNCZ2L4y.css": {
    "file": "error-404.aNCZ2L4y.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "node_modules/nuxt/dist/app/components/error-500.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "CCxS49y8.js",
    "name": "error-500",
    "src": "node_modules/nuxt/dist/app/components/error-500.vue",
    "isDynamicEntry": true,
    "imports": [
      "_DlAUqK2U.js",
      "_Dvr7li8E.js",
      "node_modules/nuxt/dist/app/entry.js"
    ],
    "css": []
  },
  "error-500.JESWioAZ.css": {
    "file": "error-500.JESWioAZ.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "node_modules/nuxt/dist/app/entry.js": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "Bet9VQd3.js",
    "name": "entry",
    "src": "node_modules/nuxt/dist/app/entry.js",
    "isEntry": true,
    "isDynamicEntry": true,
    "dynamicImports": [
      "i18n.config.js",
      "layouts/default.vue",
      "layouts/tools.vue",
      "node_modules/nuxt/dist/app/components/error-404.vue",
      "node_modules/nuxt/dist/app/components/error-500.vue"
    ],
    "css": [
      "entry.BM64DQIl.css"
    ],
    "_globalCSS": true
  },
  "entry.BM64DQIl.css": {
    "file": "entry.BM64DQIl.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "pages/article/[id].vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "w-gtoCeF.js",
    "name": "_id_",
    "src": "pages/article/[id].vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js",
      "_CtB2OUJd.js",
      "_Dvr7li8E.js",
      "_DlAUqK2U.js"
    ],
    "dynamicImports": [
      "node_modules/nuxt/dist/app/entry.js"
    ],
    "css": []
  },
  "_id_.DmbhpAJy.css": {
    "file": "_id_.DmbhpAJy.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "pages/cases/[caseId].vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "h5ymdQRE.js",
    "name": "_caseId_",
    "src": "pages/cases/[caseId].vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js",
      "_DlAUqK2U.js",
      "_Dvr7li8E.js",
      "_CtB2OUJd.js"
    ],
    "dynamicImports": [
      "node_modules/marked/lib/marked.esm.js",
      "node_modules/marked-highlight/src/index.js",
      "node_modules/highlight.js/es/index.js"
    ],
    "css": []
  },
  "_caseId_.DTsjJGTU.css": {
    "file": "_caseId_.DTsjJGTU.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "pages/chat/[appUuid].vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "DmfmnG82.js",
    "name": "_appUuid_",
    "src": "pages/chat/[appUuid].vue",
    "isDynamicEntry": true,
    "imports": [
      "_DLNSr_r7.js",
      "_DU85YH7X.js",
      "node_modules/nuxt/dist/app/entry.js",
      "_CtB2OUJd.js",
      "_Dvr7li8E.js",
      "_DlAUqK2U.js"
    ],
    "css": []
  },
  "_appUuid_.BGANLTBm.css": {
    "file": "_appUuid_.BGANLTBm.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "pages/index.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "DhqZOveb.js",
    "name": "index",
    "src": "pages/index.vue",
    "isDynamicEntry": true,
    "imports": [
      "_Dz3KiqLz.js",
      "node_modules/nuxt/dist/app/entry.js",
      "_CtB2OUJd.js",
      "_x_rD_Ya3.js",
      "_DlAUqK2U.js",
      "_C0D7e8xQ.js",
      "_D9cfu-kP.js",
      "_DU85YH7X.js",
      "_Dvr7li8E.js",
      "_DLNSr_r7.js"
    ],
    "css": [],
    "assets": [
      "Bitmap.BV-s4dwZ.png"
    ]
  },
  "index.AzsQSJOf.css": {
    "file": "index.AzsQSJOf.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "Bitmap.BV-s4dwZ.png": {
    "file": "Bitmap.BV-s4dwZ.png",
    "resourceType": "image",
    "prefetch": true,
    "mimeType": "image/png"
  },
  "pages/login/[socialType].vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "J2P25NI4.js",
    "name": "_socialType_",
    "src": "pages/login/[socialType].vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js",
      "_DlAUqK2U.js"
    ],
    "css": []
  },
  "_socialType_.DYGiZ1nj.css": {
    "file": "_socialType_.DYGiZ1nj.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "pages/login/index.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "B29Uc35I.js",
    "name": "index",
    "src": "pages/login/index.vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js",
      "_DlAUqK2U.js"
    ],
    "css": []
  },
  "index.D0WfnBpn.css": {
    "file": "index.D0WfnBpn.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "pages/payLink/[payInfo].vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "HpyiH0_F.js",
    "name": "_payInfo_",
    "src": "pages/payLink/[payInfo].vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js",
      "_x_rD_Ya3.js",
      "_DlAUqK2U.js"
    ],
    "css": []
  },
  "_payInfo_.BM6XeFv8.css": {
    "file": "_payInfo_.BM6XeFv8.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "pages/sign-up.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "GyPc9HVR.js",
    "name": "sign-up",
    "src": "pages/sign-up.vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js",
      "_x_rD_Ya3.js",
      "_DlAUqK2U.js"
    ],
    "css": []
  },
  "sign-up.Cj3pzlL2.css": {
    "file": "sign-up.Cj3pzlL2.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "pages/tool/[appUuid].vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "BOHFc5p4.js",
    "name": "_appUuid_",
    "src": "pages/tool/[appUuid].vue",
    "isDynamicEntry": true,
    "imports": [
      "_Dz3KiqLz.js",
      "node_modules/nuxt/dist/app/entry.js",
      "_D9cfu-kP.js",
      "pages/tool/components/InputField.vue",
      "_DU85YH7X.js",
      "_CtB2OUJd.js",
      "_Dvr7li8E.js",
      "_x_rD_Ya3.js",
      "_DlAUqK2U.js"
    ],
    "css": []
  },
  "_appUuid_.BY5k7pyp.css": {
    "file": "_appUuid_.BY5k7pyp.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "pages/tool/components/InputField.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "C-dFdJIC.js",
    "name": "InputField",
    "src": "pages/tool/components/InputField.vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "pages/tool/destroy.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "Cw6V90hh.js",
    "name": "destroy",
    "src": "pages/tool/destroy.vue",
    "isDynamicEntry": true,
    "imports": [
      "_DlAUqK2U.js",
      "node_modules/nuxt/dist/app/entry.js"
    ],
    "css": []
  },
  "destroy.BvPgb2nQ.css": {
    "file": "destroy.BvPgb2nQ.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "pages/tool/privacy-policy.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "DE___NL7.js",
    "name": "privacy-policy",
    "src": "pages/tool/privacy-policy.vue",
    "isDynamicEntry": true,
    "imports": [
      "_DlAUqK2U.js",
      "node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "pages/write/[appUuid].vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "CfV8QpCL.js",
    "name": "_appUuid_",
    "src": "pages/write/[appUuid].vue",
    "isDynamicEntry": true,
    "imports": [
      "_DLNSr_r7.js",
      "node_modules/nuxt/dist/app/entry.js",
      "_DU85YH7X.js",
      "_CtB2OUJd.js",
      "_Dvr7li8E.js",
      "_DlAUqK2U.js"
    ],
    "css": [],
    "assets": [
      "right.C7PU2hTH.png"
    ]
  },
  "_appUuid_.CUY9OiQN.css": {
    "file": "_appUuid_.CUY9OiQN.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "right.C7PU2hTH.png": {
    "file": "right.C7PU2hTH.png",
    "resourceType": "image",
    "prefetch": true,
    "mimeType": "image/png"
  }
};

export { client_manifest as default };
//# sourceMappingURL=client.manifest.mjs.map
