<script setup lang="ts">
import cookie from "js-cookie";
import { useHead } from "@vueuse/head";
import customerService from "@/components/customerService/index.vue";
import DialogueRecord from "./components/DialogueRecord.vue";
import Editor from "@/components/MsEditor/index.vue";
import { useI18n } from "vue-i18n";
import { Plus } from "@element-plus/icons-vue";
import { computed, onBeforeUnmount, onMounted, ref } from "vue";
import { useLoginStore } from "@/stores/login";
import { useRoute } from "vue-router";
import titles from "@/langs/lang.js";
import axios from "axios";
import { injectCustomCode } from "@/utils/index.js";
// 国际化
import createI18nInstance from "@/utils/i18n.js";
const { t, locale } = useI18n();
import { Cookies } from "@/utils/cookieHandler";

import { getDefaultLanguageCode } from "@/common/commonJs";
import ajax from "@/utils/request";
import { getLocation, getAppByUuid, getHToken,getPackageByKey } from "@/api/dify";
import { ElMessage, ElMessageBox } from "element-plus";
const loginStore = useLoginStore();
const route = useRoute();
const isZH = ref(false);
const hrefUrl = ref();
const dAppUuid = ref("");
const appUuid = ref("");
const currentItem: any = ref();
const subStatusDetail = ref();
const handleLogin = async () => {
  await loginStore.loginMain();
};
const userinfo =  Cookies.get("userInfo")
let userInfo = ref(JSON.parse(userinfo));
const subScript = () => {
  pageRef.value.subScript();
};
let title = ref("梅斯小智");
// 需要先导入 useHead

// 打点
const insert = async () => {
  let data = [
    {
      userRandomId:
        Math.random().toString(36).substr(2, 9) + Date.now().toString(36),
      title: document.title,
      refer: "",
      userAgen: navigator.userAgent,
      time: new Date().getTime(),
      url: top.location.href,
      actionValue: "",
      userAction: "Exposure",
      actionCode: null,
      userId: userInfo.value?.userId,
      userToken: "",
      channel: "MedSci_xAI",
      appId: dAppUuid.value,
      userUuid: userInfo.value?.openid,
    },
  ];
  // 使用 axios 发送 POST 请求
  await axios.post(
    "https://app-trace.medsci.cn/api/points/v1/user-action-batch",
    data
  );
};
const avatar = ref();

onMounted(async () => {
  hrefUrl.value =
    import.meta.env.MODE == "development"
      ? "http://localhost:3000" + "/" + locale.value
      : import.meta.env.MODE == "test"
      ? "https://ai.medon.com.cn" + "/" + locale.value
      : import.meta.env.MODE == "production"
      ? "https://ai.medsci.cn" + "/" + locale.value
      : "";
  if (!localStorage.getItem("conversation")) {
    let conversation = {};
    localStorage.setItem("conversation", JSON.stringify(conversation));
  }
  if (cookie.get("ai_apps_lang") == "zh") {
    isZH.value = true;
  }
  // if (!cookie.get("userInfo")) {
  //   loginStore.resetToken();
  //   if (!language || language == "zh") {
  //     return window.addLoginDom();
  //   } else {
  //     // 跳转到获取授权页
  //     console.log(location.origin + "/login")
  //     window.top.location.href = location.origin + "/login";
  //   }
  // } else {
  //   userInfo.value = cookie.get("userInfo")
  //     ? JSON.parse(cookie.get("userInfo"))
  //     : null;
  // }
  await handleLogin();
  insert();
  avatar.value = userInfo.value?.avatar
    ? userInfo.value?.avatar
    : "https://img.medsci.cn/web/img/user_icon.png";
  const res = await ajax.get(
    getAppByUuid + "?appUuid=" + route.params.app_uuid
  );
  const  res1 = await ajax.get(
    getPackageByKey 
  );
  subStatusDetail.value = res1.data;
  currentItem.value = res.data;
  injectCustomCode(res.data.customCss, res.data.customJs);
  dAppUuid.value = res.data.dAppUuid;
  title.value = res.data.appName;
  appUuid.value = res.data.appUuid;
});

const checkCookie = async () => {
  const newVal = cookie.get("userInfo");
  if (newVal !== userInfo.value) {
    userInfo.value = newVal ? JSON.parse(newVal) : "";
    if (userInfo.value) {
      loginStore.userInfo = userInfo.value;
      await loginStore.loginMain();
      clearInterval(intervalId);
    }
  }
};

const fullTitle = computed(() => {
  return `<${title.value}>${titles[cookie.get("ai_apps_lang")]}`;
});
useHead({
  // title: fullTitle,
  // meta: [
  //   {
  //     name: 'keywords',
  //     content: 'AI工具,智能体,研究助手,医学AI,智能分析',
  //   },
  //   {
  //     name: 'description',
  //     content: currentItem.appDescription,
  //   },
  //   { property: 'og:type', content: 'website' },
  //   { property: 'og:title', content: title.value },
  //   { property: 'og:description', content: currentItem.appDescription },
  //   { property: 'og:image', content: currentItem.appIcon },
  //   { name: 'twitter:card', content: 'summary_large_image' }, // 注意：根据常见用法推断 content 为 'summary_large_image'
  //   { name: 'twitter:title', content: title.value },
  //   { name: 'twitter:description', content: currentItem.appDescription },
  //   { name: 'twitter:image', content: currentItem.appIcon }
  // ]
});
const pageRef = ref(null);
const intervalId = setInterval(checkCookie, 1000);
onBeforeUnmount(() => clearInterval(intervalId));
//头像报错给个默认头像)
const handleError = () => {
  avatar.value = "https://img.medsci.cn/web/img/user_icon.png";
};

const loginAccount = () => {
  let language = getDefaultLanguageCode();
  if (!language || language == "zh") {
    window.addLoginDom();
  } else {
    window.top.location.href = location.origin + "/" + language + "/login";
  }
};
const refresh = () => {
  ElMessageBox.confirm(
    t(
      "tool.您即将开始一篇新文章。请先确保当前老文章已保存，然后点击确认按钮开始新的创作。点击确认后，当前页面将刷新，未保存的内容将会丢失。"
    ),
    t("tool.提示"),
    {
      confirmButtonText: t("tool.确认"),
      cancelButtonText: t("tool.取消"),
      type: "warning",
      customClass: "dialogs",
    }
  )
    .then(() => {
      localStorage.removeItem("conversation");
      localStorage.removeItem("writeContent" + "-" + appUuid.value);
      location.reload();
    })
    .catch(() => {});
};
const goHome = () => {
  top.location.href = hrefUrl.value;
};
const logout = () => {
  clearInterval(intervalId);
  let language = getDefaultLanguageCode();
  cookie.remove("userInfo", { domain: ".medon.com.cn" });
  cookie.remove("userInfo", { domain: ".medsci.cn" });
  cookie.remove("userInfo", { domain: "localhost" });
  localStorage.removeItem("hasuraToken");
  cookie.remove("yudaoToken", { domain: ".medsci.cn" });
  cookie.remove("yudaoToken", { domain: ".medon.com.cn" });
  cookie.remove("yudaoToken", { domain: "localhost" });
  localStorage.removeItem("conversation");
  var localStorageKeys = Object.keys(localStorage);
  localStorageKeys.forEach((key) => {
    if (key.includes("writeContent")) {
      localStorage.removeItem(key);
    }
  });
  if (window.location.origin.includes("medsci.cn")) {
    window.top.location.href =
      "https://www.medsci.cn/sso_logout?redirectUrl=" +
      window.top.location.href;
  } else {
    window.top.location.href =
      `https://portal-test.medon.com.cn/sso_logout?redirectUrl=` +
      window.top.location.href;
  }
  // if (!cookie.get("userInfo")) {
  loginStore.resetToken();
  //   if (!language || language == "zh") {
  //     return (window as any).addLoginDom();
  //   } else {
  //     window.top.location.href = location.origin + '/' + language + "/login";
  //   }
  // }
};
</script>

<template>
  <div>
    <customerService v-if="isZH" />
    <header class="container">
      <div class="left">
        <h3>
          <img
            class="logo"
            @click="goHome"
            src="https://img.medsci.cn/202412/8a5663cb16a84c45b9c8c5db4eda0075-6pyGtTcj0asd.png"
            alt=""
          />
          <span class="title" v-if="title">({{ title }})</span>
        </h3>
        <DialogueRecord />
        <el-button class="btn" @click="refresh"
          ><Plus style="width: 1em; height: 1em; margin-right: 8px" />{{
            $t("tool.新对话")
          }}</el-button
        >
      </div>
      <div class="right">
        <div v-if="locale == 'zh'" class="m_font change_lang m_none">
          <!-- <span class="mr-2 px-4 text-red-500" style="color:red;margin-right:px;padding:0 4px;font-size:12px ">未订阅</span> -->
          <button
            type="primary"
            class=" btn_sub"
            @click="subScript"
            >{{  subStatusDetail?.packageType == "免费"?"升级订阅":subStatusDetail?.packageType == "连续包月"||subStatusDetail?.packageType == "连续包年"?"修改订阅":"订阅" }}</button
          >
        </div>
        <!-- <a href="https://www.medsci.cn/messagePush">
          <img class="messsageImg"
            src="https://static.medsci.cn/public-image/ms-image/4ec68590-27ae-11ee-aed8-05e366306843_通知中心@2x.png"
            alt="" />
        </a> -->
        <a class="backImg" target="_top" title="梅斯小智" :href="hrefUrl">{{
          $t("tool.backtohome")
        }}</a>
        {{}}
        <a
          v-if="!userInfo?.userId"
          href="javascript: void(0)"
          class="ms-link"
          style="margin: 0 15px"
          @click="loginAccount"
          >{{ $t("tool.signIn") }}</a
        >
        <el-popover
          v-else
          placement="bottom-start"
          :width="350"
          trigger="hover"
          :popper-style="{ backgroundColor: '#f4f4f4', padding: 0 }"
        >
          <template #reference>
            <a href="#">
              <div class="img-area">
                <img
                  style="width: 32px; margin: 0 15px"
                  :src="avatar"
                  alt=""
                  @error="handleError"
                />
              </div>
            </a>
          </template>
          <template #default>
            <div
              style="
                display: flex;
                flex-direction: column;
                align-items: center;
                position: relative;
              "
            >
              <div class="iconHeader bg-write">
                <a class="exit" ms-statis="logout" href="#" @click="logout()">{{
                  $t("tool.退出")
                }}</a>
                <img class="avatar" :src="avatar" alt="" @error="handleError" />
                <span class="account">{{ userInfo.userName }}</span>
              </div>
              <!-- <ul class="menu-list bg-write">
                <li>
                  <a class="ms-statis" ms-statis="link" href="https://www.medsci.cn/user/feeds?uid=14ed8517687">
                    <img
                      src="https://static.medsci.cn/public-image/ms-image/5421b550-6225-11ec-8e2f-1389d01aad85_gerenzhuye.png"
                      alt="" />
                    <span>主页</span>
                  </a>
                </li>
                <li>
                  <a class="ms-statis" ms-statis="link" href="https://www.medsci.cn/user/account">
                    <img
                      src="https://static.medsci.cn/public-image/ms-image/5421b550-6225-11ec-8e2f-1389d01aad85_shezhi.png"
                      alt="" />
                    <span>设置</span>
                  </a>
                </li>
                <li>
                  <a class="ms-statis" ms-statis="link" href="https://www.medsci.cn/user/subscription">
                    <img
                      src="https://static.medsci.cn/public-image/ms-image/5421b550-6225-11ec-8e2f-1389d01aad85_dingyue.png"
                      alt="" />
                    <span>订阅</span>
                  </a>
                </li>
                <li>
                  <a class="ms-statis" ms-statis="link" target="_blank"
                    href="https://www.medsci.cn/order/service-query">
                    <img
                      src="https://static.medsci.cn/public-image/ms-image/5421b550-6225-11ec-8e2f-1389d01aad85_dingdan.png"
                      alt="" />
                    <span>订单</span>
                  </a>
                </li>
                <li>
                  <a class="ms-statis" ms-statis="link" href="https://www.medsci.cn/user/collection?uid=14ed8517687">
                    <img
                      src="https://static.medsci.cn/public-image/ms-image/5421b550-6225-11ec-8e2f-1389d01aad85_shoucang.png"
                      alt="" />
                    <span>收藏</span>
                  </a>
                </li>

                <li>
                  <a class="ms-statis" href="https://www.medsci.cn/course/view/list">
                    <img
                      src="https://static.medsci.cn/public-image/ms-image/5421b550-6225-11ec-8e2f-1389d01aad85_kecheng.png"
                      alt="" />
                    <span>课程</span>
                  </a>
                </li>
              </ul> -->
            </div>
          </template>
        </el-popover>
        <!-- <a href="https://www.medsci.cn/message/list.do" class="ms-link" ms-statis="link">咨询</a> -->
      </div>
    </header>
    <main>
      <Editor
        ref="pageRef"
        :appUuid="appUuid"
        :dAppUuid="dAppUuid"
        :subStatusDetail="subStatusDetail"
        v-if="appUuid && dAppUuid"
      />
    </main>
  </div>
</template>
<style >
.dialogs .el-message-box__container {
  align-items: flex-start;
}
</style>
<style scoped>
.container {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #ccc;
  padding: 10px;
  box-sizing: border-box;
  overflow: hidden;
}
.right :deep .el-button > span {
  font-size: 12px;
}
.btn {
  margin-left: 20px;
  margin-right: 10px;
  font-weight: 400;
  font-size: 12px;
  padding: 1px 10px;
  height: 25px;
  border-radius: 20px;
}
.left {
  display: flex;
  align-items: center;
}
.left .logo:hover {
  cursor: pointer;
}
.left img {
  height: 18px;
  margin-right: 4px;
}
.title {
  font-size: 14px;
}
.right {
  display: flex;
  align-items: center;
  margin-right: 20px;
}
h3 {
  margin-right: 30px;
  color: #606266;
  display: flex;
  align-items: center;
}
:deep(.no-border-input .el-input__wrapper) {
  box-shadow: none;
  border-radius: 0;
  border-bottom: 1px solid #dcdfe6;
}

.avatar {
  margin-top: 25px;
  width: 60px;
  height: 60px;
  margin-bottom: 10px;
  border-radius: 50%;
}

.exit {
  position: absolute;
  cursor: pointer;
  font-family: PingFangSC-Regular;
  font-size: 14px;
  color: #999;
  letter-spacing: 0;
  right: 34px;
  top: 20px;
  text-decoration: none;
}

.ms-statis img {
  width: 18px;
  height: 18px;
}

.iconHeader {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-bottom: 40px;
}
.backImg:hover ,.btn_sub:hover{
  cursor: pointer;
}

.btn_sub{
  display: unset;
  height: auto;
  padding-left: 14px;
  padding-right: 14px;
  padding-top: 4px;
  padding-bottom: 4px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  height: 28px;
  border: none;
  font-size: 12px;
  color: #614018;
  white-space: nowrap;
  background-image: linear-gradient(270deg, #FDE39B 0%, #F9D07A 88%) !important;
  margin-right: 8px;
}
.backImg {
  border-radius: 4px;
  background: #f1f5f9;
  padding: 6px 10px;
  font-size: 12px;
  width: max-content;
  margin-right: 8px;
  color: #666666;
  display: flex;
  align-items: center;
  text-decoration: none;
}
.backImg img {
  width: 12px;
  height: 12px;
  margin-right: 6px;
}
.menu-list {
  background-color: #fff;
  margin-top: 10px;
  list-style: none;
  display: flex;
  justify-content: space-around;
  width: 100%;
  margin-top: 10px;
  padding: 20px 10px;
  box-sizing: border-box;
}

.menu-list li a {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  color: #666;
  text-decoration: none;
  font-size: 12px;
}

.menu-list li a img {
  margin-bottom: 8px;
}

.account {
  font-size: 14px;
  color: #666;
}

.ms-link {
  color: #333;
  text-decoration: none;
  font-size: 13px;
}

.bg-write {
  background-color: #fff;
}

.messsageImg {
  width: 34px;
  height: 34px;
}
</style>