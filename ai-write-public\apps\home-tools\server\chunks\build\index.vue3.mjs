import { mergeProps, useSSRContext } from 'vue';
import { ssrRenderAttrs, ssrRenderAttr, ssrRenderClass } from 'vue/server-renderer';
import { _ as _imports_0, a as _imports_1 } from './qrcode.png.mjs';
import { _ as _export_sfc } from './_plugin-vue_export-helper.mjs';

const _sfc_main = {
  name: "AssistantComponent",
  data() {
    return {
      isCollapsed: false,
      isQrCodeVisible: false,
      isMobile: false
    };
  },
  mounted() {
    this.checkMobile();
    (void 0).addEventListener("resize", this.checkMobile);
  },
  beforeUnmount() {
    (void 0).removeEventListener("resize", this.checkMobile);
  },
  methods: {
    toggleCollapse() {
      this.isCollapsed = !this.isCollapsed;
      if (!this.isCollapsed) {
        this.isQrCodeVisible = true;
      } else {
        setTimeout(() => {
          this.isQrCodeVisible = false;
        }, 300);
      }
    },
    checkMobile() {
      this.isMobile = (void 0).innerWidth <= 768;
      if (this.isMobile) {
        this.isCollapsed = true;
        this.isQrCodeVisible = false;
      }
    }
  }
};
function _sfc_ssrRender(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(`<div${ssrRenderAttrs(mergeProps({
    class: ["assistant-container", { "is-collapsed": $data.isCollapsed }]
  }, _attrs))} data-v-8961986d><div class="assistant-icon" data-v-8961986d><img${ssrRenderAttr("src", _imports_0)} class="fas fa-user-astronaut" alt="客服" data-v-8961986d></div><div class="${ssrRenderClass([{ "is-visible": !$data.isCollapsed && $data.isQrCodeVisible }, "qr-code"])}" data-v-8961986d><img${ssrRenderAttr("src", _imports_1)} alt="QR Code" data-v-8961986d> 扫码添加小助手 </div></div>`);
}
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("components/customerService/index.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const customerService = /* @__PURE__ */ _export_sfc(_sfc_main, [["ssrRender", _sfc_ssrRender], ["__scopeId", "data-v-8961986d"]]);

export { customerService as c };
//# sourceMappingURL=index.vue3.mjs.map
