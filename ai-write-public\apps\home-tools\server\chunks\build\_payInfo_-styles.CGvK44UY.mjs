const _payInfo__vue_vue_type_style_index_0_scoped_8b8a31de_lang = "div[data-v-8b8a31de]{display:flex;justify-content:center;margin-top:10vh}div button[data-v-8b8a31de]{background:#fff;border:2px solid #2680eb;border-radius:10px;color:#2680eb;cursor:pointer;font-family:PingFang SC,PingFang SC-Regular;font-size:16px;font-weight:400;height:40px;line-height:38px;opacity:1}";

const _payInfo_Styles_CGvK44UY = [_payInfo__vue_vue_type_style_index_0_scoped_8b8a31de_lang];

export { _payInfo_Styles_CGvK44UY as default };
//# sourceMappingURL=_payInfo_-styles.CGvK44UY.mjs.map
