{"name": "unhead", "type": "module", "version": "2.0.4", "description": "Full-stack <head> manager built for any framework.", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://harlanzw.com/"}, "publishConfig": {"access": "public", "tag": "next"}, "license": "MIT", "funding": "https://github.com/sponsors/harlan-zw", "homepage": "https://unhead.unjs.io", "repository": {"type": "git", "url": "git+https://github.com/unjs/unhead.git", "directory": "packages/unhead"}, "bugs": {"url": "https://github.com/unjs/unhead/issues"}, "sideEffects": false, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "./plugins": {"types": "./dist/plugins.d.ts", "default": "./dist/plugins.mjs"}, "./server": {"types": "./dist/server.d.ts", "default": "./dist/server.mjs"}, "./client": {"types": "./dist/client.d.ts", "default": "./dist/client.mjs"}, "./legacy": {"types": "./dist/legacy.d.ts", "default": "./dist/legacy.mjs"}, "./utils": {"types": "./dist/utils.d.ts", "default": "./dist/utils.mjs"}, "./types": {"types": "./dist/types.d.ts", "default": "./dist/types.mjs"}, "./scripts": {"types": "./dist/scripts.d.ts", "default": "./dist/scripts.mjs"}}, "main": "dist/index.mjs", "module": "dist/index.mjs", "types": "dist/index.d.ts", "optionalPlugins": {"*": {"plugins": ["dist/plugins"], "server": ["dist/server"], "client": ["dist/client"], "legacy": ["dist/legacy"], "types": ["dist/types"], "utils": ["dist/utils"], "scripts": ["dist/scripts"]}}, "files": ["*.d.ts", "dist"], "dependencies": {"hookable": "^5.5.3"}, "scripts": {"build": "unbuild", "stub": "unbuild --stub", "test:attw": "attw --pack"}}