{"name": "yudao-ui-admin-vben", "version": "2.1.0-snapshot", "packageManager": "pnpm@8.9.0", "author": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://gitee.com/xingyuv"}, "license": "MIT", "homepage": "https://gitee.com/xingyuv", "repository": {"type": "git", "url": "git+https://gitee.com/xingyuv/vue-vben-admin.git"}, "bugs": {"url": "https://gitee.com/xingyuv/issues"}, "engines": {"node": "^18.0.0 || >=20.0.0", "pnpm": ">=8.9.0"}, "scripts": {"commit": "czg", "bootstrap": "pnpm install", "serve": "pnpm dev", "dev": "vite --open", "front": "vite --mode front", "build": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=8192 vite build && esno ./build/script/postBuild.ts", "build:test": "cross-env NODE_OPTIONS=--max-old-space-size=8192 vite build --mode test && esno ./build/script/postBuild.ts", "build:static": "cross-env NODE_OPTIONS=--max-old-space-size=8192 vite build --mode static && esno ./build/script/postBuild.ts", "build:no-cache": "pnpm store prune && pnpm build", "report": "cross-env REPORT=true pnpm build", "type:check": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "preview": "pnpm build && vite preview", "preview:dist": "vite preview", "clean:cache": "rimraf node_modules/.cache/ && rimraf node_modules/.vite", "clean:lib": "rimraf node_modules", "lint": "eslint .", "lint:fix": "eslint . --fix", "lint:stylelint": "stylelint \"**/*.{vue,css,less,scss}\" --fix --cache --cache-location node_modules/.cache/stylelint/", "lint:lint-staged": "lint-staged", "reinstall": "rimraf pnpm-lock.yaml && rimraf node_modules && npm run bootstrap", "gen:icon": "esno ./build/generate/icon/index.ts"}, "dependencies": {"@ant-design/colors": "^7.0.0", "@ant-design/icons-vue": "^7.0.1", "@apollo/client": "^3.10.4", "@iconify/iconify": "^3.1.1", "@kangc/v-md-editor": "^2.3.18", "@videojs-player/vue": "^1.0.0", "@vue/apollo-composable": "^4.0.2", "@vue/runtime-core": "^3.3.8", "@vueuse/core": "^10.6.1", "@zxcvbn-ts/core": "^3.0.4", "ant-design-vue": "^4.0.7", "ant-design-x-vue": "^1.0.3", "axios": "^1.6.1", "benz-amr-recorder": "^1.1.5", "codemirror": "5.65.15", "cron-parser": "^4.9.0", "cropperjs": "^1.6.1", "crypto-js": "^4.2.0", "dayjs": "^1.11.10", "echarts": "^5.4.3", "graphql": "^16.8.1", "graphql-tag": "^2.12.6", "lodash-es": "^4.17.21", "mitt": "^3.0.1", "nprogress": "^0.2.0", "path-to-regexp": "^6.2.1", "pinia": "^2.1.7", "pinia-plugin-persistedstate": "^3.2.0", "potmot-vue-editor": "^0.16.9", "print-js": "^1.6.0", "qs": "^6.11.2", "resize-observer-polyfill": "^1.5.1", "sortablejs": "^1.15.0", "tinymce": "5.10.7", "vditor": "^3.9.6", "video.js": "^7.21.5", "vue": "^3.3.8", "vue-i18n": "^9.6.5", "vue-json-pretty": "^2.2.4", "vue-router": "^4.2.5", "vue-types": "^5.1.1", "vuedraggable": "^4.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"@antfu/eslint-config": "^1.1.1", "@commitlint/cli": "^18.4.1", "@commitlint/config-conventional": "^18.4.0", "@iconify/json": "^2.2.141", "@purge-icons/generated": "^0.9.0", "@types/codemirror": "^5.60.13", "@types/crypto-js": "^4.2.1", "@types/fs-extra": "^11.0.4", "@types/inquirer": "^9.0.7", "@types/lodash-es": "^4.17.11", "@types/node": "^20.9.0", "@types/nprogress": "^0.2.3", "@types/qs": "^6.9.10", "@types/sortablejs": "^1.15.5", "@unocss/eslint-config": "^0.57.3", "@vitejs/plugin-vue": "4.4.1", "@vitejs/plugin-vue-jsx": "^3.0.2", "@vue/compiler-sfc": "^3.3.8", "cross-env": "^7.0.3", "cz-git": "^1.7.1", "czg": "^1.7.1", "dotenv": "^16.3.1", "eslint": "^8.53.0", "esno": "^4.0.0", "fs-extra": "^11.1.1", "husky": "^8.0.3", "inquirer": "^9.2.12", "less": "^4.2.0", "lint-staged": "^15.1.0", "picocolors": "^1.0.0", "postcss": "^8.4.31", "postcss-html": "^1.5.0", "postcss-less": "^6.0.0", "prettier": "^3.1.0", "rimraf": "^5.0.5", "rollup": "^4.4.0", "rollup-plugin-visualizer": "^5.9.2", "stylelint": "^15.11.0", "stylelint-config-recess-order": "^4.3.0", "stylelint-config-recommended": "^13.0.0", "stylelint-config-recommended-vue": "^1.5.0", "stylelint-config-standard": "^34.0.0", "stylelint-order": "^6.0.3", "stylelint-prettier": "^4.0.2", "terser": "^5.24.0", "typescript": "^5.2.2", "unocss": "^0.57.3", "vite": "^4.5.0", "vite-plugin-compression": "^0.5.1", "vite-plugin-mkcert": "^1.16.0", "vite-plugin-progress": "^0.0.7", "vite-plugin-purge-icons": "^0.9.2", "vite-plugin-pwa": "^0.16.7", "vite-plugin-svg-icons": "^2.0.1", "vite-vue-plugin-html": "^1.0.5", "vue-tsc": "^1.8.22"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix"], "{!(package)*.json,*.code-snippets,.!(browserslist)*rc}": ["prettier --write --parser json"], "package.json": ["prettier --write"], "*.vue": ["eslint --fix", "stylelint --fix"], "*.{scss,less,styl,html}": ["stylelint --fix"], "*.md": ["prettier --write"]}, "config": {"commitizen": {"path": "node_modules/cz-git"}}, "resolutions": {"minimatch": "^9.0.0"}}