import{aH as we,aU as nn,aV as Ln,aW as rn,aX as an,aY as sn,aZ as se,a_ as An,_ as d,g as In,s as Wn,q as Hn,p as On,a as Nn,b as Vn,c as _t,d as Zt,e as Pn,a$ as at,l as Kt,k as zn,j as Rn,y as qn,u as Zn}from"./index-DP5_VPip.js";import{b as Bn,t as He,c as Xn,a as Gn,l as Qn}from"./linear-B0fUz3yI.js";import{i as $n}from"./init-Gi6I4Gst.js";var Xt={exports:{}},jn=Xt.exports,Oe;function Jn(){return Oe||(Oe=1,function(t,e){(function(n,r){t.exports=r()})(jn,function(){return function(n,r){var a=r.prototype,i=a.format;a.format=function(s){var p=this,M=this.$locale();if(!this.isValid())return i.bind(this)(s);var T=this.$utils(),g=(s||"YYYY-MM-DDTHH:mm:ssZ").replace(/\[([^\]]+)]|Q|wo|ww|w|WW|W|zzz|z|gggg|GGGG|Do|X|x|k{1,2}|S/g,function(U){switch(U){case"Q":return Math.ceil((p.$M+1)/3);case"Do":return M.ordinal(p.$D);case"gggg":return p.weekYear();case"GGGG":return p.isoWeekYear();case"wo":return M.ordinal(p.week(),"W");case"w":case"ww":return T.s(p.week(),U==="w"?1:2,"0");case"W":case"WW":return T.s(p.isoWeek(),U==="W"?1:2,"0");case"k":case"kk":return T.s(String(p.$H===0?24:p.$H),U==="k"?1:2,"0");case"X":return Math.floor(p.$d.getTime()/1e3);case"x":return p.$d.getTime();case"z":return"["+p.offsetName()+"]";case"zzz":return"["+p.offsetName("long")+"]";default:return U}});return i.bind(this)(g)}}})}(Xt)),Xt.exports}var Kn=Jn();const tr=we(Kn);var Gt={exports:{}},er=Gt.exports,Ne;function nr(){return Ne||(Ne=1,function(t,e){(function(n,r){t.exports=r()})(er,function(){var n={LTS:"h:mm:ss A",LT:"h:mm A",L:"MM/DD/YYYY",LL:"MMMM D, YYYY",LLL:"MMMM D, YYYY h:mm A",LLLL:"dddd, MMMM D, YYYY h:mm A"},r=/(\[[^[]*\])|([-_:/.,()\s]+)|(A|a|Q|YYYY|YY?|ww?|MM?M?M?|Do|DD?|hh?|HH?|mm?|ss?|S{1,3}|z|ZZ?)/g,a=/\d/,i=/\d\d/,s=/\d\d?/,p=/\d*[^-_:/,()\s\d]+/,M={},T=function(D){return(D=+D)+(D>68?1900:2e3)},g=function(D){return function(I){this[D]=+I}},U=[/[+-]\d\d:?(\d\d)?|Z/,function(D){(this.zone||(this.zone={})).offset=function(I){if(!I||I==="Z")return 0;var V=I.match(/([+-]|\d\d)/g),W=60*V[1]+(+V[2]||0);return W===0?0:V[0]==="+"?-W:W}(D)}],C=function(D){var I=M[D];return I&&(I.indexOf?I:I.s.concat(I.f))},x=function(D,I){var V,W=M.meridiem;if(W){for(var Z=1;Z<=24;Z+=1)if(D.indexOf(W(Z,0,I))>-1){V=Z>12;break}}else V=D===(I?"pm":"PM");return V},X={A:[p,function(D){this.afternoon=x(D,!1)}],a:[p,function(D){this.afternoon=x(D,!0)}],Q:[a,function(D){this.month=3*(D-1)+1}],S:[a,function(D){this.milliseconds=100*+D}],SS:[i,function(D){this.milliseconds=10*+D}],SSS:[/\d{3}/,function(D){this.milliseconds=+D}],s:[s,g("seconds")],ss:[s,g("seconds")],m:[s,g("minutes")],mm:[s,g("minutes")],H:[s,g("hours")],h:[s,g("hours")],HH:[s,g("hours")],hh:[s,g("hours")],D:[s,g("day")],DD:[i,g("day")],Do:[p,function(D){var I=M.ordinal,V=D.match(/\d+/);if(this.day=V[0],I)for(var W=1;W<=31;W+=1)I(W).replace(/\[|\]/g,"")===D&&(this.day=W)}],w:[s,g("week")],ww:[i,g("week")],M:[s,g("month")],MM:[i,g("month")],MMM:[p,function(D){var I=C("months"),V=(C("monthsShort")||I.map(function(W){return W.slice(0,3)})).indexOf(D)+1;if(V<1)throw new Error;this.month=V%12||V}],MMMM:[p,function(D){var I=C("months").indexOf(D)+1;if(I<1)throw new Error;this.month=I%12||I}],Y:[/[+-]?\d+/,g("year")],YY:[i,function(D){this.year=T(D)}],YYYY:[/\d{4}/,g("year")],Z:U,ZZ:U};function H(D){var I,V;I=D,V=M&&M.formats;for(var W=(D=I.replace(/(\[[^\]]+])|(LTS?|l{1,4}|L{1,4})/g,function(S,_,k){var Y=k&&k.toUpperCase();return _||V[k]||n[k]||V[Y].replace(/(\[[^\]]+])|(MMMM|MM|DD|dddd)/g,function(l,h,y){return h||y.slice(1)})})).match(r),Z=W.length,$=0;$<Z;$+=1){var w=W[$],O=X[w],b=O&&O[0],F=O&&O[1];W[$]=F?{regex:b,parser:F}:w.replace(/^\[|\]$/g,"")}return function(S){for(var _={},k=0,Y=0;k<Z;k+=1){var l=W[k];if(typeof l=="string")Y+=l.length;else{var h=l.regex,y=l.parser,m=S.slice(Y),E=h.exec(m)[0];y.call(_,E),S=S.replace(E,"")}}return function(c){var u=c.afternoon;if(u!==void 0){var o=c.hours;u?o<12&&(c.hours+=12):o===12&&(c.hours=0),delete c.afternoon}}(_),_}}return function(D,I,V){V.p.customParseFormat=!0,D&&D.parseTwoDigitYear&&(T=D.parseTwoDigitYear);var W=I.prototype,Z=W.parse;W.parse=function($){var w=$.date,O=$.utc,b=$.args;this.$u=O;var F=b[1];if(typeof F=="string"){var S=b[2]===!0,_=b[3]===!0,k=S||_,Y=b[2];_&&(Y=b[2]),M=this.$locale(),!S&&Y&&(M=V.Ls[Y]),this.$d=function(m,E,c,u){try{if(["x","X"].indexOf(E)>-1)return new Date((E==="X"?1e3:1)*m);var o=H(E)(m),R=o.year,P=o.month,z=o.day,K=o.hours,G=o.minutes,j=o.seconds,it=o.milliseconds,v=o.zone,A=o.week,N=new Date,f=z||(R||P?1:N.getDate()),J=R||N.getFullYear(),L=0;R&&!P||(L=P>0?P-1:N.getMonth());var Q,B=K||0,rt=G||0,st=j||0,pt=it||0;return v?new Date(Date.UTC(J,L,f,B,rt,st,pt+60*v.offset*1e3)):c?new Date(Date.UTC(J,L,f,B,rt,st,pt)):(Q=new Date(J,L,f,B,rt,st,pt),A&&(Q=u(Q).week(A).toDate()),Q)}catch{return new Date("")}}(w,F,O,V),this.init(),Y&&Y!==!0&&(this.$L=this.locale(Y).$L),k&&w!=this.format(F)&&(this.$d=new Date("")),M={}}else if(F instanceof Array)for(var l=F.length,h=1;h<=l;h+=1){b[1]=F[h-1];var y=V.apply(this,b);if(y.isValid()){this.$d=y.$d,this.$L=y.$L,this.init();break}h===l&&(this.$d=new Date(""))}else Z.call(this,$)}}})}(Gt)),Gt.exports}var rr=nr();const ar=we(rr);function ir(t,e){let n;if(e===void 0)for(const r of t)r!=null&&(n<r||n===void 0&&r>=r)&&(n=r);else{let r=-1;for(let a of t)(a=e(a,++r,t))!=null&&(n<a||n===void 0&&a>=a)&&(n=a)}return n}function sr(t,e){let n;if(e===void 0)for(const r of t)r!=null&&(n>r||n===void 0&&r>=r)&&(n=r);else{let r=-1;for(let a of t)(a=e(a,++r,t))!=null&&(n>a||n===void 0&&a>=a)&&(n=a)}return n}function or(t){return t}var Qt=1,oe=2,ke=3,Bt=4,Ve=1e-6;function cr(t){return"translate("+t+",0)"}function ur(t){return"translate(0,"+t+")"}function lr(t){return e=>+t(e)}function fr(t,e){return e=Math.max(0,t.bandwidth()-e*2)/2,t.round()&&(e=Math.round(e)),n=>+t(n)+e}function hr(){return!this.__axis}function on(t,e){var n=[],r=null,a=null,i=6,s=6,p=3,M=typeof window<"u"&&window.devicePixelRatio>1?0:.5,T=t===Qt||t===Bt?-1:1,g=t===Bt||t===oe?"x":"y",U=t===Qt||t===ke?cr:ur;function C(x){var X=r??(e.ticks?e.ticks.apply(e,n):e.domain()),H=a??(e.tickFormat?e.tickFormat.apply(e,n):or),D=Math.max(i,0)+p,I=e.range(),V=+I[0]+M,W=+I[I.length-1]+M,Z=(e.bandwidth?fr:lr)(e.copy(),M),$=x.selection?x.selection():x,w=$.selectAll(".domain").data([null]),O=$.selectAll(".tick").data(X,e).order(),b=O.exit(),F=O.enter().append("g").attr("class","tick"),S=O.select("line"),_=O.select("text");w=w.merge(w.enter().insert("path",".tick").attr("class","domain").attr("stroke","currentColor")),O=O.merge(F),S=S.merge(F.append("line").attr("stroke","currentColor").attr(g+"2",T*i)),_=_.merge(F.append("text").attr("fill","currentColor").attr(g,T*D).attr("dy",t===Qt?"0em":t===ke?"0.71em":"0.32em")),x!==$&&(w=w.transition(x),O=O.transition(x),S=S.transition(x),_=_.transition(x),b=b.transition(x).attr("opacity",Ve).attr("transform",function(k){return isFinite(k=Z(k))?U(k+M):this.getAttribute("transform")}),F.attr("opacity",Ve).attr("transform",function(k){var Y=this.parentNode.__axis;return U((Y&&isFinite(Y=Y(k))?Y:Z(k))+M)})),b.remove(),w.attr("d",t===Bt||t===oe?s?"M"+T*s+","+V+"H"+M+"V"+W+"H"+T*s:"M"+M+","+V+"V"+W:s?"M"+V+","+T*s+"V"+M+"H"+W+"V"+T*s:"M"+V+","+M+"H"+W),O.attr("opacity",1).attr("transform",function(k){return U(Z(k)+M)}),S.attr(g+"2",T*i),_.attr(g,T*D).text(H),$.filter(hr).attr("fill","none").attr("font-size",10).attr("font-family","sans-serif").attr("text-anchor",t===oe?"start":t===Bt?"end":"middle"),$.each(function(){this.__axis=Z})}return C.scale=function(x){return arguments.length?(e=x,C):e},C.ticks=function(){return n=Array.from(arguments),C},C.tickArguments=function(x){return arguments.length?(n=x==null?[]:Array.from(x),C):n.slice()},C.tickValues=function(x){return arguments.length?(r=x==null?null:Array.from(x),C):r&&r.slice()},C.tickFormat=function(x){return arguments.length?(a=x,C):a},C.tickSize=function(x){return arguments.length?(i=s=+x,C):i},C.tickSizeInner=function(x){return arguments.length?(i=+x,C):i},C.tickSizeOuter=function(x){return arguments.length?(s=+x,C):s},C.tickPadding=function(x){return arguments.length?(p=+x,C):p},C.offset=function(x){return arguments.length?(M=+x,C):M},C}function dr(t){return on(Qt,t)}function mr(t){return on(ke,t)}const gr=Math.PI/180,yr=180/Math.PI,te=18,cn=.96422,un=1,ln=.82521,fn=4/29,St=6/29,hn=3*St*St,kr=St*St*St;function dn(t){if(t instanceof ft)return new ft(t.l,t.a,t.b,t.opacity);if(t instanceof dt)return mn(t);t instanceof nn||(t=Ln(t));var e=fe(t.r),n=fe(t.g),r=fe(t.b),a=ce((.2225045*e+.7168786*n+.0606169*r)/un),i,s;return e===n&&n===r?i=s=a:(i=ce((.4360747*e+.3850649*n+.1430804*r)/cn),s=ce((.0139322*e+.0971045*n+.7141733*r)/ln)),new ft(116*a-16,500*(i-a),200*(a-s),t.opacity)}function pr(t,e,n,r){return arguments.length===1?dn(t):new ft(t,e,n,r??1)}function ft(t,e,n,r){this.l=+t,this.a=+e,this.b=+n,this.opacity=+r}rn(ft,pr,an(sn,{brighter(t){return new ft(this.l+te*(t??1),this.a,this.b,this.opacity)},darker(t){return new ft(this.l-te*(t??1),this.a,this.b,this.opacity)},rgb(){var t=(this.l+16)/116,e=isNaN(this.a)?t:t+this.a/500,n=isNaN(this.b)?t:t-this.b/200;return e=cn*ue(e),t=un*ue(t),n=ln*ue(n),new nn(le(3.1338561*e-1.6168667*t-.4906146*n),le(-.9787684*e+1.9161415*t+.033454*n),le(.0719453*e-.2289914*t+1.4052427*n),this.opacity)}}));function ce(t){return t>kr?Math.pow(t,1/3):t/hn+fn}function ue(t){return t>St?t*t*t:hn*(t-fn)}function le(t){return 255*(t<=.0031308?12.92*t:1.055*Math.pow(t,1/2.4)-.055)}function fe(t){return(t/=255)<=.04045?t/12.92:Math.pow((t+.055)/1.055,2.4)}function vr(t){if(t instanceof dt)return new dt(t.h,t.c,t.l,t.opacity);if(t instanceof ft||(t=dn(t)),t.a===0&&t.b===0)return new dt(NaN,0<t.l&&t.l<100?0:NaN,t.l,t.opacity);var e=Math.atan2(t.b,t.a)*yr;return new dt(e<0?e+360:e,Math.sqrt(t.a*t.a+t.b*t.b),t.l,t.opacity)}function pe(t,e,n,r){return arguments.length===1?vr(t):new dt(t,e,n,r??1)}function dt(t,e,n,r){this.h=+t,this.c=+e,this.l=+n,this.opacity=+r}function mn(t){if(isNaN(t.h))return new ft(t.l,0,0,t.opacity);var e=t.h*gr;return new ft(t.l,Math.cos(e)*t.c,Math.sin(e)*t.c,t.opacity)}rn(dt,pe,an(sn,{brighter(t){return new dt(this.h,this.c,this.l+te*(t??1),this.opacity)},darker(t){return new dt(this.h,this.c,this.l-te*(t??1),this.opacity)},rgb(){return mn(this).rgb()}}));function Tr(t){return function(e,n){var r=t((e=pe(e)).h,(n=pe(n)).h),a=se(e.c,n.c),i=se(e.l,n.l),s=se(e.opacity,n.opacity);return function(p){return e.h=r(p),e.c=a(p),e.l=i(p),e.opacity=s(p),e+""}}}const xr=Tr(An);function br(t,e){t=t.slice();var n=0,r=t.length-1,a=t[n],i=t[r],s;return i<a&&(s=n,n=r,r=s,s=a,a=i,i=s),t[n]=e.floor(a),t[r]=e.ceil(i),t}const he=new Date,de=new Date;function et(t,e,n,r){function a(i){return t(i=arguments.length===0?new Date:new Date(+i)),i}return a.floor=i=>(t(i=new Date(+i)),i),a.ceil=i=>(t(i=new Date(i-1)),e(i,1),t(i),i),a.round=i=>{const s=a(i),p=a.ceil(i);return i-s<p-i?s:p},a.offset=(i,s)=>(e(i=new Date(+i),s==null?1:Math.floor(s)),i),a.range=(i,s,p)=>{const M=[];if(i=a.ceil(i),p=p==null?1:Math.floor(p),!(i<s)||!(p>0))return M;let T;do M.push(T=new Date(+i)),e(i,p),t(i);while(T<i&&i<s);return M},a.filter=i=>et(s=>{if(s>=s)for(;t(s),!i(s);)s.setTime(s-1)},(s,p)=>{if(s>=s)if(p<0)for(;++p<=0;)for(;e(s,-1),!i(s););else for(;--p>=0;)for(;e(s,1),!i(s););}),n&&(a.count=(i,s)=>(he.setTime(+i),de.setTime(+s),t(he),t(de),Math.floor(n(he,de))),a.every=i=>(i=Math.floor(i),!isFinite(i)||!(i>0)?null:i>1?a.filter(r?s=>r(s)%i===0:s=>a.count(0,s)%i===0):a)),a}const Yt=et(()=>{},(t,e)=>{t.setTime(+t+e)},(t,e)=>e-t);Yt.every=t=>(t=Math.floor(t),!isFinite(t)||!(t>0)?null:t>1?et(e=>{e.setTime(Math.floor(e/t)*t)},(e,n)=>{e.setTime(+e+n*t)},(e,n)=>(n-e)/t):Yt);Yt.range;const mt=1e3,ct=mt*60,gt=ct*60,yt=gt*24,Ce=yt*7,Pe=yt*30,me=yt*365,vt=et(t=>{t.setTime(t-t.getMilliseconds())},(t,e)=>{t.setTime(+t+e*mt)},(t,e)=>(e-t)/mt,t=>t.getUTCSeconds());vt.range;const Wt=et(t=>{t.setTime(t-t.getMilliseconds()-t.getSeconds()*mt)},(t,e)=>{t.setTime(+t+e*ct)},(t,e)=>(e-t)/ct,t=>t.getMinutes());Wt.range;const wr=et(t=>{t.setUTCSeconds(0,0)},(t,e)=>{t.setTime(+t+e*ct)},(t,e)=>(e-t)/ct,t=>t.getUTCMinutes());wr.range;const Ht=et(t=>{t.setTime(t-t.getMilliseconds()-t.getSeconds()*mt-t.getMinutes()*ct)},(t,e)=>{t.setTime(+t+e*gt)},(t,e)=>(e-t)/gt,t=>t.getHours());Ht.range;const Cr=et(t=>{t.setUTCMinutes(0,0,0)},(t,e)=>{t.setTime(+t+e*gt)},(t,e)=>(e-t)/gt,t=>t.getUTCHours());Cr.range;const Tt=et(t=>t.setHours(0,0,0,0),(t,e)=>t.setDate(t.getDate()+e),(t,e)=>(e-t-(e.getTimezoneOffset()-t.getTimezoneOffset())*ct)/yt,t=>t.getDate()-1);Tt.range;const De=et(t=>{t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+e)},(t,e)=>(e-t)/yt,t=>t.getUTCDate()-1);De.range;const Dr=et(t=>{t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+e)},(t,e)=>(e-t)/yt,t=>Math.floor(t/yt));Dr.range;function wt(t){return et(e=>{e.setDate(e.getDate()-(e.getDay()+7-t)%7),e.setHours(0,0,0,0)},(e,n)=>{e.setDate(e.getDate()+n*7)},(e,n)=>(n-e-(n.getTimezoneOffset()-e.getTimezoneOffset())*ct)/Ce)}const Vt=wt(0),Ot=wt(1),gn=wt(2),yn=wt(3),xt=wt(4),kn=wt(5),pn=wt(6);Vt.range;Ot.range;gn.range;yn.range;xt.range;kn.range;pn.range;function Ct(t){return et(e=>{e.setUTCDate(e.getUTCDate()-(e.getUTCDay()+7-t)%7),e.setUTCHours(0,0,0,0)},(e,n)=>{e.setUTCDate(e.getUTCDate()+n*7)},(e,n)=>(n-e)/Ce)}const vn=Ct(0),ee=Ct(1),Mr=Ct(2),_r=Ct(3),Ut=Ct(4),Sr=Ct(5),Fr=Ct(6);vn.range;ee.range;Mr.range;_r.range;Ut.range;Sr.range;Fr.range;const Nt=et(t=>{t.setDate(1),t.setHours(0,0,0,0)},(t,e)=>{t.setMonth(t.getMonth()+e)},(t,e)=>e.getMonth()-t.getMonth()+(e.getFullYear()-t.getFullYear())*12,t=>t.getMonth());Nt.range;const Yr=et(t=>{t.setUTCDate(1),t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCMonth(t.getUTCMonth()+e)},(t,e)=>e.getUTCMonth()-t.getUTCMonth()+(e.getUTCFullYear()-t.getUTCFullYear())*12,t=>t.getUTCMonth());Yr.range;const kt=et(t=>{t.setMonth(0,1),t.setHours(0,0,0,0)},(t,e)=>{t.setFullYear(t.getFullYear()+e)},(t,e)=>e.getFullYear()-t.getFullYear(),t=>t.getFullYear());kt.every=t=>!isFinite(t=Math.floor(t))||!(t>0)?null:et(e=>{e.setFullYear(Math.floor(e.getFullYear()/t)*t),e.setMonth(0,1),e.setHours(0,0,0,0)},(e,n)=>{e.setFullYear(e.getFullYear()+n*t)});kt.range;const bt=et(t=>{t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCFullYear(t.getUTCFullYear()+e)},(t,e)=>e.getUTCFullYear()-t.getUTCFullYear(),t=>t.getUTCFullYear());bt.every=t=>!isFinite(t=Math.floor(t))||!(t>0)?null:et(e=>{e.setUTCFullYear(Math.floor(e.getUTCFullYear()/t)*t),e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},(e,n)=>{e.setUTCFullYear(e.getUTCFullYear()+n*t)});bt.range;function Ur(t,e,n,r,a,i){const s=[[vt,1,mt],[vt,5,5*mt],[vt,15,15*mt],[vt,30,30*mt],[i,1,ct],[i,5,5*ct],[i,15,15*ct],[i,30,30*ct],[a,1,gt],[a,3,3*gt],[a,6,6*gt],[a,12,12*gt],[r,1,yt],[r,2,2*yt],[n,1,Ce],[e,1,Pe],[e,3,3*Pe],[t,1,me]];function p(T,g,U){const C=g<T;C&&([T,g]=[g,T]);const x=U&&typeof U.range=="function"?U:M(T,g,U),X=x?x.range(T,+g+1):[];return C?X.reverse():X}function M(T,g,U){const C=Math.abs(g-T)/U,x=Bn(([,,D])=>D).right(s,C);if(x===s.length)return t.every(He(T/me,g/me,U));if(x===0)return Yt.every(Math.max(He(T,g,U),1));const[X,H]=s[C/s[x-1][2]<s[x][2]/C?x-1:x];return X.every(H)}return[p,M]}const[Er,Lr]=Ur(kt,Nt,Vt,Tt,Ht,Wt);function ge(t){if(0<=t.y&&t.y<100){var e=new Date(-1,t.m,t.d,t.H,t.M,t.S,t.L);return e.setFullYear(t.y),e}return new Date(t.y,t.m,t.d,t.H,t.M,t.S,t.L)}function ye(t){if(0<=t.y&&t.y<100){var e=new Date(Date.UTC(-1,t.m,t.d,t.H,t.M,t.S,t.L));return e.setUTCFullYear(t.y),e}return new Date(Date.UTC(t.y,t.m,t.d,t.H,t.M,t.S,t.L))}function Lt(t,e,n){return{y:t,m:e,d:n,H:0,M:0,S:0,L:0}}function Ar(t){var e=t.dateTime,n=t.date,r=t.time,a=t.periods,i=t.days,s=t.shortDays,p=t.months,M=t.shortMonths,T=At(a),g=It(a),U=At(i),C=It(i),x=At(s),X=It(s),H=At(p),D=It(p),I=At(M),V=It(M),W={a:m,A:E,b:c,B:u,c:null,d:Xe,e:Xe,f:ra,g:da,G:ga,H:ta,I:ea,j:na,L:Tn,m:aa,M:ia,p:o,q:R,Q:$e,s:je,S:sa,u:oa,U:ca,V:ua,w:la,W:fa,x:null,X:null,y:ha,Y:ma,Z:ya,"%":Qe},Z={a:P,A:z,b:K,B:G,c:null,d:Ge,e:Ge,f:Ta,g:Ya,G:Ea,H:ka,I:pa,j:va,L:bn,m:xa,M:ba,p:j,q:it,Q:$e,s:je,S:wa,u:Ca,U:Da,V:Ma,w:_a,W:Sa,x:null,X:null,y:Fa,Y:Ua,Z:La,"%":Qe},$={a:S,A:_,b:k,B:Y,c:l,d:Ze,e:Ze,f:$r,g:qe,G:Re,H:Be,I:Be,j:Br,L:Qr,m:Zr,M:Xr,p:F,q:qr,Q:Jr,s:Kr,S:Gr,u:Nr,U:Vr,V:Pr,w:Or,W:zr,x:h,X:y,y:qe,Y:Re,Z:Rr,"%":jr};W.x=w(n,W),W.X=w(r,W),W.c=w(e,W),Z.x=w(n,Z),Z.X=w(r,Z),Z.c=w(e,Z);function w(v,A){return function(N){var f=[],J=-1,L=0,Q=v.length,B,rt,st;for(N instanceof Date||(N=new Date(+N));++J<Q;)v.charCodeAt(J)===37&&(f.push(v.slice(L,J)),(rt=ze[B=v.charAt(++J)])!=null?B=v.charAt(++J):rt=B==="e"?" ":"0",(st=A[B])&&(B=st(N,rt)),f.push(B),L=J+1);return f.push(v.slice(L,J)),f.join("")}}function O(v,A){return function(N){var f=Lt(1900,void 0,1),J=b(f,v,N+="",0),L,Q;if(J!=N.length)return null;if("Q"in f)return new Date(f.Q);if("s"in f)return new Date(f.s*1e3+("L"in f?f.L:0));if(A&&!("Z"in f)&&(f.Z=0),"p"in f&&(f.H=f.H%12+f.p*12),f.m===void 0&&(f.m="q"in f?f.q:0),"V"in f){if(f.V<1||f.V>53)return null;"w"in f||(f.w=1),"Z"in f?(L=ye(Lt(f.y,0,1)),Q=L.getUTCDay(),L=Q>4||Q===0?ee.ceil(L):ee(L),L=De.offset(L,(f.V-1)*7),f.y=L.getUTCFullYear(),f.m=L.getUTCMonth(),f.d=L.getUTCDate()+(f.w+6)%7):(L=ge(Lt(f.y,0,1)),Q=L.getDay(),L=Q>4||Q===0?Ot.ceil(L):Ot(L),L=Tt.offset(L,(f.V-1)*7),f.y=L.getFullYear(),f.m=L.getMonth(),f.d=L.getDate()+(f.w+6)%7)}else("W"in f||"U"in f)&&("w"in f||(f.w="u"in f?f.u%7:"W"in f?1:0),Q="Z"in f?ye(Lt(f.y,0,1)).getUTCDay():ge(Lt(f.y,0,1)).getDay(),f.m=0,f.d="W"in f?(f.w+6)%7+f.W*7-(Q+5)%7:f.w+f.U*7-(Q+6)%7);return"Z"in f?(f.H+=f.Z/100|0,f.M+=f.Z%100,ye(f)):ge(f)}}function b(v,A,N,f){for(var J=0,L=A.length,Q=N.length,B,rt;J<L;){if(f>=Q)return-1;if(B=A.charCodeAt(J++),B===37){if(B=A.charAt(J++),rt=$[B in ze?A.charAt(J++):B],!rt||(f=rt(v,N,f))<0)return-1}else if(B!=N.charCodeAt(f++))return-1}return f}function F(v,A,N){var f=T.exec(A.slice(N));return f?(v.p=g.get(f[0].toLowerCase()),N+f[0].length):-1}function S(v,A,N){var f=x.exec(A.slice(N));return f?(v.w=X.get(f[0].toLowerCase()),N+f[0].length):-1}function _(v,A,N){var f=U.exec(A.slice(N));return f?(v.w=C.get(f[0].toLowerCase()),N+f[0].length):-1}function k(v,A,N){var f=I.exec(A.slice(N));return f?(v.m=V.get(f[0].toLowerCase()),N+f[0].length):-1}function Y(v,A,N){var f=H.exec(A.slice(N));return f?(v.m=D.get(f[0].toLowerCase()),N+f[0].length):-1}function l(v,A,N){return b(v,e,A,N)}function h(v,A,N){return b(v,n,A,N)}function y(v,A,N){return b(v,r,A,N)}function m(v){return s[v.getDay()]}function E(v){return i[v.getDay()]}function c(v){return M[v.getMonth()]}function u(v){return p[v.getMonth()]}function o(v){return a[+(v.getHours()>=12)]}function R(v){return 1+~~(v.getMonth()/3)}function P(v){return s[v.getUTCDay()]}function z(v){return i[v.getUTCDay()]}function K(v){return M[v.getUTCMonth()]}function G(v){return p[v.getUTCMonth()]}function j(v){return a[+(v.getUTCHours()>=12)]}function it(v){return 1+~~(v.getUTCMonth()/3)}return{format:function(v){var A=w(v+="",W);return A.toString=function(){return v},A},parse:function(v){var A=O(v+="",!1);return A.toString=function(){return v},A},utcFormat:function(v){var A=w(v+="",Z);return A.toString=function(){return v},A},utcParse:function(v){var A=O(v+="",!0);return A.toString=function(){return v},A}}}var ze={"-":"",_:" ",0:"0"},nt=/^\s*\d+/,Ir=/^%/,Wr=/[\\^$*+?|[\]().{}]/g;function q(t,e,n){var r=t<0?"-":"",a=(r?-t:t)+"",i=a.length;return r+(i<n?new Array(n-i+1).join(e)+a:a)}function Hr(t){return t.replace(Wr,"\\$&")}function At(t){return new RegExp("^(?:"+t.map(Hr).join("|")+")","i")}function It(t){return new Map(t.map((e,n)=>[e.toLowerCase(),n]))}function Or(t,e,n){var r=nt.exec(e.slice(n,n+1));return r?(t.w=+r[0],n+r[0].length):-1}function Nr(t,e,n){var r=nt.exec(e.slice(n,n+1));return r?(t.u=+r[0],n+r[0].length):-1}function Vr(t,e,n){var r=nt.exec(e.slice(n,n+2));return r?(t.U=+r[0],n+r[0].length):-1}function Pr(t,e,n){var r=nt.exec(e.slice(n,n+2));return r?(t.V=+r[0],n+r[0].length):-1}function zr(t,e,n){var r=nt.exec(e.slice(n,n+2));return r?(t.W=+r[0],n+r[0].length):-1}function Re(t,e,n){var r=nt.exec(e.slice(n,n+4));return r?(t.y=+r[0],n+r[0].length):-1}function qe(t,e,n){var r=nt.exec(e.slice(n,n+2));return r?(t.y=+r[0]+(+r[0]>68?1900:2e3),n+r[0].length):-1}function Rr(t,e,n){var r=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(e.slice(n,n+6));return r?(t.Z=r[1]?0:-(r[2]+(r[3]||"00")),n+r[0].length):-1}function qr(t,e,n){var r=nt.exec(e.slice(n,n+1));return r?(t.q=r[0]*3-3,n+r[0].length):-1}function Zr(t,e,n){var r=nt.exec(e.slice(n,n+2));return r?(t.m=r[0]-1,n+r[0].length):-1}function Ze(t,e,n){var r=nt.exec(e.slice(n,n+2));return r?(t.d=+r[0],n+r[0].length):-1}function Br(t,e,n){var r=nt.exec(e.slice(n,n+3));return r?(t.m=0,t.d=+r[0],n+r[0].length):-1}function Be(t,e,n){var r=nt.exec(e.slice(n,n+2));return r?(t.H=+r[0],n+r[0].length):-1}function Xr(t,e,n){var r=nt.exec(e.slice(n,n+2));return r?(t.M=+r[0],n+r[0].length):-1}function Gr(t,e,n){var r=nt.exec(e.slice(n,n+2));return r?(t.S=+r[0],n+r[0].length):-1}function Qr(t,e,n){var r=nt.exec(e.slice(n,n+3));return r?(t.L=+r[0],n+r[0].length):-1}function $r(t,e,n){var r=nt.exec(e.slice(n,n+6));return r?(t.L=Math.floor(r[0]/1e3),n+r[0].length):-1}function jr(t,e,n){var r=Ir.exec(e.slice(n,n+1));return r?n+r[0].length:-1}function Jr(t,e,n){var r=nt.exec(e.slice(n));return r?(t.Q=+r[0],n+r[0].length):-1}function Kr(t,e,n){var r=nt.exec(e.slice(n));return r?(t.s=+r[0],n+r[0].length):-1}function Xe(t,e){return q(t.getDate(),e,2)}function ta(t,e){return q(t.getHours(),e,2)}function ea(t,e){return q(t.getHours()%12||12,e,2)}function na(t,e){return q(1+Tt.count(kt(t),t),e,3)}function Tn(t,e){return q(t.getMilliseconds(),e,3)}function ra(t,e){return Tn(t,e)+"000"}function aa(t,e){return q(t.getMonth()+1,e,2)}function ia(t,e){return q(t.getMinutes(),e,2)}function sa(t,e){return q(t.getSeconds(),e,2)}function oa(t){var e=t.getDay();return e===0?7:e}function ca(t,e){return q(Vt.count(kt(t)-1,t),e,2)}function xn(t){var e=t.getDay();return e>=4||e===0?xt(t):xt.ceil(t)}function ua(t,e){return t=xn(t),q(xt.count(kt(t),t)+(kt(t).getDay()===4),e,2)}function la(t){return t.getDay()}function fa(t,e){return q(Ot.count(kt(t)-1,t),e,2)}function ha(t,e){return q(t.getFullYear()%100,e,2)}function da(t,e){return t=xn(t),q(t.getFullYear()%100,e,2)}function ma(t,e){return q(t.getFullYear()%1e4,e,4)}function ga(t,e){var n=t.getDay();return t=n>=4||n===0?xt(t):xt.ceil(t),q(t.getFullYear()%1e4,e,4)}function ya(t){var e=t.getTimezoneOffset();return(e>0?"-":(e*=-1,"+"))+q(e/60|0,"0",2)+q(e%60,"0",2)}function Ge(t,e){return q(t.getUTCDate(),e,2)}function ka(t,e){return q(t.getUTCHours(),e,2)}function pa(t,e){return q(t.getUTCHours()%12||12,e,2)}function va(t,e){return q(1+De.count(bt(t),t),e,3)}function bn(t,e){return q(t.getUTCMilliseconds(),e,3)}function Ta(t,e){return bn(t,e)+"000"}function xa(t,e){return q(t.getUTCMonth()+1,e,2)}function ba(t,e){return q(t.getUTCMinutes(),e,2)}function wa(t,e){return q(t.getUTCSeconds(),e,2)}function Ca(t){var e=t.getUTCDay();return e===0?7:e}function Da(t,e){return q(vn.count(bt(t)-1,t),e,2)}function wn(t){var e=t.getUTCDay();return e>=4||e===0?Ut(t):Ut.ceil(t)}function Ma(t,e){return t=wn(t),q(Ut.count(bt(t),t)+(bt(t).getUTCDay()===4),e,2)}function _a(t){return t.getUTCDay()}function Sa(t,e){return q(ee.count(bt(t)-1,t),e,2)}function Fa(t,e){return q(t.getUTCFullYear()%100,e,2)}function Ya(t,e){return t=wn(t),q(t.getUTCFullYear()%100,e,2)}function Ua(t,e){return q(t.getUTCFullYear()%1e4,e,4)}function Ea(t,e){var n=t.getUTCDay();return t=n>=4||n===0?Ut(t):Ut.ceil(t),q(t.getUTCFullYear()%1e4,e,4)}function La(){return"+0000"}function Qe(){return"%"}function $e(t){return+t}function je(t){return Math.floor(+t/1e3)}var Mt,ne;Aa({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]});function Aa(t){return Mt=Ar(t),ne=Mt.format,Mt.parse,Mt.utcFormat,Mt.utcParse,Mt}function Ia(t){return new Date(t)}function Wa(t){return t instanceof Date?+t:+new Date(+t)}function Cn(t,e,n,r,a,i,s,p,M,T){var g=Xn(),U=g.invert,C=g.domain,x=T(".%L"),X=T(":%S"),H=T("%I:%M"),D=T("%I %p"),I=T("%a %d"),V=T("%b %d"),W=T("%B"),Z=T("%Y");function $(w){return(M(w)<w?x:p(w)<w?X:s(w)<w?H:i(w)<w?D:r(w)<w?a(w)<w?I:V:n(w)<w?W:Z)(w)}return g.invert=function(w){return new Date(U(w))},g.domain=function(w){return arguments.length?C(Array.from(w,Wa)):C().map(Ia)},g.ticks=function(w){var O=C();return t(O[0],O[O.length-1],w??10)},g.tickFormat=function(w,O){return O==null?$:T(O)},g.nice=function(w){var O=C();return(!w||typeof w.range!="function")&&(w=e(O[0],O[O.length-1],w??10)),w?C(br(O,w)):g},g.copy=function(){return Gn(g,Cn(t,e,n,r,a,i,s,p,M,T))},g}function Ha(){return $n.apply(Cn(Er,Lr,kt,Nt,Vt,Tt,Ht,Wt,vt,ne).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}var $t={exports:{}},Oa=$t.exports,Je;function Na(){return Je||(Je=1,function(t,e){(function(n,r){t.exports=r()})(Oa,function(){var n="day";return function(r,a,i){var s=function(T){return T.add(4-T.isoWeekday(),n)},p=a.prototype;p.isoWeekYear=function(){return s(this).year()},p.isoWeek=function(T){if(!this.$utils().u(T))return this.add(7*(T-this.isoWeek()),n);var g,U,C,x,X=s(this),H=(g=this.isoWeekYear(),U=this.$u,C=(U?i.utc:i)().year(g).startOf("year"),x=4-C.isoWeekday(),C.isoWeekday()>4&&(x+=7),C.add(x,n));return X.diff(H,"week")+1},p.isoWeekday=function(T){return this.$utils().u(T)?this.day()||7:this.day(this.day()%7?T:T-7)};var M=p.startOf;p.startOf=function(T,g){var U=this.$utils(),C=!!U.u(g)||g;return U.p(T)==="isoweek"?C?this.date(this.date()-(this.isoWeekday()-1)).startOf("day"):this.date(this.date()-1-(this.isoWeekday()-1)+7).endOf("day"):M.bind(this)(T,g)}}})}($t)),$t.exports}var Va=Na();const Pa=we(Va);var ve=function(){var t=d(function(Y,l,h,y){for(h=h||{},y=Y.length;y--;h[Y[y]]=l);return h},"o"),e=[6,8,10,12,13,14,15,16,17,18,20,21,22,23,24,25,26,27,28,29,30,31,33,35,36,38,40],n=[1,26],r=[1,27],a=[1,28],i=[1,29],s=[1,30],p=[1,31],M=[1,32],T=[1,33],g=[1,34],U=[1,9],C=[1,10],x=[1,11],X=[1,12],H=[1,13],D=[1,14],I=[1,15],V=[1,16],W=[1,19],Z=[1,20],$=[1,21],w=[1,22],O=[1,23],b=[1,25],F=[1,35],S={trace:d(function(){},"trace"),yy:{},symbols_:{error:2,start:3,gantt:4,document:5,EOF:6,line:7,SPACE:8,statement:9,NL:10,weekday:11,weekday_monday:12,weekday_tuesday:13,weekday_wednesday:14,weekday_thursday:15,weekday_friday:16,weekday_saturday:17,weekday_sunday:18,weekend:19,weekend_friday:20,weekend_saturday:21,dateFormat:22,inclusiveEndDates:23,topAxis:24,axisFormat:25,tickInterval:26,excludes:27,includes:28,todayMarker:29,title:30,acc_title:31,acc_title_value:32,acc_descr:33,acc_descr_value:34,acc_descr_multiline_value:35,section:36,clickStatement:37,taskTxt:38,taskData:39,click:40,callbackname:41,callbackargs:42,href:43,clickStatementDebug:44,$accept:0,$end:1},terminals_:{2:"error",4:"gantt",6:"EOF",8:"SPACE",10:"NL",12:"weekday_monday",13:"weekday_tuesday",14:"weekday_wednesday",15:"weekday_thursday",16:"weekday_friday",17:"weekday_saturday",18:"weekday_sunday",20:"weekend_friday",21:"weekend_saturday",22:"dateFormat",23:"inclusiveEndDates",24:"topAxis",25:"axisFormat",26:"tickInterval",27:"excludes",28:"includes",29:"todayMarker",30:"title",31:"acc_title",32:"acc_title_value",33:"acc_descr",34:"acc_descr_value",35:"acc_descr_multiline_value",36:"section",38:"taskTxt",39:"taskData",40:"click",41:"callbackname",42:"callbackargs",43:"href"},productions_:[0,[3,3],[5,0],[5,2],[7,2],[7,1],[7,1],[7,1],[11,1],[11,1],[11,1],[11,1],[11,1],[11,1],[11,1],[19,1],[19,1],[9,1],[9,1],[9,1],[9,1],[9,1],[9,1],[9,1],[9,1],[9,1],[9,1],[9,1],[9,2],[9,2],[9,1],[9,1],[9,1],[9,2],[37,2],[37,3],[37,3],[37,4],[37,3],[37,4],[37,2],[44,2],[44,3],[44,3],[44,4],[44,3],[44,4],[44,2]],performAction:d(function(l,h,y,m,E,c,u){var o=c.length-1;switch(E){case 1:return c[o-1];case 2:this.$=[];break;case 3:c[o-1].push(c[o]),this.$=c[o-1];break;case 4:case 5:this.$=c[o];break;case 6:case 7:this.$=[];break;case 8:m.setWeekday("monday");break;case 9:m.setWeekday("tuesday");break;case 10:m.setWeekday("wednesday");break;case 11:m.setWeekday("thursday");break;case 12:m.setWeekday("friday");break;case 13:m.setWeekday("saturday");break;case 14:m.setWeekday("sunday");break;case 15:m.setWeekend("friday");break;case 16:m.setWeekend("saturday");break;case 17:m.setDateFormat(c[o].substr(11)),this.$=c[o].substr(11);break;case 18:m.enableInclusiveEndDates(),this.$=c[o].substr(18);break;case 19:m.TopAxis(),this.$=c[o].substr(8);break;case 20:m.setAxisFormat(c[o].substr(11)),this.$=c[o].substr(11);break;case 21:m.setTickInterval(c[o].substr(13)),this.$=c[o].substr(13);break;case 22:m.setExcludes(c[o].substr(9)),this.$=c[o].substr(9);break;case 23:m.setIncludes(c[o].substr(9)),this.$=c[o].substr(9);break;case 24:m.setTodayMarker(c[o].substr(12)),this.$=c[o].substr(12);break;case 27:m.setDiagramTitle(c[o].substr(6)),this.$=c[o].substr(6);break;case 28:this.$=c[o].trim(),m.setAccTitle(this.$);break;case 29:case 30:this.$=c[o].trim(),m.setAccDescription(this.$);break;case 31:m.addSection(c[o].substr(8)),this.$=c[o].substr(8);break;case 33:m.addTask(c[o-1],c[o]),this.$="task";break;case 34:this.$=c[o-1],m.setClickEvent(c[o-1],c[o],null);break;case 35:this.$=c[o-2],m.setClickEvent(c[o-2],c[o-1],c[o]);break;case 36:this.$=c[o-2],m.setClickEvent(c[o-2],c[o-1],null),m.setLink(c[o-2],c[o]);break;case 37:this.$=c[o-3],m.setClickEvent(c[o-3],c[o-2],c[o-1]),m.setLink(c[o-3],c[o]);break;case 38:this.$=c[o-2],m.setClickEvent(c[o-2],c[o],null),m.setLink(c[o-2],c[o-1]);break;case 39:this.$=c[o-3],m.setClickEvent(c[o-3],c[o-1],c[o]),m.setLink(c[o-3],c[o-2]);break;case 40:this.$=c[o-1],m.setLink(c[o-1],c[o]);break;case 41:case 47:this.$=c[o-1]+" "+c[o];break;case 42:case 43:case 45:this.$=c[o-2]+" "+c[o-1]+" "+c[o];break;case 44:case 46:this.$=c[o-3]+" "+c[o-2]+" "+c[o-1]+" "+c[o];break}},"anonymous"),table:[{3:1,4:[1,2]},{1:[3]},t(e,[2,2],{5:3}),{6:[1,4],7:5,8:[1,6],9:7,10:[1,8],11:17,12:n,13:r,14:a,15:i,16:s,17:p,18:M,19:18,20:T,21:g,22:U,23:C,24:x,25:X,26:H,27:D,28:I,29:V,30:W,31:Z,33:$,35:w,36:O,37:24,38:b,40:F},t(e,[2,7],{1:[2,1]}),t(e,[2,3]),{9:36,11:17,12:n,13:r,14:a,15:i,16:s,17:p,18:M,19:18,20:T,21:g,22:U,23:C,24:x,25:X,26:H,27:D,28:I,29:V,30:W,31:Z,33:$,35:w,36:O,37:24,38:b,40:F},t(e,[2,5]),t(e,[2,6]),t(e,[2,17]),t(e,[2,18]),t(e,[2,19]),t(e,[2,20]),t(e,[2,21]),t(e,[2,22]),t(e,[2,23]),t(e,[2,24]),t(e,[2,25]),t(e,[2,26]),t(e,[2,27]),{32:[1,37]},{34:[1,38]},t(e,[2,30]),t(e,[2,31]),t(e,[2,32]),{39:[1,39]},t(e,[2,8]),t(e,[2,9]),t(e,[2,10]),t(e,[2,11]),t(e,[2,12]),t(e,[2,13]),t(e,[2,14]),t(e,[2,15]),t(e,[2,16]),{41:[1,40],43:[1,41]},t(e,[2,4]),t(e,[2,28]),t(e,[2,29]),t(e,[2,33]),t(e,[2,34],{42:[1,42],43:[1,43]}),t(e,[2,40],{41:[1,44]}),t(e,[2,35],{43:[1,45]}),t(e,[2,36]),t(e,[2,38],{42:[1,46]}),t(e,[2,37]),t(e,[2,39])],defaultActions:{},parseError:d(function(l,h){if(h.recoverable)this.trace(l);else{var y=new Error(l);throw y.hash=h,y}},"parseError"),parse:d(function(l){var h=this,y=[0],m=[],E=[null],c=[],u=this.table,o="",R=0,P=0,z=2,K=1,G=c.slice.call(arguments,1),j=Object.create(this.lexer),it={yy:{}};for(var v in this.yy)Object.prototype.hasOwnProperty.call(this.yy,v)&&(it.yy[v]=this.yy[v]);j.setInput(l,it.yy),it.yy.lexer=j,it.yy.parser=this,typeof j.yylloc>"u"&&(j.yylloc={});var A=j.yylloc;c.push(A);var N=j.options&&j.options.ranges;typeof it.yy.parseError=="function"?this.parseError=it.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError;function f(ot){y.length=y.length-2*ot,E.length=E.length-ot,c.length=c.length-ot}d(f,"popStack");function J(){var ot;return ot=m.pop()||j.lex()||K,typeof ot!="number"&&(ot instanceof Array&&(m=ot,ot=m.pop()),ot=h.symbols_[ot]||ot),ot}d(J,"lex");for(var L,Q,B,rt,st={},pt,ut,We,qt;;){if(Q=y[y.length-1],this.defaultActions[Q]?B=this.defaultActions[Q]:((L===null||typeof L>"u")&&(L=J()),B=u[Q]&&u[Q][L]),typeof B>"u"||!B.length||!B[0]){var ie="";qt=[];for(pt in u[Q])this.terminals_[pt]&&pt>z&&qt.push("'"+this.terminals_[pt]+"'");j.showPosition?ie="Parse error on line "+(R+1)+`:
`+j.showPosition()+`
Expecting `+qt.join(", ")+", got '"+(this.terminals_[L]||L)+"'":ie="Parse error on line "+(R+1)+": Unexpected "+(L==K?"end of input":"'"+(this.terminals_[L]||L)+"'"),this.parseError(ie,{text:j.match,token:this.terminals_[L]||L,line:j.yylineno,loc:A,expected:qt})}if(B[0]instanceof Array&&B.length>1)throw new Error("Parse Error: multiple actions possible at state: "+Q+", token: "+L);switch(B[0]){case 1:y.push(L),E.push(j.yytext),c.push(j.yylloc),y.push(B[1]),L=null,P=j.yyleng,o=j.yytext,R=j.yylineno,A=j.yylloc;break;case 2:if(ut=this.productions_[B[1]][1],st.$=E[E.length-ut],st._$={first_line:c[c.length-(ut||1)].first_line,last_line:c[c.length-1].last_line,first_column:c[c.length-(ut||1)].first_column,last_column:c[c.length-1].last_column},N&&(st._$.range=[c[c.length-(ut||1)].range[0],c[c.length-1].range[1]]),rt=this.performAction.apply(st,[o,P,R,it.yy,B[1],E,c].concat(G)),typeof rt<"u")return rt;ut&&(y=y.slice(0,-1*ut*2),E=E.slice(0,-1*ut),c=c.slice(0,-1*ut)),y.push(this.productions_[B[1]][0]),E.push(st.$),c.push(st._$),We=u[y[y.length-2]][y[y.length-1]],y.push(We);break;case 3:return!0}}return!0},"parse")},_=function(){var Y={EOF:1,parseError:d(function(h,y){if(this.yy.parser)this.yy.parser.parseError(h,y);else throw new Error(h)},"parseError"),setInput:d(function(l,h){return this.yy=h||this.yy||{},this._input=l,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},"setInput"),input:d(function(){var l=this._input[0];this.yytext+=l,this.yyleng++,this.offset++,this.match+=l,this.matched+=l;var h=l.match(/(?:\r\n?|\n).*/g);return h?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),l},"input"),unput:d(function(l){var h=l.length,y=l.split(/(?:\r\n?|\n)/g);this._input=l+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-h),this.offset-=h;var m=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),y.length-1&&(this.yylineno-=y.length-1);var E=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:y?(y.length===m.length?this.yylloc.first_column:0)+m[m.length-y.length].length-y[0].length:this.yylloc.first_column-h},this.options.ranges&&(this.yylloc.range=[E[0],E[0]+this.yyleng-h]),this.yyleng=this.yytext.length,this},"unput"),more:d(function(){return this._more=!0,this},"more"),reject:d(function(){if(this.options.backtrack_lexer)this._backtrack=!0;else return this.parseError("Lexical error on line "+(this.yylineno+1)+`. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).
`+this.showPosition(),{text:"",token:null,line:this.yylineno});return this},"reject"),less:d(function(l){this.unput(this.match.slice(l))},"less"),pastInput:d(function(){var l=this.matched.substr(0,this.matched.length-this.match.length);return(l.length>20?"...":"")+l.substr(-20).replace(/\n/g,"")},"pastInput"),upcomingInput:d(function(){var l=this.match;return l.length<20&&(l+=this._input.substr(0,20-l.length)),(l.substr(0,20)+(l.length>20?"...":"")).replace(/\n/g,"")},"upcomingInput"),showPosition:d(function(){var l=this.pastInput(),h=new Array(l.length+1).join("-");return l+this.upcomingInput()+`
`+h+"^"},"showPosition"),test_match:d(function(l,h){var y,m,E;if(this.options.backtrack_lexer&&(E={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(E.yylloc.range=this.yylloc.range.slice(0))),m=l[0].match(/(?:\r\n?|\n).*/g),m&&(this.yylineno+=m.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:m?m[m.length-1].length-m[m.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+l[0].length},this.yytext+=l[0],this.match+=l[0],this.matches=l,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(l[0].length),this.matched+=l[0],y=this.performAction.call(this,this.yy,this,h,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),y)return y;if(this._backtrack){for(var c in E)this[c]=E[c];return!1}return!1},"test_match"),next:d(function(){if(this.done)return this.EOF;this._input||(this.done=!0);var l,h,y,m;this._more||(this.yytext="",this.match="");for(var E=this._currentRules(),c=0;c<E.length;c++)if(y=this._input.match(this.rules[E[c]]),y&&(!h||y[0].length>h[0].length)){if(h=y,m=c,this.options.backtrack_lexer){if(l=this.test_match(y,E[c]),l!==!1)return l;if(this._backtrack){h=!1;continue}else return!1}else if(!this.options.flex)break}return h?(l=this.test_match(h,E[m]),l!==!1?l:!1):this._input===""?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+`. Unrecognized text.
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},"next"),lex:d(function(){var h=this.next();return h||this.lex()},"lex"),begin:d(function(h){this.conditionStack.push(h)},"begin"),popState:d(function(){var h=this.conditionStack.length-1;return h>0?this.conditionStack.pop():this.conditionStack[0]},"popState"),_currentRules:d(function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules},"_currentRules"),topState:d(function(h){return h=this.conditionStack.length-1-Math.abs(h||0),h>=0?this.conditionStack[h]:"INITIAL"},"topState"),pushState:d(function(h){this.begin(h)},"pushState"),stateStackSize:d(function(){return this.conditionStack.length},"stateStackSize"),options:{"case-insensitive":!0},performAction:d(function(h,y,m,E){switch(m){case 0:return this.begin("open_directive"),"open_directive";case 1:return this.begin("acc_title"),31;case 2:return this.popState(),"acc_title_value";case 3:return this.begin("acc_descr"),33;case 4:return this.popState(),"acc_descr_value";case 5:this.begin("acc_descr_multiline");break;case 6:this.popState();break;case 7:return"acc_descr_multiline_value";case 8:break;case 9:break;case 10:break;case 11:return 10;case 12:break;case 13:break;case 14:this.begin("href");break;case 15:this.popState();break;case 16:return 43;case 17:this.begin("callbackname");break;case 18:this.popState();break;case 19:this.popState(),this.begin("callbackargs");break;case 20:return 41;case 21:this.popState();break;case 22:return 42;case 23:this.begin("click");break;case 24:this.popState();break;case 25:return 40;case 26:return 4;case 27:return 22;case 28:return 23;case 29:return 24;case 30:return 25;case 31:return 26;case 32:return 28;case 33:return 27;case 34:return 29;case 35:return 12;case 36:return 13;case 37:return 14;case 38:return 15;case 39:return 16;case 40:return 17;case 41:return 18;case 42:return 20;case 43:return 21;case 44:return"date";case 45:return 30;case 46:return"accDescription";case 47:return 36;case 48:return 38;case 49:return 39;case 50:return":";case 51:return 6;case 52:return"INVALID"}},"anonymous"),rules:[/^(?:%%\{)/i,/^(?:accTitle\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*\{\s*)/i,/^(?:[\}])/i,/^(?:[^\}]*)/i,/^(?:%%(?!\{)*[^\n]*)/i,/^(?:[^\}]%%*[^\n]*)/i,/^(?:%%*[^\n]*[\n]*)/i,/^(?:[\n]+)/i,/^(?:\s+)/i,/^(?:%[^\n]*)/i,/^(?:href[\s]+["])/i,/^(?:["])/i,/^(?:[^"]*)/i,/^(?:call[\s]+)/i,/^(?:\([\s]*\))/i,/^(?:\()/i,/^(?:[^(]*)/i,/^(?:\))/i,/^(?:[^)]*)/i,/^(?:click[\s]+)/i,/^(?:[\s\n])/i,/^(?:[^\s\n]*)/i,/^(?:gantt\b)/i,/^(?:dateFormat\s[^#\n;]+)/i,/^(?:inclusiveEndDates\b)/i,/^(?:topAxis\b)/i,/^(?:axisFormat\s[^#\n;]+)/i,/^(?:tickInterval\s[^#\n;]+)/i,/^(?:includes\s[^#\n;]+)/i,/^(?:excludes\s[^#\n;]+)/i,/^(?:todayMarker\s[^\n;]+)/i,/^(?:weekday\s+monday\b)/i,/^(?:weekday\s+tuesday\b)/i,/^(?:weekday\s+wednesday\b)/i,/^(?:weekday\s+thursday\b)/i,/^(?:weekday\s+friday\b)/i,/^(?:weekday\s+saturday\b)/i,/^(?:weekday\s+sunday\b)/i,/^(?:weekend\s+friday\b)/i,/^(?:weekend\s+saturday\b)/i,/^(?:\d\d\d\d-\d\d-\d\d\b)/i,/^(?:title\s[^\n]+)/i,/^(?:accDescription\s[^#\n;]+)/i,/^(?:section\s[^\n]+)/i,/^(?:[^:\n]+)/i,/^(?::[^#\n;]+)/i,/^(?::)/i,/^(?:$)/i,/^(?:.)/i],conditions:{acc_descr_multiline:{rules:[6,7],inclusive:!1},acc_descr:{rules:[4],inclusive:!1},acc_title:{rules:[2],inclusive:!1},callbackargs:{rules:[21,22],inclusive:!1},callbackname:{rules:[18,19,20],inclusive:!1},href:{rules:[15,16],inclusive:!1},click:{rules:[24,25],inclusive:!1},INITIAL:{rules:[0,1,3,5,8,9,10,11,12,13,14,17,23,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52],inclusive:!0}}};return Y}();S.lexer=_;function k(){this.yy={}}return d(k,"Parser"),k.prototype=S,S.Parser=k,new k}();ve.parser=ve;var za=ve;at.extend(Pa);at.extend(ar);at.extend(tr);var Ke={friday:5,saturday:6},lt="",Me="",_e=void 0,Se="",Pt=[],zt=[],Fe=new Map,Ye=[],re=[],Et="",Ue="",Dn=["active","done","crit","milestone","vert"],Ee=[],Rt=!1,Le=!1,Ae="sunday",ae="saturday",Te=0,Ra=d(function(){Ye=[],re=[],Et="",Ee=[],jt=0,be=void 0,Jt=void 0,tt=[],lt="",Me="",Ue="",_e=void 0,Se="",Pt=[],zt=[],Rt=!1,Le=!1,Te=0,Fe=new Map,qn(),Ae="sunday",ae="saturday"},"clear"),qa=d(function(t){Me=t},"setAxisFormat"),Za=d(function(){return Me},"getAxisFormat"),Ba=d(function(t){_e=t},"setTickInterval"),Xa=d(function(){return _e},"getTickInterval"),Ga=d(function(t){Se=t},"setTodayMarker"),Qa=d(function(){return Se},"getTodayMarker"),$a=d(function(t){lt=t},"setDateFormat"),ja=d(function(){Rt=!0},"enableInclusiveEndDates"),Ja=d(function(){return Rt},"endDatesAreInclusive"),Ka=d(function(){Le=!0},"enableTopAxis"),ti=d(function(){return Le},"topAxisEnabled"),ei=d(function(t){Ue=t},"setDisplayMode"),ni=d(function(){return Ue},"getDisplayMode"),ri=d(function(){return lt},"getDateFormat"),ai=d(function(t){Pt=t.toLowerCase().split(/[\s,]+/)},"setIncludes"),ii=d(function(){return Pt},"getIncludes"),si=d(function(t){zt=t.toLowerCase().split(/[\s,]+/)},"setExcludes"),oi=d(function(){return zt},"getExcludes"),ci=d(function(){return Fe},"getLinks"),ui=d(function(t){Et=t,Ye.push(t)},"addSection"),li=d(function(){return Ye},"getSections"),fi=d(function(){let t=tn();const e=10;let n=0;for(;!t&&n<e;)t=tn(),n++;return re=tt,re},"getTasks"),Mn=d(function(t,e,n,r){return r.includes(t.format(e.trim()))?!1:n.includes("weekends")&&(t.isoWeekday()===Ke[ae]||t.isoWeekday()===Ke[ae]+1)||n.includes(t.format("dddd").toLowerCase())?!0:n.includes(t.format(e.trim()))},"isInvalidDate"),hi=d(function(t){Ae=t},"setWeekday"),di=d(function(){return Ae},"getWeekday"),mi=d(function(t){ae=t},"setWeekend"),_n=d(function(t,e,n,r){if(!n.length||t.manualEndTime)return;let a;t.startTime instanceof Date?a=at(t.startTime):a=at(t.startTime,e,!0),a=a.add(1,"d");let i;t.endTime instanceof Date?i=at(t.endTime):i=at(t.endTime,e,!0);const[s,p]=gi(a,i,e,n,r);t.endTime=s.toDate(),t.renderEndTime=p},"checkTaskDates"),gi=d(function(t,e,n,r,a){let i=!1,s=null;for(;t<=e;)i||(s=e.toDate()),i=Mn(t,n,r,a),i&&(e=e.add(1,"d")),t=t.add(1,"d");return[e,s]},"fixTaskDates"),xe=d(function(t,e,n){n=n.trim();const a=/^after\s+(?<ids>[\d\w- ]+)/.exec(n);if(a!==null){let s=null;for(const M of a.groups.ids.split(" ")){let T=Dt(M);T!==void 0&&(!s||T.endTime>s.endTime)&&(s=T)}if(s)return s.endTime;const p=new Date;return p.setHours(0,0,0,0),p}let i=at(n,e.trim(),!0);if(i.isValid())return i.toDate();{Kt.debug("Invalid date:"+n),Kt.debug("With date format:"+e.trim());const s=new Date(n);if(s===void 0||isNaN(s.getTime())||s.getFullYear()<-1e4||s.getFullYear()>1e4)throw new Error("Invalid date:"+n);return s}},"getStartDate"),Sn=d(function(t){const e=/^(\d+(?:\.\d+)?)([Mdhmswy]|ms)$/.exec(t.trim());return e!==null?[Number.parseFloat(e[1]),e[2]]:[NaN,"ms"]},"parseDuration"),Fn=d(function(t,e,n,r=!1){n=n.trim();const i=/^until\s+(?<ids>[\d\w- ]+)/.exec(n);if(i!==null){let g=null;for(const C of i.groups.ids.split(" ")){let x=Dt(C);x!==void 0&&(!g||x.startTime<g.startTime)&&(g=x)}if(g)return g.startTime;const U=new Date;return U.setHours(0,0,0,0),U}let s=at(n,e.trim(),!0);if(s.isValid())return r&&(s=s.add(1,"d")),s.toDate();let p=at(t);const[M,T]=Sn(n);if(!Number.isNaN(M)){const g=p.add(M,T);g.isValid()&&(p=g)}return p.toDate()},"getEndDate"),jt=0,Ft=d(function(t){return t===void 0?(jt=jt+1,"task"+jt):t},"parseId"),yi=d(function(t,e){let n;e.substr(0,1)===":"?n=e.substr(1,e.length):n=e;const r=n.split(","),a={};Ie(r,a,Dn);for(let s=0;s<r.length;s++)r[s]=r[s].trim();let i="";switch(r.length){case 1:a.id=Ft(),a.startTime=t.endTime,i=r[0];break;case 2:a.id=Ft(),a.startTime=xe(void 0,lt,r[0]),i=r[1];break;case 3:a.id=Ft(r[0]),a.startTime=xe(void 0,lt,r[1]),i=r[2];break}return i&&(a.endTime=Fn(a.startTime,lt,i,Rt),a.manualEndTime=at(i,"YYYY-MM-DD",!0).isValid(),_n(a,lt,zt,Pt)),a},"compileData"),ki=d(function(t,e){let n;e.substr(0,1)===":"?n=e.substr(1,e.length):n=e;const r=n.split(","),a={};Ie(r,a,Dn);for(let i=0;i<r.length;i++)r[i]=r[i].trim();switch(r.length){case 1:a.id=Ft(),a.startTime={type:"prevTaskEnd",id:t},a.endTime={data:r[0]};break;case 2:a.id=Ft(),a.startTime={type:"getStartDate",startData:r[0]},a.endTime={data:r[1]};break;case 3:a.id=Ft(r[0]),a.startTime={type:"getStartDate",startData:r[1]},a.endTime={data:r[2]};break}return a},"parseData"),be,Jt,tt=[],Yn={},pi=d(function(t,e){const n={section:Et,type:Et,processed:!1,manualEndTime:!1,renderEndTime:null,raw:{data:e},task:t,classes:[]},r=ki(Jt,e);n.raw.startTime=r.startTime,n.raw.endTime=r.endTime,n.id=r.id,n.prevTaskId=Jt,n.active=r.active,n.done=r.done,n.crit=r.crit,n.milestone=r.milestone,n.vert=r.vert,n.order=Te,Te++;const a=tt.push(n);Jt=n.id,Yn[n.id]=a-1},"addTask"),Dt=d(function(t){const e=Yn[t];return tt[e]},"findTaskById"),vi=d(function(t,e){const n={section:Et,type:Et,description:t,task:t,classes:[]},r=yi(be,e);n.startTime=r.startTime,n.endTime=r.endTime,n.id=r.id,n.active=r.active,n.done=r.done,n.crit=r.crit,n.milestone=r.milestone,n.vert=r.vert,be=n,re.push(n)},"addTaskOrg"),tn=d(function(){const t=d(function(n){const r=tt[n];let a="";switch(tt[n].raw.startTime.type){case"prevTaskEnd":{const i=Dt(r.prevTaskId);r.startTime=i.endTime;break}case"getStartDate":a=xe(void 0,lt,tt[n].raw.startTime.startData),a&&(tt[n].startTime=a);break}return tt[n].startTime&&(tt[n].endTime=Fn(tt[n].startTime,lt,tt[n].raw.endTime.data,Rt),tt[n].endTime&&(tt[n].processed=!0,tt[n].manualEndTime=at(tt[n].raw.endTime.data,"YYYY-MM-DD",!0).isValid(),_n(tt[n],lt,zt,Pt))),tt[n].processed},"compileTask");let e=!0;for(const[n,r]of tt.entries())t(n),e=e&&r.processed;return e},"compileTasks"),Ti=d(function(t,e){let n=e;_t().securityLevel!=="loose"&&(n=Rn.sanitizeUrl(e)),t.split(",").forEach(function(r){Dt(r)!==void 0&&(En(r,()=>{window.open(n,"_self")}),Fe.set(r,n))}),Un(t,"clickable")},"setLink"),Un=d(function(t,e){t.split(",").forEach(function(n){let r=Dt(n);r!==void 0&&r.classes.push(e)})},"setClass"),xi=d(function(t,e,n){if(_t().securityLevel!=="loose"||e===void 0)return;let r=[];if(typeof n=="string"){r=n.split(/,(?=(?:(?:[^"]*"){2})*[^"]*$)/);for(let i=0;i<r.length;i++){let s=r[i].trim();s.startsWith('"')&&s.endsWith('"')&&(s=s.substr(1,s.length-2)),r[i]=s}}r.length===0&&r.push(t),Dt(t)!==void 0&&En(t,()=>{Zn.runFunc(e,...r)})},"setClickFun"),En=d(function(t,e){Ee.push(function(){const n=document.querySelector(`[id="${t}"]`);n!==null&&n.addEventListener("click",function(){e()})},function(){const n=document.querySelector(`[id="${t}-text"]`);n!==null&&n.addEventListener("click",function(){e()})})},"pushFun"),bi=d(function(t,e,n){t.split(",").forEach(function(r){xi(r,e,n)}),Un(t,"clickable")},"setClickEvent"),wi=d(function(t){Ee.forEach(function(e){e(t)})},"bindFunctions"),Ci={getConfig:d(()=>_t().gantt,"getConfig"),clear:Ra,setDateFormat:$a,getDateFormat:ri,enableInclusiveEndDates:ja,endDatesAreInclusive:Ja,enableTopAxis:Ka,topAxisEnabled:ti,setAxisFormat:qa,getAxisFormat:Za,setTickInterval:Ba,getTickInterval:Xa,setTodayMarker:Ga,getTodayMarker:Qa,setAccTitle:Vn,getAccTitle:Nn,setDiagramTitle:On,getDiagramTitle:Hn,setDisplayMode:ei,getDisplayMode:ni,setAccDescription:Wn,getAccDescription:In,addSection:ui,getSections:li,getTasks:fi,addTask:pi,findTaskById:Dt,addTaskOrg:vi,setIncludes:ai,getIncludes:ii,setExcludes:si,getExcludes:oi,setClickEvent:bi,setLink:Ti,getLinks:ci,bindFunctions:wi,parseDuration:Sn,isInvalidDate:Mn,setWeekday:hi,getWeekday:di,setWeekend:mi};function Ie(t,e,n){let r=!0;for(;r;)r=!1,n.forEach(function(a){const i="^\\s*"+a+"\\s*$",s=new RegExp(i);t[0].match(s)&&(e[a]=!0,t.shift(1),r=!0)})}d(Ie,"getTaskTags");var Di=d(function(){Kt.debug("Something is calling, setConf, remove the call")},"setConf"),en={monday:Ot,tuesday:gn,wednesday:yn,thursday:xt,friday:kn,saturday:pn,sunday:Vt},Mi=d((t,e)=>{let n=[...t].map(()=>-1/0),r=[...t].sort((i,s)=>i.startTime-s.startTime||i.order-s.order),a=0;for(const i of r)for(let s=0;s<n.length;s++)if(i.startTime>=n[s]){n[s]=i.endTime,i.order=s+e,s>a&&(a=s);break}return a},"getMaxIntersections"),ht,_i=d(function(t,e,n,r){const a=_t().gantt,i=_t().securityLevel;let s;i==="sandbox"&&(s=Zt("#i"+e));const p=i==="sandbox"?Zt(s.nodes()[0].contentDocument.body):Zt("body"),M=i==="sandbox"?s.nodes()[0].contentDocument:document,T=M.getElementById(e);ht=T.parentElement.offsetWidth,ht===void 0&&(ht=1200),a.useWidth!==void 0&&(ht=a.useWidth);const g=r.db.getTasks();let U=[];for(const b of g)U.push(b.type);U=O(U);const C={};let x=2*a.topPadding;if(r.db.getDisplayMode()==="compact"||a.displayMode==="compact"){const b={};for(const S of g)b[S.section]===void 0?b[S.section]=[S]:b[S.section].push(S);let F=0;for(const S of Object.keys(b)){const _=Mi(b[S],F)+1;F+=_,x+=_*(a.barHeight+a.barGap),C[S]=_}}else{x+=g.length*(a.barHeight+a.barGap);for(const b of U)C[b]=g.filter(F=>F.type===b).length}T.setAttribute("viewBox","0 0 "+ht+" "+x);const X=p.select(`[id="${e}"]`),H=Ha().domain([sr(g,function(b){return b.startTime}),ir(g,function(b){return b.endTime})]).rangeRound([0,ht-a.leftPadding-a.rightPadding]);function D(b,F){const S=b.startTime,_=F.startTime;let k=0;return S>_?k=1:S<_&&(k=-1),k}d(D,"taskCompare"),g.sort(D),I(g,ht,x),Pn(X,x,ht,a.useMaxWidth),X.append("text").text(r.db.getDiagramTitle()).attr("x",ht/2).attr("y",a.titleTopMargin).attr("class","titleText");function I(b,F,S){const _=a.barHeight,k=_+a.barGap,Y=a.topPadding,l=a.leftPadding,h=Qn().domain([0,U.length]).range(["#00B9FA","#F95002"]).interpolate(xr);W(k,Y,l,F,S,b,r.db.getExcludes(),r.db.getIncludes()),Z(l,Y,F,S),V(b,k,Y,l,_,h,F),$(k,Y),w(l,Y,F,S)}d(I,"makeGantt");function V(b,F,S,_,k,Y,l){b.sort((u,o)=>u.vert===o.vert?0:u.vert?1:-1);const y=[...new Set(b.map(u=>u.order))].map(u=>b.find(o=>o.order===u));X.append("g").selectAll("rect").data(y).enter().append("rect").attr("x",0).attr("y",function(u,o){return o=u.order,o*F+S-2}).attr("width",function(){return l-a.rightPadding/2}).attr("height",F).attr("class",function(u){for(const[o,R]of U.entries())if(u.type===R)return"section section"+o%a.numberSectionStyles;return"section section0"}).enter();const m=X.append("g").selectAll("rect").data(b).enter(),E=r.db.getLinks();if(m.append("rect").attr("id",function(u){return u.id}).attr("rx",3).attr("ry",3).attr("x",function(u){return u.milestone?H(u.startTime)+_+.5*(H(u.endTime)-H(u.startTime))-.5*k:H(u.startTime)+_}).attr("y",function(u,o){return o=u.order,u.vert?a.gridLineStartPadding:o*F+S}).attr("width",function(u){return u.milestone?k:u.vert?.08*k:H(u.renderEndTime||u.endTime)-H(u.startTime)}).attr("height",function(u){return u.vert?g.length*(a.barHeight+a.barGap)+a.barHeight*2:k}).attr("transform-origin",function(u,o){return o=u.order,(H(u.startTime)+_+.5*(H(u.endTime)-H(u.startTime))).toString()+"px "+(o*F+S+.5*k).toString()+"px"}).attr("class",function(u){const o="task";let R="";u.classes.length>0&&(R=u.classes.join(" "));let P=0;for(const[K,G]of U.entries())u.type===G&&(P=K%a.numberSectionStyles);let z="";return u.active?u.crit?z+=" activeCrit":z=" active":u.done?u.crit?z=" doneCrit":z=" done":u.crit&&(z+=" crit"),z.length===0&&(z=" task"),u.milestone&&(z=" milestone "+z),u.vert&&(z=" vert "+z),z+=P,z+=" "+R,o+z}),m.append("text").attr("id",function(u){return u.id+"-text"}).text(function(u){return u.task}).attr("font-size",a.fontSize).attr("x",function(u){let o=H(u.startTime),R=H(u.renderEndTime||u.endTime);if(u.milestone&&(o+=.5*(H(u.endTime)-H(u.startTime))-.5*k,R=o+k),u.vert)return H(u.startTime)+_;const P=this.getBBox().width;return P>R-o?R+P+1.5*a.leftPadding>l?o+_-5:R+_+5:(R-o)/2+o+_}).attr("y",function(u,o){return u.vert?a.gridLineStartPadding+g.length*(a.barHeight+a.barGap)+60:(o=u.order,o*F+a.barHeight/2+(a.fontSize/2-2)+S)}).attr("text-height",k).attr("class",function(u){const o=H(u.startTime);let R=H(u.endTime);u.milestone&&(R=o+k);const P=this.getBBox().width;let z="";u.classes.length>0&&(z=u.classes.join(" "));let K=0;for(const[j,it]of U.entries())u.type===it&&(K=j%a.numberSectionStyles);let G="";return u.active&&(u.crit?G="activeCritText"+K:G="activeText"+K),u.done?u.crit?G=G+" doneCritText"+K:G=G+" doneText"+K:u.crit&&(G=G+" critText"+K),u.milestone&&(G+=" milestoneText"),u.vert&&(G+=" vertText"),P>R-o?R+P+1.5*a.leftPadding>l?z+" taskTextOutsideLeft taskTextOutside"+K+" "+G:z+" taskTextOutsideRight taskTextOutside"+K+" "+G+" width-"+P:z+" taskText taskText"+K+" "+G+" width-"+P}),_t().securityLevel==="sandbox"){let u;u=Zt("#i"+e);const o=u.nodes()[0].contentDocument;m.filter(function(R){return E.has(R.id)}).each(function(R){var P=o.querySelector("#"+R.id),z=o.querySelector("#"+R.id+"-text");const K=P.parentNode;var G=o.createElement("a");G.setAttribute("xlink:href",E.get(R.id)),G.setAttribute("target","_top"),K.appendChild(G),G.appendChild(P),G.appendChild(z)})}}d(V,"drawRects");function W(b,F,S,_,k,Y,l,h){if(l.length===0&&h.length===0)return;let y,m;for(const{startTime:P,endTime:z}of Y)(y===void 0||P<y)&&(y=P),(m===void 0||z>m)&&(m=z);if(!y||!m)return;if(at(m).diff(at(y),"year")>5){Kt.warn("The difference between the min and max time is more than 5 years. This will cause performance issues. Skipping drawing exclude days.");return}const E=r.db.getDateFormat(),c=[];let u=null,o=at(y);for(;o.valueOf()<=m;)r.db.isInvalidDate(o,E,l,h)?u?u.end=o:u={start:o,end:o}:u&&(c.push(u),u=null),o=o.add(1,"d");X.append("g").selectAll("rect").data(c).enter().append("rect").attr("id",function(P){return"exclude-"+P.start.format("YYYY-MM-DD")}).attr("x",function(P){return H(P.start)+S}).attr("y",a.gridLineStartPadding).attr("width",function(P){const z=P.end.add(1,"day");return H(z)-H(P.start)}).attr("height",k-F-a.gridLineStartPadding).attr("transform-origin",function(P,z){return(H(P.start)+S+.5*(H(P.end)-H(P.start))).toString()+"px "+(z*b+.5*k).toString()+"px"}).attr("class","exclude-range")}d(W,"drawExcludeDays");function Z(b,F,S,_){let k=mr(H).tickSize(-_+F+a.gridLineStartPadding).tickFormat(ne(r.db.getAxisFormat()||a.axisFormat||"%Y-%m-%d"));const l=/^([1-9]\d*)(millisecond|second|minute|hour|day|week|month)$/.exec(r.db.getTickInterval()||a.tickInterval);if(l!==null){const h=l[1],y=l[2],m=r.db.getWeekday()||a.weekday;switch(y){case"millisecond":k.ticks(Yt.every(h));break;case"second":k.ticks(vt.every(h));break;case"minute":k.ticks(Wt.every(h));break;case"hour":k.ticks(Ht.every(h));break;case"day":k.ticks(Tt.every(h));break;case"week":k.ticks(en[m].every(h));break;case"month":k.ticks(Nt.every(h));break}}if(X.append("g").attr("class","grid").attr("transform","translate("+b+", "+(_-50)+")").call(k).selectAll("text").style("text-anchor","middle").attr("fill","#000").attr("stroke","none").attr("font-size",10).attr("dy","1em"),r.db.topAxisEnabled()||a.topAxis){let h=dr(H).tickSize(-_+F+a.gridLineStartPadding).tickFormat(ne(r.db.getAxisFormat()||a.axisFormat||"%Y-%m-%d"));if(l!==null){const y=l[1],m=l[2],E=r.db.getWeekday()||a.weekday;switch(m){case"millisecond":h.ticks(Yt.every(y));break;case"second":h.ticks(vt.every(y));break;case"minute":h.ticks(Wt.every(y));break;case"hour":h.ticks(Ht.every(y));break;case"day":h.ticks(Tt.every(y));break;case"week":h.ticks(en[E].every(y));break;case"month":h.ticks(Nt.every(y));break}}X.append("g").attr("class","grid").attr("transform","translate("+b+", "+F+")").call(h).selectAll("text").style("text-anchor","middle").attr("fill","#000").attr("stroke","none").attr("font-size",10)}}d(Z,"makeGrid");function $(b,F){let S=0;const _=Object.keys(C).map(k=>[k,C[k]]);X.append("g").selectAll("text").data(_).enter().append(function(k){const Y=k[0].split(zn.lineBreakRegex),l=-(Y.length-1)/2,h=M.createElementNS("http://www.w3.org/2000/svg","text");h.setAttribute("dy",l+"em");for(const[y,m]of Y.entries()){const E=M.createElementNS("http://www.w3.org/2000/svg","tspan");E.setAttribute("alignment-baseline","central"),E.setAttribute("x","10"),y>0&&E.setAttribute("dy","1em"),E.textContent=m,h.appendChild(E)}return h}).attr("x",10).attr("y",function(k,Y){if(Y>0)for(let l=0;l<Y;l++)return S+=_[Y-1][1],k[1]*b/2+S*b+F;else return k[1]*b/2+F}).attr("font-size",a.sectionFontSize).attr("class",function(k){for(const[Y,l]of U.entries())if(k[0]===l)return"sectionTitle sectionTitle"+Y%a.numberSectionStyles;return"sectionTitle"})}d($,"vertLabels");function w(b,F,S,_){const k=r.db.getTodayMarker();if(k==="off")return;const Y=X.append("g").attr("class","today"),l=new Date,h=Y.append("line");h.attr("x1",H(l)+b).attr("x2",H(l)+b).attr("y1",a.titleTopMargin).attr("y2",_-a.titleTopMargin).attr("class","today"),k!==""&&h.attr("style",k.replace(/,/g,";"))}d(w,"drawToday");function O(b){const F={},S=[];for(let _=0,k=b.length;_<k;++_)Object.prototype.hasOwnProperty.call(F,b[_])||(F[b[_]]=!0,S.push(b[_]));return S}d(O,"checkUnique")},"draw"),Si={setConf:Di,draw:_i},Fi=d(t=>`
  .mermaid-main-font {
        font-family: ${t.fontFamily};
  }

  .exclude-range {
    fill: ${t.excludeBkgColor};
  }

  .section {
    stroke: none;
    opacity: 0.2;
  }

  .section0 {
    fill: ${t.sectionBkgColor};
  }

  .section2 {
    fill: ${t.sectionBkgColor2};
  }

  .section1,
  .section3 {
    fill: ${t.altSectionBkgColor};
    opacity: 0.2;
  }

  .sectionTitle0 {
    fill: ${t.titleColor};
  }

  .sectionTitle1 {
    fill: ${t.titleColor};
  }

  .sectionTitle2 {
    fill: ${t.titleColor};
  }

  .sectionTitle3 {
    fill: ${t.titleColor};
  }

  .sectionTitle {
    text-anchor: start;
    font-family: ${t.fontFamily};
  }


  /* Grid and axis */

  .grid .tick {
    stroke: ${t.gridColor};
    opacity: 0.8;
    shape-rendering: crispEdges;
  }

  .grid .tick text {
    font-family: ${t.fontFamily};
    fill: ${t.textColor};
  }

  .grid path {
    stroke-width: 0;
  }


  /* Today line */

  .today {
    fill: none;
    stroke: ${t.todayLineColor};
    stroke-width: 2px;
  }


  /* Task styling */

  /* Default task */

  .task {
    stroke-width: 2;
  }

  .taskText {
    text-anchor: middle;
    font-family: ${t.fontFamily};
  }

  .taskTextOutsideRight {
    fill: ${t.taskTextDarkColor};
    text-anchor: start;
    font-family: ${t.fontFamily};
  }

  .taskTextOutsideLeft {
    fill: ${t.taskTextDarkColor};
    text-anchor: end;
  }


  /* Special case clickable */

  .task.clickable {
    cursor: pointer;
  }

  .taskText.clickable {
    cursor: pointer;
    fill: ${t.taskTextClickableColor} !important;
    font-weight: bold;
  }

  .taskTextOutsideLeft.clickable {
    cursor: pointer;
    fill: ${t.taskTextClickableColor} !important;
    font-weight: bold;
  }

  .taskTextOutsideRight.clickable {
    cursor: pointer;
    fill: ${t.taskTextClickableColor} !important;
    font-weight: bold;
  }


  /* Specific task settings for the sections*/

  .taskText0,
  .taskText1,
  .taskText2,
  .taskText3 {
    fill: ${t.taskTextColor};
  }

  .task0,
  .task1,
  .task2,
  .task3 {
    fill: ${t.taskBkgColor};
    stroke: ${t.taskBorderColor};
  }

  .taskTextOutside0,
  .taskTextOutside2
  {
    fill: ${t.taskTextOutsideColor};
  }

  .taskTextOutside1,
  .taskTextOutside3 {
    fill: ${t.taskTextOutsideColor};
  }


  /* Active task */

  .active0,
  .active1,
  .active2,
  .active3 {
    fill: ${t.activeTaskBkgColor};
    stroke: ${t.activeTaskBorderColor};
  }

  .activeText0,
  .activeText1,
  .activeText2,
  .activeText3 {
    fill: ${t.taskTextDarkColor} !important;
  }


  /* Completed task */

  .done0,
  .done1,
  .done2,
  .done3 {
    stroke: ${t.doneTaskBorderColor};
    fill: ${t.doneTaskBkgColor};
    stroke-width: 2;
  }

  .doneText0,
  .doneText1,
  .doneText2,
  .doneText3 {
    fill: ${t.taskTextDarkColor} !important;
  }


  /* Tasks on the critical line */

  .crit0,
  .crit1,
  .crit2,
  .crit3 {
    stroke: ${t.critBorderColor};
    fill: ${t.critBkgColor};
    stroke-width: 2;
  }

  .activeCrit0,
  .activeCrit1,
  .activeCrit2,
  .activeCrit3 {
    stroke: ${t.critBorderColor};
    fill: ${t.activeTaskBkgColor};
    stroke-width: 2;
  }

  .doneCrit0,
  .doneCrit1,
  .doneCrit2,
  .doneCrit3 {
    stroke: ${t.critBorderColor};
    fill: ${t.doneTaskBkgColor};
    stroke-width: 2;
    cursor: pointer;
    shape-rendering: crispEdges;
  }

  .milestone {
    transform: rotate(45deg) scale(0.8,0.8);
  }

  .milestoneText {
    font-style: italic;
  }
  .doneCritText0,
  .doneCritText1,
  .doneCritText2,
  .doneCritText3 {
    fill: ${t.taskTextDarkColor} !important;
  }

  .vert {
    stroke: ${t.vertLineColor};
  }

  .vertText {
    font-size: 15px;
    text-anchor: middle;
    fill: ${t.vertLineColor} !important;
  }

  .activeCritText0,
  .activeCritText1,
  .activeCritText2,
  .activeCritText3 {
    fill: ${t.taskTextDarkColor} !important;
  }

  .titleText {
    text-anchor: middle;
    font-size: 18px;
    fill: ${t.titleColor||t.textColor};
    font-family: ${t.fontFamily};
  }
`,"getStyles"),Yi=Fi,Ai={parser:za,db:Ci,renderer:Si,styles:Yi};export{Ai as diagram};
