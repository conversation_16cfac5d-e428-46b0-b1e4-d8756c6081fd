<template>
  <div class="header ms-header-media">
    <div class="ms-header">
      <!-- BEGIN .wrapper -->
      <div class="wrapper">
        <div
          class="main-menu-placeholder wrapper clearfix"
          style="height: 56px; display: block !important"
        >
          <div class="ms-header-img">
            <a :href="hrefUrl" title="梅斯小智"
              ><img
                src="https://img.medsci.cn/202412/8a5663cb16a84c45b9c8c5db4eda0075-6pyGtTcj0asd.png"
                alt=""
            /></a>
          </div>
          <div id="main-menu" class="ms-header-nav">
            <div class="header-top header-user" id="user-info-header">
              <ul>
                <li v-if="locale == 'zh'">
                  <div class="m_font change_lang m_none h-full">
                    <button
                      type="primary"
                      @click="subScript"
                      style="background-image: linear-gradient(270deg, #FDE39B 0%, #F9D07A 88%)"
                      class="px-[15px] py-[4px] flex items-center h-[28px] rounded border-none text-xs text-[#614018]"
                      >{{  subStatusDetail?.packageType == "免费"?"升级订阅":subStatusDetail?.packageType == "连续包月"||subStatusDetail?.packageType == "连续包年"?"修改订阅":"订阅" }}</button
                    >
                  </div>
                  <div class="m_font change_lang pc_none">
                    <button
                      type="primary"
                      @click="subScript"
                      style="background-image: linear-gradient(270deg, #FDE39B 0%, #F9D07A 88%)"
                      class="px-[14px] py-[4px] rounded-[8px] flex items-center h-[28px] rounded border-none text-xs text-[#614018] whitespace-nowrap"
                      >{{ subStatusDetail?.packageType == "免费"?"升级订阅":subStatusDetail?.packageType == "连续包月"||subStatusDetail?.packageType == "连续包年"?"修改订阅":"订阅" }}</button
                    >
                  </div>
                </li>
                  <li v-if="!isMobiles">
                    <a href="https://ai.medsci.cn/idoc" :title="$t('tool.iDoc')" target="_blank">{{
                      $t("tool.idocWebsite")
                    }}</a>
                </li>
                <li>
                  <div class="mr-2 px-md-4 cursor-pointer m_font">
                    <a href="https://aisite.medsci.cn/" :title="$t('tool.AINavigationSite')" target="_blank">{{
                      $t("tool.AINavigationSite")
                    }}</a>
                  </div>
                </li>
                <li
                  class="index-user-img index-user-img_left"
                  @mouseover="showMenu"
                  @mouseout="hideMenu"
                  @click="showMenu"
                >
                  <div class="change_lang" v-if="!isIncludeTool">
                    <span class="current_lang">{{
                      langs.filter((item) => item.value == selectedLanguage)[0]
                        ?.name
                    }}</span>
                    <span class="ms-link">{{ $t("market.switch") }}</span>
                  </div>
                  <div class="ms-dropdown-menu" ref="menu">
                    <div class="new-header-avator-pop" id="new-header-avator">
                      <div class="new-header-bottom" style="padding: 0">
                        <client-only>
                          <div class="langUl" v-if="langs.length > 0">
                            <p
                              v-for="item in langs"
                              :key="item"
                              @click.stop="toggle(item.value)"
                              :class="{
                                langItemSelected:
                                  item.value === selectedLanguage,
                              }"
                            >
                              {{ item.name }}
                            </p>
                          </div>
                        </client-only>
                      </div>
                    </div>
                  </div>
                </li>
                <template v-if="!userInfo?.userId">
                  <li class="index-user-img_right">
                    <a
                      href="javascript: void(0)"
                      class="ms-link"
                      @click="loginAccount"
                      >{{ $t("market.login") }}</a
                    >
                  </li>
                </template>
                <template v-else>
                  <li
                    class="index-user-img"
                    @mouseover="showMenu1"
                    @mouseout="hideMenu1"
                  >
                    <a href="#">
                      <div class="img-area">
                        <img
                          :src="
                            avatar
                              ? avatar + '?t=' + Date.now()
                              : 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII='
                          "
                          @error="changeImg"
                          alt=""
                        />
                      </div>
                    </a>
                    <div class="ms-dropdown-menu" ref="menu1">
                      <div class="new-header-avator-pop" id="new-header-avator">
                        <a
                          class="new-header-exit ms-statis"
                          ms-statis="logout"
                          href="#"
                          @click="logout()"
                          >{{ $t("market.logout") }}</a
                        >
                        <div class="new-header-top">
                          <div class="new-header-info">
                            <img
                              class="new-header-avatar"
                              :src="
                                avatar
                                  ? avatar + '?t=' + Date.now()
                                  : 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII='
                              "
                              @error="changeImg"
                              alt=""
                            />

                            <div class="new-header-name">
                              <span>{{
                                userInfo?.realName
                                  ? userInfo?.realName
                                  : userInfo?.userName
                              }}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </li>
                </template>
              </ul>
            </div>
          </div>
        </div>
        <!-- END .wrapper -->
      </div>
    </div>
  </div>
</template>
<script setup>
import { ref, computed, onMounted, onBeforeUnmount, watch,defineProps } from "vue";
import { useRouter } from "vue-router";
import { useI18n } from "vue-i18n";
import cookie from "js-cookie";
import { Button as VanButton } from "vant";
import { getDefaultLanguageCode } from "@/common/commonJs";
import { getAppLangs, aiLogout } from "@/api/base";
const emit = defineEmits(["getAppLang", "getAppLang","subScript"]);

const router = useRouter();
const { setLocale, locale } = useI18n();
const props = defineProps({
  subStatusDetail: {
    type: Object,
    default: () => {
      return {};
    },
  },
});
const subStatusDetail = ref(props.subStatusDetail)
// 响应式数据
const avatar = ref("");
const userInfo = ref(null);
const isIncludeTool = ref(false);
const selectedLanguage = ref("");
const langs = ref([]);
const hrefUrl = ref("");
const intervalId = ref(null);
const menu = ref();
const menu1 = ref();
const isMobiles = ref();
// 计算属性
const baseUrl = computed(() => "https://www.medsci.cn/");
const userInfoCookie = computed(() => cookie.get("userInfo"));

// 方法
const changeImg = (e) => {
  avatar.value = "https://img.medsci.cn/web/img/user_icon.png";
};

const showMenu = () => (menu.value.style.display = "block");
const hideMenu = () => (menu.value.style.display = "none");
const showMenu1 = () => (menu1.value.style.display = "block");
const hideMenu1 = () => (menu1.value.style.display = "none");
const subScript = () => {
  emit("subScript");
}
const isMobile = () => {
  return /(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry)/i
    .test(navigator.userAgent);
}
const toggle = (value) => {
  const ai_apps_lang = useCookie("ai_apps_lang", {
    domain:
      import.meta.env.VITE_MODE === "development"
        ? "localhost"
        : import.meta.env.VITE_MODE === "test"
        ? ".medon.com.cn"
        : ".medsci.cn",
    maxAge: 30 * 24 * 60 * 60 * 12,
  });
  ai_apps_lang.value = value;
  selectedLanguage.value = value;
  setLocale(value);
  window.sessionStorage.setItem(
    "redirectUrl",
    value != "zh" ? location.origin + "/" + value : location.origin
  );
  // location.href = location.href+value;
  // emit("getAppLang", value);
  // emit("isZHChange", value === "zh");
  menu.value.style.display = "none";
  isMobiles.value = isMobile();
};

const logout = async () => {
  clearInterval(intervalId.value);

  // 清除所有域的cookie
  cookie.remove("userInfo", { domain: ".medon.com.cn" });
  cookie.remove("userInfo", { domain: ".medsci.cn" });
  cookie.remove("userInfo", { domain: "ai.medon.com.cn" });
  cookie.remove("userInfo", { domain: "ai.medsci.cn" });
  cookie.remove("userInfo", { domain: "localhost" });

  // 清除localStorage
  localStorage.removeItem("conversation");
  Object.keys(localStorage).forEach((key) => {
    if (key.includes("writeContent")) {
      localStorage.removeItem(key);
    }
  });

  const socialType = localStorage.getItem("socialType");
  if (socialType && (socialType == 35 || socialType == 36)) {
    try {
      await aiLogout();
      clearAuthStorage();
      location.reload();
    } catch (err) {
      console.error("Logout failed:", err);
    }
  } else {
    clearAuthStorage();
    const redirectUrl = window.location.href;
    const logoutUrl = window.location.origin.includes("medsci.cn")
      ? `https://www.medsci.cn/sso_logout?redirectUrl=${redirectUrl}`
      : `https://portal-test.medon.com.cn/sso_logout?redirectUrl=${redirectUrl}`;
    window.location.href = logoutUrl;
  }
};

const clearAuthStorage = () => {
  localStorage.removeItem("hasuraToken");
  cookie.remove("yudaoToken", { domain: "ai.medsci.cn" });
  cookie.remove("yudaoToken", { domain: "ai.medon.com.cn" });
  cookie.remove("yudaoToken", { domain: ".medsci.cn" });
  cookie.remove("yudaoToken", { domain: ".medon.com.cn" });
  cookie.remove("yudaoToken", { domain: "localhost" });
  localStorage.removeItem("socialUserId");
  localStorage.removeItem("socialType");
  localStorage.removeItem("openid");
};

const loginAccount = () => {
  const language = getDefaultLanguageCode(locale.value);
  if (!language || language === "zh") {
    window.addLoginDom?.();
  } else {
    location.href = location.origin + "/" + locale.value + "/login";
  }
};

const medsciSearch = () => {
  const keyword = document.getElementById("medsciSearchKeyword")?.value;
  const url = keyword
    ? `https://www.medsci.cn/search?q=${keyword}`
    : "https://search.medsci.cn/";
  window.open(url, "_blank");
};

const getAppLangsData = async () => {
  try {
    const res = await getAppLangs();
    langs.value = res
      .filter((item) => !item.status)
      .map((item) => ({ name: item.value, value: item.remark }));
  } catch (error) {
    console.error("Failed to get app langs:", error);
  }
};

const checkCookie = () => {
  const newVal = cookie.get("userInfo");
  if (newVal !== JSON.stringify(userInfo.value)) {
    userInfo.value = newVal ? JSON.parse(newVal) : null;
    avatar.value =
      userInfo.value?.avatar || "https://img.medsci.cn/web/img/user_icon.png";
  }
};
const { data: res } = await useAsyncData("languages", async () => {
  const route = useRoute();

  const cookie = useCookie("userInfo");
  const ai_apps_lang = useCookie("ai_apps_lang");
  const event = useRequestEvent();
  const header = useRequestHeaders();

  userInfo.value = cookie.value || null;
  avatar.value =
    userInfo.value?.avatar || "https://img.medsci.cn/web/img/user_icon.png";
  const res = await getAppLangs(event);
  langs.value = res
    .filter((item) => !item.status)
    .map((item) => ({ name: item.value, value: item.remark }));
  selectedLanguage.value = ai_apps_lang.value;
  isIncludeTool.value = route.path.includes("/tool");
  isMobiles.value = /(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry)/i.test(header['user-agent']);
  return [isMobiles.value,selectedLanguage.value,isIncludeTool.value,langs.value,avatar.value,userInfo.value];
});
isMobiles.value = process.server? res?.value&&res?.value[0]:isMobile();
selectedLanguage.value = res?.value&&res?.value[1];
isIncludeTool.value = res?.value&&res?.value[2];
langs.value = res?.value&&res?.value[3];
userInfo.value = res?.value&&res?.value[5];
avatar.value = res?.value&&res?.value[4];
// 生命周期
onMounted(() => {
  hrefUrl.value =
    import.meta.env.VITE_MODE === "development"
      ? "http://localhost:3000" + "/" + locale.value
      : import.meta.env.VITE_MODE === "test"
      ? "https://ai.medon.com.cn" + "/" + locale.value
      : import.meta.env.VITE_MODE === "prd"
      ? "https://ai.medsci.cn" + "/" + locale.value
      : "";
  // getAppLangsData()
  intervalId.value = setInterval(checkCookie, 1000);
});

onBeforeUnmount(() => {
  clearInterval(intervalId.value);
});

// 监听cookie变化
watch(userInfoCookie, (newVal) => {
  console.log("userInfo cookie changed:", newVal);
  checkCookie();
});
</script>

<style lang="scss" scoped>
// @media (min-width: 1600px) {
//   .wrapper {
//     width: 71% !important;
//     & > .wrapper {
//       width: 100% !important;
//     }
//   }
// }
.yxd::before {
  content: "";
  display: none !important;
}
.change_lang {
  line-height: 54px;
  display: flex;
  align-items: center;
  .current_lang {
    font-size: 16px;
    color: #000;
    margin-right: 6px;
  }
  .ms-link {
    color: rgba(64, 158, 255);
    margin-right: 20px;
  }
}
.ms-header-img a {
  display: flex;
  align-items: center;
  height: 56px;
  line-height: 56px;
  width: 139px;
}
.ms-header-media {
  height: 56px;
}

#main-menu > ul > li > a > span {
  height: auto;
}

.ms-header-padding {
}

.ms-header #main-menu > .header-user {
  top: 0;
}
.ms-header #main-menu > .header-user > ul {
  display: flex;
}
.header .header-top ul li a {
  font-size: 14px;
}

.header {
  padding: unset;
  margin-bottom: 0;
}

.apps {
  position: fixed;
  right: 50px;
  bottom: 200px;
  z-index: 1000;
  width: 90px;
  height: 90px;
}

.apps img {
  width: 100%;
  height: 100%;
}

.iconfont {
  cursor: pointer;
  // position: absolute;
  // color: #cccccc;
  // top: -20px;
  // right: -20px;
  // cursor: pointer;
  // font-weight: bold;
  // text-align: center;
}

.index-user-img {
  position: relative;

  // &:hover,&:active {
  //   .ms-dropdown-menu {
  //     display: block;
  //   }
  // }

  a {
    padding-top: 10px !important;
    padding-bottom: 0px !important;
  }

  .img-area {
    display: inline-block;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    overflow: hidden;
  }

  .ms-dropdown-menu {
    display: none;
    right: 0;
    min-width: 124px;
    padding-top: 4px;
    position: absolute;
    top: 30px;
    z-index: 101;

    .ms-dropdown-content {
      background-color: white;
      border-radius: 3px;
      box-shadow: 0 2px 3px rgba(10, 10, 10, 0.1),
        0 0 0 1px rgba(10, 10, 10, 0.1);
      padding-bottom: 4px;
      padding-top: 6px;

      hr {
        margin-top: 5px;
        margin-bottom: 5px;
      }

      .ms-dropdown-item {
        display: block;
        font-size: 14px;
        // height: 32px;
        line-height: 32px;
        text-align: center;
        color: #232323;
        padding: 0 !important;
        width: 124px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;

        &:hover {
          background: rgba(47, 146, 238, 0.1);
        }
      }
    }
  }
}
</style>

<style>
.code-validate .nc-container .nc_scale {
  background: #dfefff !important;
}

.code-validate
  .nc-container
  .scale_text.scale_text.slidetounlock
  span[data-nc-lang="_startTEXT"] {
  background: -webkit-gradient(
    linear,
    left top,
    right top,
    color-stop(0, #2f92ee),
    color-stop(0.4, #2f92ee),
    color-stop(0.5, #fff),
    color-stop(0.6, #2f92ee),
    color-stop(1, #2f92ee)
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  -webkit-animation: slidetounlock 3s infinite;
  -webkit-text-size-adjust: none;
}

.code-validate .nc-container .nc_scale div.nc_bg {
  background: #7ac23c !important;
  /* 滑过时的背景色 */
}

.code-validate .nc-container .nc_scale .scale_text2 {
  color: #fff !important;
  /* 滑过时的字体颜色 */
}

/* .dot_red{
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;  
  background: red; 
} */

.langItemSelected {
  background-color: #dcdfe6;
}

.langUl {
  display: flex;
  flex-wrap: wrap;
  list-style: none;
  padding: 0;
  margin: 0;
}

.langUl p {
  flex: 0 0 calc(33.333% - 0px);
  box-sizing: border-box;
  text-align: left;
  margin-right: 0px;
  /* margin-bottom: 20px; */
  cursor: pointer;
  box-sizing: border-box;
  padding: 10px 0 10px 20px;
  margin-bottom: 0;
}

/* 最后一行的最后一个li不需要右边距 */
.langUl p:nth-child(3n) {
  margin-right: 0;
}

/* 最后一行的最后一个li不需要底边距 */
/* .langUl p:last-child {
  margin-bottom: 0;
} */

.langUl p:hover {
  background-color: #f4f4f5;
}
@media screen and (min-width: 768px) {
  .pc_none{
    display: none!important;
  }
}
@media screen and (max-width: 768px) {
  .new-header-exit {
    text-align: right;
  }
  .ms-header-img img {
  line-height: 56px;
  width: 80px;
}
  .m_none{
    display: none !important;
  }
  .pc_none{
    display: flex!important;
    height: 54px;
  }
  .pc_none .van-button__text{
      font-size: 12px;
      color: red;
    }
  #new-header-avator.new-header-avator-pop {
    width: 80vw !important;
  }
  .overflow-auto:hover ~ .ms-dropdown-menu {
    display: none;
  }
  .change_lang .ms-link {
    margin-right: 0 !important;
  }
  .change_lang .current_lang {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 56%;
    display: inline-block;
    text-align: right;
  }
  .index-user-img_left .change_lang {
    display: flex;
    align-items: center;
    height: 56px;
    white-space: nowrap;
    justify-content: flex-end;
  }
  .ms-header #main-menu > .header-user > ul {
    justify-content: flex-end;
    width: 200px;
  }
  .index-user-img_left .change_lang .ms-link {
    display: inline-block;
  }
  .index-user-img_right {
    display: inline-block;
    text-align: right;
    width: 25%;
  }
  .header .header-top ul li a {
    width: 100%;
    padding: 17px 0 17px 10px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
</style>
