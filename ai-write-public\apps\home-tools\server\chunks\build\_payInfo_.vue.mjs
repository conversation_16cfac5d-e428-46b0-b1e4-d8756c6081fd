import { ssrRenderAttrs } from 'vue/server-renderer';
import { useRoute } from 'vue-router';
import { ref, useSSRContext } from 'vue';
import { _ as _export_sfc } from './_plugin-vue_export-helper.mjs';
import { e as useI18n } from './server.mjs';
import '../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'node:fs';
import 'node:url';
import 'consola';
import 'node:path';
import 'node:crypto';
import 'element-plus';

const _sfc_main = {
  __name: "[payInfo]",
  __ssrInlineRender: true,
  setup(__props) {
    const { t } = useI18n();
    useRoute();
    ref();
    ref("");
    return (_ctx, _push, _parent, _attrs) => {
      _push(`<div${ssrRenderAttrs(_attrs)} data-v-8b8a31de><button data-v-8b8a31de>返回首页</button></div>`);
    };
  }
};
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/payLink/[payInfo].vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const _payInfo_ = /* @__PURE__ */ _export_sfc(_sfc_main, [["__scopeId", "data-v-8b8a31de"]]);

export { _payInfo_ as default };
//# sourceMappingURL=_payInfo_.vue.mjs.map
