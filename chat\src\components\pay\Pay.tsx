import React, { useState, useEffect } from "react";
import "../../assets/css/pay.css"; // 假设 CSS 文件单独存放
import { useRequest } from "ahooks";
import AppService from '../../services/app/request'
import { useDifyChat,IDifyChatContextSingleApp } from '@dify-chat/core'
import Cookies from "js-cookie";
import { message } from 'antd';
import {QRCodeCanvas } from 'qrcode.react';
import { useTranslation } from 'react-i18next';
const Pay = ({ userInfo, currentItem, onClose,appConfig }: { 
  userInfo: any, 
  currentItem: any, 
  onClose?: (val:boolean) => void ,
  appConfig:any
}) => {
  const { t } = useTranslation();

  // 订阅类型映射函数
  const getSubscriptionTypeText = (type: string) => {
    const typeMap: { [key: string]: string } = {
      '免费': t('subscription.free'),
      '连续包月': t('subscription.monthly'),
      '连续包季': t('subscription.quarterly'),
      '连续包年': t('subscription.yearly')
    }
    return typeMap[type] || type
  }

  userInfo = JSON.parse(userInfo) || {};
  const [loading, setLoading] = useState(false);
  const [activeItem, setActiveItem]:any = useState({});
  const [active, setActive] = useState(null);
  const [payUrl, setPayUrl] = useState("");
  const [avatar, setAvatar] = useState(
    userInfo.avatar || "https://img.medsci.cn/web/img/user_icon.png"
  );
  const [isPc, setIsPc] = useState(window.innerWidth > 768);
  const [piId, setPiId] = useState(null);
  const [timer, setTimer] = useState(null);
  const appserver = new AppService()

  const getDefaultLanguageCode = async () => {
    return "zh"; // 模拟语言
  };

  const isUp =
    window.location.origin.includes("medon.com.cn") ||
    window.location.origin.includes("medsci.cn");

  const changeImg = () => {
    setAvatar("https://img.medsci.cn/web/img/user_icon.png");
  };

  const toAgreement = () => {
    window.open("https://www.medsci.cn/about/index.do?id=27");
  };

  const checkScreenWidth = () => {
    setIsPc(window.innerWidth > 768);
  };

  const close = () => {
    if (timer) clearInterval(timer);
    onClose?.(false);
  };

  const CheckItem = (item: any, index: number) => {
    setActiveItem(item);
    setActive(index);
    if (item?.coinType== '人民币' && item.feePrice !== 0) {
      subscribe(item, appConfig.id);
    }
  };

  const getStatus = (piId:any) => {
    const interval = setInterval(async () => {
      const res = await appserver.getSubOrder({piId:`${piId}`},{
        Authorization: `Bearer ${Cookies.get("yudaoToken")}`});
      if (res?.data?.payStatus === "PAID") {
        window.location.reload();
        clearInterval(interval);
      }
    }, 2000);
    // 将 NodeJS.Timeout 类型转换为 React state 可接受的类型
    setTimer(interval as unknown as null);
  };
  const language = () => {
		return Cookies.get(
			'ai_apps_lang'
		)? Cookies.get('ai_apps_lang')
		: navigator.browserLanguage || navigator.language
	}
  const subscribe = async (item: any, appUuid: string) => {
    if (! item?.coinType) {
      message.warning("请选择订阅服务周期"); // 替换为 UI 提示
      return;
    }
    const languages = language();
    if (!userInfo.userId) {
      if (!languages || languages === "zh") {
        console.log("Trigger login"); // 替换为登录逻辑
      } else {
        window.top.location.href =  location.origin + '/' + languages + "/login"
      }
    } else {
      const subscriptionParams = {
        appUuid:appUuid||'',
        priceId: item.priceId,
        monthNum: item.monthNum,
		packageKey:item.packageKey,
		packageType:item.type
      };
      try {
        setLoading(true);
        const res = await appserver.createSubscription(subscriptionParams,{
          Authorization: `Bearer ${Cookies.get("yudaoToken")}`,
        });
        if(res?.code==0){
        setLoading(false);
        if (item.coinType == "人民币" && item.feePrice !== 0) {
          const payInfo = res.data;
          const origin = window.location.origin;
          const payLink =
            origin.includes(".medsci.cn") || origin.includes(".medon.com.cn")
              ? `${origin}/payLink/${encodeURIComponent(payInfo)}`
              : `${origin}/payLink/${encodeURIComponent(payInfo)}`;
          setPayUrl(payLink);
          setPiId(JSON.parse(payInfo).piId);
          if (timer) {
            clearInterval(timer as NodeJS.Timeout);
          }
          getStatus(JSON.parse(payInfo).piId);
        } else {
          message.success(t("tool.sS")); // 替换为 UI 提示
          setTimeout(() => {
            window.top.location.href = res.data;
          }, 1000);
        }
        }
        
      } catch (error) {
        setLoading(false);
        console.error(error);
      }
    }
  };

  useEffect(() => {
    if (currentItem.appType === "写作") {
      localStorage.setItem(
        `appWrite-${appConfig.id}`,
        JSON.stringify({
          appUuid: appConfig.id,
          directoryMd: currentItem.directoryMd,
        })
      );
    }
    checkScreenWidth();
    window.addEventListener("resize", checkScreenWidth);

    if (isPc && currentItem.feeTypes?.length === 1) {
      const feeType = currentItem.feeTypes[0];
      CheckItem(feeType, 0);
    }

    return () => {
      window.removeEventListener("resize", checkScreenWidth);
      if (timer) clearInterval(timer);
    };
  }, [currentItem, isPc]);

  return (
    <div id="app">
      <div className="scale">
        <div className="micro_header">
          <div className="micro_left">
            <div className="avatar">
              <img src={avatar} onError={changeImg} alt="" />
              <span className="t1">{userInfo.realName || userInfo.userName}</span>
            </div>
          </div>
          <div className="micro_right">
            <img
              src="https://static.medsci.cn/public-image/ms-image/78bf61f0-208d-11ee-ae3d-b3e9904fad92_关闭@2x.png"
              alt=""
              onClick={close}
            />
          </div>
        </div>
        <div className="micro_main">
          <div className="micro_main_top">
            <div className="micro_main-sp">
              <div className="micro_main_temp">
                {(currentItem.feeTypes[0]?.coinType == '美元' || currentItem.feeTypes?.length > 1) && (
                  <div className="swiper-vip">
                    {currentItem.feeTypes?.map((item: { type: string; coinType: string; feePrice: number }, index: number) => (
                      <div
                        key={index}
                        className="swiper-vip-item"
                        onClick={() => CheckItem(item, index)}
                      >
                        <div
                          className="newer"
                          style={{
                            left: index % 4 === 0 && index !== 0 ? "6px" : "-1px",
                          }}
                        ></div>
                        <div
                          className={`swiper-vip-item-child ${
                            active === index ? "sactvie" : ""
                          }`}
                        >
                          <div className="title">{getSubscriptionTypeText(item.type)}</div>
                          <div className="pricePc">
                            <span>{item.coinType === "人民币" ? "¥" : "$"}</span>{" "}
                            {item.feePrice}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>
          <div className="micro_main_middle">
            <div className="micro_main_middle_banner">
              <div className="micro_main_middle_title">
                <img
                  src="https://img.medsci.cn/202503/36f8fcc00cb841eaaa9ff81b3b225285-Ar3LIaiI1GQo.png"
                  alt=""
                />
                {currentItem.name}
              </div>
              <div className="micro_main_middle_content">
                {currentItem.description}
              </div>
            </div>
          </div>
          {activeItem.coinType && activeItem.feePrice !== 0 && activeItem?.coinType== '人民币' && (
            <div className="micro_main_bottom">
              <div className="micro_pay">
                <div className="micro_pay_right">
                  {loading && <div className="noQrCode"></div>}
                  {!loading && payUrl && (
                    <div className="qr-code">
                      {/* 替换为 qrcode.react 或其他二维码库 */}
                      <QRCodeCanvas 
                      value={payUrl}
                      size={131}
                      fgColor="#000"
                      level="L" // 纠错等级高
                    />
                    </div>
                  )}
                  <div className="price">
                    <div className="micro_way">
                      <div className="box">
                        <img
                          src="https://static.medsci.cn/public-image/ms-image/19eef410-5893-11ec-8e2f-1389d01aad85_zfb.png"
                          alt=""
                        />
                      </div>
                      <span>{t("tool.Support_Alipay_Payment")}</span>
                    </div>
                    <span className="t1">
                      {t("tool.Support_Alipay_Payment")}
                      <span className="bd">{activeItem.feePrice}</span>
                      {activeItem.coinType === "人民币" ? "¥" : "$"}/
                      {activeItem.monthNum === 3
                        ? t("tool.Quarter")
                        : activeItem.monthNum === 12
                        ? t("tool.Year")
                        : t("tool.Month")}
                    </span>
                    <span className="t2">
                      {t("tool.Meisi_Account")}：{userInfo.userName}
                    </span>
                    <span className="t3" onClick={toAgreement}>
                      {t(
                        "tool.Please_activate_after_reading_and_agreeing_to_the_agreement"
                      )}
                      <img
                        src="https://static.medsci.cn/public-image/ms-image/17ae6920-5be4-11ec-8e2f-1389d01aad85_right.png"
                        alt=""
                      />
                    </span>
                  </div>
                </div>
              </div>
            </div>
          )}
          {activeItem.coinType && activeItem.feePrice === 0 && (
            <div className="btns">
              <button
                className="subscribe-btn"
                onClick={() => subscribe(activeItem, appConfig.id)}
              >
                {t("tool.Free_Trial")}
              </button>
            </div>
          )}
          {activeItem.coinType && activeItem.feePrice > 0 && activeItem?.coinType== '美元' && (
            <div className="btns">
              <button
                className="subscribe-btn"
                onClick={() => subscribe(activeItem, appConfig.id)}
              >
                {t("market.subscribe")}
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Pay;