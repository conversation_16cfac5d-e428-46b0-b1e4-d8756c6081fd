<template>
  <div class="p-3 flex-1 rounded-md">
    <h6 class="text-[14px] font-bold mb-2 text-gray-600">
      {{ label }}
    </h6>
    <template v-if="type === 'paragraph' || type === 'text-input'">
      <el-input @focus="focusSubscribe" v-model="inputValue" :type="type === 'paragraph' ? 'textarea' : 'text'"
        :rows="5" :required="required" :placeholder="`${label}`" show-word-limit resize="none"
        :maxlength="max_length" />
    </template>

    <el-input v-else-if="type === 'number'" v-model.number="inputValue" @focus="focusSubscribe" type="number" :required="required"
      :placeholder="`${label}`" resize="none" />

    <el-select v-else-if="type === 'select'" @change="focusSubscribe" v-model="inputValue" :required="required"
      :placeholder="`${label}`">
      <el-option v-for="option in options" :key="option" :label="option" :value="option" />
    </el-select>
    <el-upload
        v-model:file-list="fileList"
        class="upload-demo"
        multiple
        show-file-list
        v-else-if="type === 'file'||type === 'file-list'"
        :on-preview="handlePreview"
        :on-remove="handleRemove"
        :before-remove="beforeRemove"
        :limit="max_length"
        :accept="checkFileType()"
        :auto-upload="true"
        :on-Success="handleSuccess"
        :http-request="customRequest"
        :on-exceed="handleExceed"
        >
       <el-button :disabled="type === 'file'?fileList.length==1:fileList.length==max_length?true:false"><el-icon class="el-icon--upload" style="margin-right:5px;font-size:16px"><upload-filled /></el-icon>从本地上传</el-button>
    </el-upload>
  </div>
</template>

<script setup>
import { defineProps, defineEmits, ref } from 'vue';
import { upload  } from "@/api/base";
import cookie from "js-cookie";
import { useRoute,useRouter } from 'vue-router';
import { UploadFilled } from '@element-plus/icons-vue'
const userInfo = ref(cookie.get("userInfo") ? JSON.parse(cookie.get("userInfo")) : {}); // 用户信息
import {
  getDefaultLanguageCode,
} from "@/common/commonJs";
import {useI18n} from "vue-i18n"
const route = useRoute()
const router = useRouter()
const {locale} = useI18n()
const fileList = ref([])
const fileType = ref({
  "jpg": "image",
  "jpeg": "image",
  "png": "image",
  "webp": "image",
  "gif": "image",
  "svg": "image",
  "mp4": "video",
  "mov": "video",
  "mpeg": "video",
  "mpga": "video",
  "mp3": "audio",
  "m4a": "audio",
  "wav": "audio",
  "webm": "audio",
  "amr": "audio",
  "txt": "document",
  "markdown": "document",
  "md": "document",
  "mdx": "document",
  "pdf": "document",
  "html": "document",
  "htm": "document",
  "xlsx": "document",
  "xls": "document",
  "docx": "document",
  "csv": "document",
  "eml": "document",
  "msg": "document",
  "pptx": "document",
  "xml": "document",
  "epub": "document"
})

const props = defineProps({
  type: { type: String, required: true },
  label: { type: String, required: true },
  // value: { type: [String, Number], required: true },
  required: { type: Boolean, required: true },
  placeholder: { type: String, required: true },
  max_length: { type: Number, required: false },
  options: { type: Array, required: false },
  fileVerify:{type: Array, required: false, default: () => ['']},
  currentItem:{type:Object,required:false}
});
const inputValue = ref(props.options?.[0] || '');
const type = props.type
const fileVerify = props.fileVerify
const label = props.label
const required = props.required
const max_length = props.max_length
const options = props.options
if(type == 'file'){
  inputValue.value=null
}
if(type == 'file-list'){
  inputValue.value=[]
}
const acceptList ={"image":[".jpg",".jpeg",".png",".webp",".gif",".svg"],"video":[".mp4",".mov",".mpeg",".mpga"],"audio":[".mp3",".m4a",".wav",".webm",".amr"],"document":[".txt",".markdown",".md",".mdx",".pdf",".html",".htm",".xlsx",".xls",".docx",".csv",".eml",".msg",".pptx",".xml",".epub"]}
const checkFileType = () => {
  let strList = ''
  fileVerify.forEach((i,index)=>{
    if(index<fileVerify.length-1){
    strList +=acceptList[i].join(",")+','
    }else{
      strList +=acceptList[i].join(",")
    }
  })
  return strList
}
const emit = defineEmits(['update:value']);


// 获取焦点判断是否登录和订阅
const focusSubscribe = async () => {
    if (!cookie.get("userInfo")) {
  cookie.remove("yudaoToken",{ domain: "ai.medsci.cn" });
  cookie.remove("yudaoToken",{ domain: "ai.medon.com.cn" });
  cookie.remove("yudaoToken",{ domain: ".medsci.cn" });
  cookie.remove("yudaoToken",{ domain: ".medon.com.cn" });
  cookie.remove("yudaoToken",{ domain: "localhost" });
    localStorage.removeItem("hasuraToken");
    const language = getDefaultLanguageCode(locale.value);
    if (!language || language == "zh") {
      window.addLoginDom();
    } else {
      // 跳转到获取授权页
      location.href = location.origin + '/' +locale.value + "/login";
    }
    Array.from(document.getElementsByTagName("input")).forEach(i => i.blur());
    Array.from(document.getElementsByTagName("textarea")).forEach(i => i.blur());
    return false;
  }
  if(!props.currentItem?.appUser?.status || props.currentItem?.appUser?.status == 2){
    emit('payShowStatus', true)
    Array.from(document.getElementsByTagName("input")).forEach(i => i.blur());
    Array.from(document.getElementsByTagName("textarea")).forEach(i => i.blur());
    return false;
  }  
    return true
  }
const handleSuccess = (res,file, fileList)=>{
}
const handleRemove = ()=>{
  inputValue.value = ''
}
const handlePreview = (file)=>{
  console.log('todo handlePreview',file)
}
const beforeRemove = (file, fileList) => {
  console.log('todo beforeRemove', file, fileList);
}
const handleExceed = (files, fileList) => {
  console.log('todo handleExceed', files, fileList);
}
const customRequest=async (options)=> {
 const isLoginAndSub=await focusSubscribe()
 if(!isLoginAndSub){
  return false
 }
  const { file, onSuccess, onError } = options;
    // 自定义上传行为，如设置 headers 或请求方式
    const formData = new FormData();
    formData.append('file', file);
    formData.append('appId', props.currentItem?.dAppUuid);
    formData.append('user', userInfo.value.userName);
    try {
      const res = await upload(formData)
      if(type=="file-list"){
        inputValue.value.push({
        "type":fileType.value[res.extension],
        "transfer_method":"local_file",
        "url":"",
        "upload_file_id":res.id
      })
      }else{
        inputValue.value = {
        "type":fileType.value[res.extension],
        "transfer_method":"local_file",
        "url":"",
        "upload_file_id":res.id
      }
      }
      onSuccess(res, file);
    } catch (error) {
      onError(error);
    }
   
    // 返回 false 阻止 el-upload 默认行为
    return false;
  }
// 子组件的方法
const updateMessage = () => {
  if(options&&options.length>0){
    inputValue.value =  options[0]
  }
  else if(type == 'file'){
  inputValue.value=null
  fileList.value = []
  }
  else if(type == 'file-list'){
    inputValue.value=[]
    fileList.value = []
  }else{
    inputValue.value = ''
  }
};

// 暴露方法给父组件
defineExpose({
  updateMessage
});
watch(inputValue, (newValue) => {
    emit('update:value', newValue);
},{ immediate: true,deep:true });
</script>
<style lang="scss" scoped>

</style>
