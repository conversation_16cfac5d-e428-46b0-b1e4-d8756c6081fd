import axios from "axios";
import storage from "./storage"; // 假设 storage 是自定义的存储工具
import { ElMessage } from "element-plus"; // 客户端专用的消息提示
import { $loading } from "@/utils/loading"; // 加载状态工具
import cookie from "js-cookie"; // 客户端 cookie 操作
import {Cookies} from '@/utils/cookieHandler'
// 服务端专用的 cookie 解析工具（仅在服务端使用）
import { parseCookies, getCookie } from "h3";
import {
  mainLogin,
} from "@/api/base";
// 判断运行环境
const isServer = typeof window === "undefined";

// 获取 userId，兼容服务端和客户端
const userId = isServer
  ? "" // 服务端初始化为空，实际从请求头或 cookie 中获取
  : cookie.get("userInfo")
    ? JSON.parse(cookie.get("userInfo"))?.userId
    : "";
// 设置 baseURL，兼容开发和生产环境
let baseURL = import.meta.env.VITE_BASE_URL || "";
if (import.meta.env.VITE_MODE === "development") {
  baseURL = isServer ? import.meta.env.VITE_BASE_URL : "/dev-api";
} else {
  baseURL =  import.meta.env.VITE_BASE_URL+ "/dev-api"; // 可根据实际生产环境调整
}

// 创建 axios 实例
const service = axios.create({
  baseURL,
  timeout: 1000 * 60 * 2, // 2 分钟超时
  headers: { "Content-Type": "application/json;charset=utf-8" },
});

// 请求拦截器
service.interceptors.request.use(
  (config) => {
    // 处理文件上传的 Content-Type
    if (config.url.includes("v1/files/upload")) {
      config.headers["Content-Type"] = "multipart/form-data";
    }

    // 获取设备 ID 和 token
    const deviceId = storage.get("deviceId") || null;
    const token = Cookies.get("yudaoToken") || null;

    // 设置请求头
    config.headers["Visitor-Code"] = deviceId;
    config.headers["User-Id"] = userId;

    // 非特定接口添加 Authorization
    if (config.url !== "/dev-api/ai-base/index/getAiWriteToken" && token) {
      config.headers["Authorization"] = `Bearer ${token}`;
    }

    // 处理 GET 请求的参数
    if (config.method === "get") {
      config.params = config.data;
    }

    // 仅在客户端显示 loading
    if (!isServer && !config.noLoading) {
      $loading.showLoading();
    }

    return config;
  },
  (error) => {
    console.error("Request Error:", error);
    return Promise.reject(error);
  }
);

// 响应拦截器
service.interceptors.response.use(
  (response) => {
    const { status, data } = response;
    // 仅在客户端隐藏 loading
    if (!isServer) {
      $loading.hideLoading();
    }

    if (status === 200) {
      const code = data.code;
      switch (code) {
        case 0: // 成功
          if (data.data) {
            return Promise.resolve(data.data);
          } else {
            if (!isServer) {
              ElMessage.error("访问太火爆了！请稍后再试~");
            }
            return Promise.reject("访问太火爆了！请稍后再试~");
          }
        case 401: // 未授权
          // 清空客户端存储（仅客户端执行）
          if (!isServer) {
            Object.keys(localStorage)
              .filter((key) => key.includes("writeContent"))
              .forEach((key) => localStorage.removeItem(key));

            // 删除 cookie，兼容多个域名
            cookie.remove("userInfo", { domain: ".medon.com.cn" });
            cookie.remove("userInfo", { domain: ".medsci.cn" });
            cookie.remove("userInfo", { domain: "localhost" });

            // 删除本地存储
            localStorage.removeItem("conversation");
            localStorage.removeItem("hasuraToken");
            cookie.remove("yudaoToken",{ domain: "ai.medsci.cn" });
            cookie.remove("yudaoToken",{ domain: "ai.medon.com.cn" });
            cookie.remove("yudaoToken",{ domain: ".medsci.cn" });
            cookie.remove("yudaoToken",{ domain: ".medon.com.cn" });
            cookie.remove("yudaoToken",{ domain: "localhost" });
            localStorage.removeItem("socialUserId");
            localStorage.removeItem("socialType");
            localStorage.removeItem("openid");

            // 根据语言和域名跳转
            const language = cookies.get("ai_apps_lang");
            if (!language || language === "zh") {
              window.addLoginDom();
            } else {
              const origin = location?.origin;
              const loginUrl =
                origin.includes("medon.com.cn") || origin.includes("medsci.cn")
                  ? `${origin}${language?'/'+language:''}/login`
                  : `${origin}${language?'/'+language:''}/${language}/login`;
              location.href = loginUrl;
            }
          }
          return Promise.reject("未授权，请重新登录");
        default:
          console.error("Response Error:", data, code);
          if (!isServer) {
            ElMessage.error(data.msg);
          }
          return Promise.reject(data);
      }
    } else {
      if (!isServer) {
        ElMessage.error("访问太火爆了！请稍后再试~");
      }
      return Promise.reject("访问太火爆了！请稍后再试~");
    }
  },
  (error) => {
    console.error("Response Error:", error);
    if (!isServer) {
      $loading.hideLoading();
    }
    return Promise.reject(error);
  }
);

// 服务端专用的请求方法（如果需要从服务端调用）
export const serverRequest = async (config, event) => {
  if (!event) {
    throw new Error("Server request requires an event object");
  }

  // 从服务端请求中解析 cookie
  const cookies = parseCookies(event);  const userInfo = cookies.userInfo ? JSON.parse(cookies.userInfo) : {};
  const token = cookies.yudaoToken || null;
  config.headers = config.headers || {};
  if(!config.url.includes("getAiWriteToken")){
    if(token){
      config.headers["Authorization"] = `Bearer ${token}`;
    }else{
      const yudaoToken =  useCookie('yudaoToken',{domain:import.meta.env.VITE_MODE === "development" ?'localhost':import.meta.env.VITE_MODE === "test" ?"ai.medon.com.cn" : "ai.medsci.cn",maxAge: 30 * 24 * 60 * 60 * 12} )
      const usericon = useCookie("userInfo");
      const event = process.server ? useRequestEvent() : null;
     // 获取芋道token
          const userInfoStr = JSON.stringify(usericon.value);
          if (userInfoStr) {
            const userInfo = JSON.parse(userInfoStr);
            const res = await  mainLogin({
                userId: userInfo.userId,
                userName: userInfo.userName,
                realName: userInfo.realName,
                avatar: userInfo.avatar,
                plaintextUserId: userInfo.plaintextUserId,
                mobile: userInfo.mobile,
                email: userInfo.email,
              },event)
              yudaoToken.value = res?.token;
              config.headers["Authorization"] = `Bearer ${res?.token}`;
              // config.headers["Authorization"] = `Bearer ${res?.token}`;
          }
    }
  }
  const serverUserId = userInfo?.userId || "";
  // 动态设置 userId

  config.headers["User-Id"] = serverUserId;
  return service.request(config);
};

// 导出默认请求方法（客户端使用）
export default service.request;