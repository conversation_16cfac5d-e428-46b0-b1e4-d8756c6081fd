const error500_vue_vue_type_style_index_0_scoped_bfd09c70_lang = ".spotlight[data-v-bfd09c70]{background:linear-gradient(45deg,#00dc82,#36e4da 50%,#0047e1);filter:blur(20vh)}.fixed[data-v-bfd09c70]{position:fixed}.-bottom-1\\/2[data-v-bfd09c70]{bottom:-50%}.left-0[data-v-bfd09c70]{left:0}.right-0[data-v-bfd09c70]{right:0}.grid[data-v-bfd09c70]{display:grid}.mb-16[data-v-bfd09c70]{margin-bottom:4rem}.mb-8[data-v-bfd09c70]{margin-bottom:2rem}.h-1\\/2[data-v-bfd09c70]{height:50%}.max-w-520px[data-v-bfd09c70]{max-width:520px}.min-h-screen[data-v-bfd09c70]{min-height:100vh}.place-content-center[data-v-bfd09c70]{place-content:center}.overflow-hidden[data-v-bfd09c70]{overflow:hidden}.bg-white[data-v-bfd09c70]{--un-bg-opacity:1;background-color:rgb(255 255 255/var(--un-bg-opacity))}.px-8[data-v-bfd09c70]{padding-left:2rem;padding-right:2rem}.text-center[data-v-bfd09c70]{text-align:center}.text-8xl[data-v-bfd09c70]{font-size:6rem;line-height:1}.text-xl[data-v-bfd09c70]{font-size:1.25rem;line-height:1.75rem}.text-black[data-v-bfd09c70]{--un-text-opacity:1;color:rgb(0 0 0/var(--un-text-opacity))}.font-light[data-v-bfd09c70]{font-weight:300}.font-medium[data-v-bfd09c70]{font-weight:500}.leading-tight[data-v-bfd09c70]{line-height:1.25}.font-sans[data-v-bfd09c70]{font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji}.antialiased[data-v-bfd09c70]{-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}@media (prefers-color-scheme:dark){.dark\\:bg-black[data-v-bfd09c70]{--un-bg-opacity:1;background-color:rgb(0 0 0/var(--un-bg-opacity))}.dark\\:text-white[data-v-bfd09c70]{--un-text-opacity:1;color:rgb(255 255 255/var(--un-text-opacity))}}@media (min-width:640px){.sm\\:px-0[data-v-bfd09c70]{padding-left:0;padding-right:0}.sm\\:text-4xl[data-v-bfd09c70]{font-size:2.25rem;line-height:2.5rem}}";

const error500Styles_Cx3AJg0u = [error500_vue_vue_type_style_index_0_scoped_bfd09c70_lang];

export { error500Styles_Cx3AJg0u as default };
//# sourceMappingURL=error-500-styles.Cx3AJg0u.mjs.map
