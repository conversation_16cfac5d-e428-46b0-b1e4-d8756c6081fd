{"version": 3, "file": "lang.mjs", "sources": ["../../../../../../home-tools/langs/lang.js"], "sourcesContent": null, "names": [], "mappings": "AAAY,MAAC,OAAQ,GAAA;AAAA,EACb,OAAQ,EAAA,WAAA;AAAA,EACR,OAAQ,EAAA,WAAA;AAAA,EACR,IAAK,EAAA,WAAA;AAAA,EACL,IAAK,EAAA,4BAAA;AAAA,EACL,IAAK,EAAA,4BAAA;AAAA,EACL,IAAK,EAAA,mBAAA;AAAA,EACL,OAAQ,EAAA,WAAA;AAAA,EACR,OAAQ,EAAA,WAAA;AAAA,EACR,IAAK,EAAA,yBAAA;AAAA,EACL,IAAK,EAAA,oBAAA;AAAA,EACL,IAAK,EAAA,4BAAA;AAAA,EACL,IAAK,EAAA,sBAAA;AAAA,EACL,IAAK,EAAA,8BAAA;AAAA,EACL,IAAK,EAAA;AACb;AACY,MAAC,IAAM,GAAA;AAAA,EACX,KAAO,EAAA;AAAA,IACH,OAAS,EAAA,gBAAA;AAAA,IACT,OAAS,EAAA,gBAAA;AAAA,IACT,IAAM,EAAA,gBAAA;AAAA,IACN,IAAM,EAAA,oCAAA;AAAA,IACN,IAAM,EAAA,0CAAA;AAAA,IACN,IAAM,EAAA,2BAAA;AAAA,IACN,OAAS,EAAA,gBAAA;AAAA,IACT,OAAQ,EAAA,gBAAA;AAAA,IACR,IAAM,EAAA,qCAAA;AAAA,IACN,IAAM,EAAA,4BAAA;AAAA,IACN,IAAM,EAAA,uCAAA;AAAA,IACN,IAAM,EAAA,+CAAA;AAAA,IACN,IAAM,EAAA,iCAAA;AAAA,IACN,IAAM,EAAA;AAAA,GACT;AAAA,EACD,QAAU,EAAA;AAAA,IACN,OAAS,EAAA,iCAAA;AAAA,IACT,OAAS,EAAA,iCAAA;AAAA,IACT,IAAM,EAAA,iCAAA;AAAA,IACN,IAAM,EAAA,wEAAA;AAAA,IACN,IAAM,EAAA,gHAAA;AAAA,IACN,IAAM,EAAA,4CAAA;AAAA,IACN,OAAS,EAAA,iCAAA;AAAA,IACT,OAAS,EAAA,iCAAA;AAAA,IACT,IAAM,EAAA,uEAAA;AAAA,IACN,IAAM,EAAA,gDAAA;AAAA,IACN,IAAM,EAAA,4GAAA;AAAA,IACN,IAAM,EAAA,gIAAA;AAAA,IACN,IAAM,EAAA,0EAAA;AAAA,IACN,IAAM,EAAA;AAAA,GACT;AAAA,EACD,YAAc,EAAA;AAAA,IACV,OAAS,EAAA,mBAAA;AAAA,IACT,OAAS,EAAA,mBAAA;AAAA,IACT,IAAM,EAAA,mBAAA;AAAA,IACN,IAAM,EAAA,sCAAA;AAAA,IACN,IAAM,EAAA,oDAAA;AAAA,IACN,IAAM,EAAA,6BAAA;AAAA,IACN,OAAS,EAAA,mBAAA;AAAA,IACT,OAAS,EAAA,mBAAA;AAAA,IACT,IAAM,EAAA,wCAAA;AAAA,IACN,IAAM,EAAA,6BAAA;AAAA,IACN,IAAM,EAAA,mDAAA;AAAA,IACN,IAAM,EAAA,+DAAA;AAAA,IACN,IAAM,EAAA,kCAAA;AAAA,IACN,IAAM,EAAA;AAAA,GACT;AAAA,EACD,aAAe,EAAA;AAAA,IACX,OAAS,EAAA,mBAAA;AAAA,IACT,OAAS,EAAA,mBAAA;AAAA,IACT,IAAM,EAAA,mBAAA;AAAA,IACN,IAAM,EAAA,wCAAA;AAAA,IACN,IAAM,EAAA,gDAAA;AAAA,IACN,IAAM,EAAA,4BAAA;AAAA,IACN,OAAS,EAAA,mBAAA;AAAA,IACT,OAAS,EAAA,mBAAA;AAAA,IACT,IAAM,EAAA,uCAAA;AAAA,IACN,IAAM,EAAA,6BAAA;AAAA,IACN,IAAM,EAAA,8CAAA;AAAA,IACN,IAAM,EAAA,+DAAA;AAAA,IACN,IAAM,EAAA,uCAAA;AAAA,IACN,IAAM,EAAA;AAAA,GACT;AAAA,EACD,YAAc,EAAA;AAAA,IACV,OAAS,EAAA,mBAAA;AAAA,IACT,OAAS,EAAA,mBAAA;AAAA,IACT,IAAM,EAAA,mBAAA;AAAA,IACN,IAAM,EAAA,oCAAA;AAAA,IACN,IAAM,EAAA,8DAAA;AAAA,IACN,IAAM,EAAA,6BAAA;AAAA,IACN,OAAS,EAAA,mBAAA;AAAA,IACT,OAAS,EAAA,mBAAA;AAAA,IACT,IAAM,EAAA,wCAAA;AAAA,IACN,IAAM,EAAA,8BAAA;AAAA,IACN,IAAM,EAAA,6DAAA;AAAA,IACN,IAAM,EAAA,sEAAA;AAAA,IACN,IAAM,EAAA,yCAAA;AAAA,IACN,IAAM,EAAA;AAAA,GACT;AAAA,EACD,WAAa,EAAA;AAAA,IACT,OAAS,EAAA,kGAAA;AAAA,IACT,OAAS,EAAA,kGAAA;AAAA,IACT,IAAM,EAAA,kGAAA;AAAA,IACN,IAAM,EAAA,mYAAA;AAAA,IACN,IAAM,EAAA,0cAAA;AAAA,IACN,IAAM,EAAA,0IAAA;AAAA,IACN,OAAS,EAAA,kGAAA;AAAA,IACT,OAAS,EAAA,kGAAA;AAAA,IACT,IAAM,EAAA,0UAAA;AAAA,IACN,IAAM,EAAA,2JAAA;AAAA,IACN,IAAM,EAAA,4aAAA;AAAA,IACN,IAAM,EAAA,uUAAA;AAAA,IACN,IAAM,EAAA,2XAAA;AAAA,IACN,IAAM,EAAA;AAAA;AAElB;;;;"}