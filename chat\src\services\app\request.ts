import { PayAppStore, type IDifyAppItem } from '@dify-chat/core'

import { BaseRequest } from '../base-request'
import {message} from 'antd'
import Cookies from 'js-cookie';
// 获取当前域名
const currentHostname = typeof window !== 'undefined' ? window.location.hostname : 'localhost';
const API_BASE_URL = currentHostname === 'ai.medsci.cn' ? 'https://ai.medsci.cn/dev-api/ai-base' :currentHostname === 'ai.medon.com.cn'? 'https://ai.medon.com.cn/dev-api/ai-base':'/ai-base';

const requests = new BaseRequest({ baseURL: API_BASE_URL})
const language = () => {
    return Cookies.get(
        'ai_apps_lang'
    )? Cookies.get('ai_apps_lang')
    : (typeof navigator !== 'undefined' ? (navigator as any).browserLanguage || navigator.language : 'zh')
}
// 根据请求判断
const req = (result: { code: number;data:any, msg?: string }) => {
    if(result.code === 401) {
        Cookies.remove("userInfo", { domain: ".medon.com.cn" });
		Cookies.remove("userInfo", { domain: ".medsci.cn" });
		Cookies.remove("userInfo", { domain: "localhost" });
		Cookies.remove("yudaoToken", { domain: "ai.medon.com.cn" });
		Cookies.remove("yudaoToken", { domain: "ai.medsci.cn" });
		Cookies.remove("yudaoToken", { domain: ".medon.com.cn" });
		Cookies.remove("yudaoToken", { domain: ".medsci.cn" });
		Cookies.remove("yudaoToken", { domain: "localhost" });
        localStorage.removeItem("conversation")
        localStorage.removeItem("hasuraToken");
        localStorage.removeItem("socialUserId");
        localStorage.removeItem("socialType");
        localStorage.removeItem("openid");
        let languages = language();
        // country='xxxx' // 模拟国外
        // 判断是否是国内用户访问
        if (!languages || languages == "zh") {
          if (typeof window !== 'undefined') {
            (window as any).addLoginDom();
          }
        } else {
          // 跳转到获取授权页
          if (typeof window !== 'undefined' && window.top && typeof location !== 'undefined') {
            window.top.location.href = location?.origin.includes("medon.com.cn") || location?.origin.includes("medsci.cn")?`${location?.origin}${languages?'/'+languages:''}/login`:`${location?.origin}${languages?'/'+languages:''}/login`;
          }
        }
        return result
      }
    if(result.code !== 0) {
        message.open({ type: 'error', content: result.msg });
        return result
    }
    if(result.code === 0) {
        return result
    }
  
}
/**
 * 应用列表 CRUD 的 RESTful 实现
 */
class Request extends PayAppStore {
	async createSubscription(params: Record<string, unknown> = {}, headers:Record<string, string> = {} ): Promise<any> {
        let res = await requests.post('/appUser/createSubscription',params,headers);
		return req(res)
	}
    async getAiWriteToken(params: Record<string, unknown> = {}, headers:Record<string, string> = {} ) {
        let res = await requests.post('/index/getAiWriteToken',params,headers);
		return req(res)
	}
    async getSubOrder(params: Record<string, string> = {}, headers:Record<string, string> = {} ) {
        let res = await requests.get('/index/getSubOrder',params,headers);
		return req(res)
	}
    async createAliSub(params: Record<string, string> = {}, headers:Record<string, string> = {} ) {
        let res = await requests.get('/index/createAliSub',params,headers);
		return req(res)
	}
    async freeLimit(params: Record<string, string> = {}, headers:Record<string, string> = {} ) {
        let res = await requests.get('/index/free-limit',params,headers);
		return req(res)
	}
    async getPackageByKey(params: Record<string, string> = {}, headers:Record<string, string> = {} ) {
        let res = await requests.get('/index/getPackageByKey',params,headers);
		return req(res)
	}
    async cancelSubscription(params: Record<string, string> = {}, headers:Record<string, string> = {} ) {
        let res = await requests.post('/appUser/cancelSubscription?appUuid=',{},headers);
		return req(res)
	}
    // 查询所有的应用
    async getAppList(params: Record<string, string> = {}, headers:Record<string, string> = {} ) {
        let res = await requests.post('/index/getAppList',params,headers);
		return req(res)
	}
    // 查询所有的应用
    async getAppByConfigKey(params: Record<string, string> = {}, headers:Record<string, string> = {} ) {
        let res = await requests.post('/index/getAppByConfigKey',params,headers);
		return req(res)
	}
        // 查询活动pro的订阅次数
        async bindAppUser(params: Record<string, string> = {}, headers:Record<string, string> = {} ) {
            let res = await requests.post(`/appUser/bindAppUser?appUuid=${params.appUuid}&appNameEn=${params.appNameEn}`,{},headers);
            return req(res)
        }
      // 查询活动pro的订阅次数
      async qaList(params: Record<string, string> = {}, headers:Record<string, string> = {} ) {
        let res = await requests.post(`/index/qa-list`,params,headers);
        return req(res)
    }
    // 获取所有语言
    async getConfigPage(params: Record<string, string> = {}, headers:Record<string, string> = {} ) {
        let res = await requests.get(`/index/getConfigPage`,params,headers);
        return req(res)
    }
}

export default Request
