<template>
  <div  class="bg-[#F9F9F9] overflow-auto h-full">
    <HeaderNav @getAppLang="getAppLang" :subStatusDetail="subStatusDetail" @isZHChange="isZHChange" @subScript="subScript" />
    <div class="flex flex-col items-center h-[246px] relative min-w-[980px]" :style="{
      background: `url(${bgImg}) no-repeat center`,
      backgroundSize: 'cover',
    }">
      <h1 class="pt-[75px] text-white mb-[30px] font-bold">{{ $t(TITLE) }}</h1>
      <!-- 工具查找 -->
      <div class="flex justify-center">
        <el-input class="!w-[888px] !h-[54px]" v-model="inputValue" :placeholder="$t('market.keywords')" clearable
          @input="handleInputChange">
          <template #prefix>
            <el-icon size="24" class="cursor-pointer mt-[2px]">
              <img class="w-[24px] h-[24px]" src="@/assets/svg/搜索.svg" alt="" />
            </el-icon>
          </template>
        </el-input>
      </div>
    </div>
    <main>
      <div class="content">
        <!-- 工具分类 -->
        <div class="flex justify-center my-8 bg-[#F9F9F9]">
          <div class="flex items-center">
            <div class="mr-2 px-4 py-1 cursor-pointer m_font" :class="typeActive == index ? 'bg-[#409eff] text-white rounded-4xl' : ''
              " v-for="(item, index) in typeList" :key="index" @click="changeType(index, item)">
              {{ $t(`${appTypes[item.remark]}`) }}
            </div>
                <div
                  class="mr-2 px-4  cursor-pointer m_font"
                >
                  <a  href="https://aisite.medsci.cn/" target="_blank">{{ $t("tool.AINavigationSite") }}</a>
                </div>
          </div>
        </div>
        <div class="menu-box flex flex-wrap justify-between" v-if="typeActive != 0">
          <el-card shadow="hover" class="max-w-[369px] cursor-pointer mb-[16px] !bg-[#FBFBFB] !border-0 cursor-item"
            :style="{
              background: `url(${item.isInternalUser == 1 ? bg1 : bg}) no-repeat center`,
              backgroundSize: 'cover',
              width: 'calc(33.33% - 8px)',
            }" v-for="(item, index) in menuList" :key="index" @click="goToPage(item)">
            <div class="flex mb-1 card-item">
              <div class="flex" style="width: 75%; align-items: center">
                <img class="w-[40px] h-[40px] block mr-2" style="border-radius: 10px" :src="item.appIcon" alt="icon" />
                <div class="text-[16px] font-bold text-dark-200 two_lines" style="width: calc(100% - 40px)"
                  :title="item.appName" ><a :href="item.url" @click.stop.prevent="defaultA(item)" :title="item.appName" target="_blank" ><h6 v-html="item.appName"></h6></a></div>
              </div>
              <div style="width: 30%; text-align: right; font-size: 14px">
                <a :href="item.url" @click.stop.prevent="defaultA(item)" :title="item.appName" target="_blank" >
                <el-button style="--el-button-bg-color: #fff" size="small" color="#2F92EE" plain round>
                  {{ $t("market.open")
                  }}<el-icon>
                    <DArrowRight style="margin-left: 4px" />
                  </el-icon>
                </el-button>
                </a>
              </div>
            </div>
            <div class="textOverflowFour tex-[#838383] leading-[18px] h-[72px] mb-[12px]" :title="item.appDescription"
              v-html="item.appDescription"></div>
            <div class="flex justify-between items-center">
              <div class="text-[#B0B0B0]">
                {{ $t(`${appTypes[item.appType]}`) }}
              </div>
              <!-- 订阅中 -->
              <div v-if="item.appUser?.status == 1" class="during_order">
                {{ $t("market.subUntil") }}{{ item.appUser?.expireAt
                }}{{ $t("market.expiredOn") }}
              </div>
              <!-- 已过期 -->
              <div v-if="item.appUser?.status == 2" class="delay_order">
                {{ $t("market.haveBeen") }}{{ item.appUser?.expireAt
                }}{{ $t("market.expiredOn") }}
              </div>
            </div>
          </el-card>
        </div>
        <div v-else class="tab_box">
          <el-tabs v-model="activeTabName" class="demo-tabs" @tab-change="handleTabChange">
            <el-tab-pane :label="$t('market.subscribed')" name="first" />
            <el-tab-pane :label="$t('market.expired')" name="second" />
          </el-tabs>
          <!-- <div class="flex items-center all_search">
            <span class="label_w">{{$t('market.sort')}}</span>
            <el-select v-model="mineSortValue" style="width:200px;" @change="handleMineSortChange">
              <el-option value="1" :label="$t('market.usePer')" />
              <el-option value="2" :label="$t('market.subExpiry')" />
            </el-select>
          </div> -->
          <div class="menu-box flex flex-wrap justify-between">
            <el-card shadow="hover" class="max-w-[369px] cursor-pointer mb-[16px] !bg-[#FBFBFB] !border-0 cursor-item"
              :style="{
                background: `url(${bg}) no-repeat center`,
                backgroundSize: 'cover',
                width: 'calc(33.33% - 8px)',
                maxHeight: '189.5px',
              }" v-for="(item, index) in menuList" :key="index" @click="handleClick(item)">
              <div class="flex mb-1 card-item">
                <div class="flex" style="width: 75%; align-items: center">
                  <img class="w-[40px] h-[40px] block mr-2" style="border-radius: 10px" :src="item.appIcon"
                    alt="icon" />
                  <div class="text-[16px] font-bold text-dark-200 two_lines" style="width: calc(100% - 40px)"
                    :title="item.appName" ><a :href="item.url" @click.stop.prevent="defaultA(item)" :title="item.appName" target="_blank" ><h6 v-html="item.appName"></h6></a></div>
                </div>
                <div style="width: 30%; text-align: right">
                  <a :href="item.url" @click.stop.prevent="defaultA(item)" :title="item.appName" target="_blank" >
                  <el-button v-if="item.appUser?.status == 1" style="--el-button-bg-color: #fff" size="small"
                    color="#2F92EE" plain round>
                    {{ $t("market.open")
                    }}<el-icon>
                      <DArrowRight style="margin-left: 4px" />
                    </el-icon>
                  </el-button>
                  </a>
                  <el-button v-if="item.appUser?.status == 2" style="--el-button-bg-color: #fff" size="small"
                    color="#FF9A45" plain round @click.stop="handleOrder(item)">
                    {{ $t("market.renew")
                    }}<el-icon>
                      <DArrowRight style="margin-left: 4px" />
                    </el-icon>
                  </el-button>
                  <el-button v-if="!item.appUser" style="--el-button-bg-color: #fff" size="small" color="#FF9A45" plain
                    round @click.stop="handleOrder(item)">
                    {{ $t("market.subscribe")
                    }}<el-icon>
                      <DArrowRight style="margin-left: 4px" />
                    </el-icon>
                  </el-button>
                </div>
              </div>
              <div class="textOverflowFour tex-[#838383] leading-[18px] h-[72px] mb-[12px]"
                v-html="item.appDescription">
              </div>
              <div class="flex justify-between items-center">
                <div class="text-[#B0B0B0]">
                  {{ $t(`${appTypes[item.appType]}`) }}
                </div>
                <!-- 订阅中 -->
                <div v-if="item.appUser?.status == 1" class="during_order">
                  {{ $t("market.subUntil") }}{{ item.appUser?.expireAt
                  }}{{ $t("market.expiredOn") }}
                </div>
                <!-- 已过期 -->
                <div v-if="item.appUser?.status == 2" class="delay_order">
                  {{ $t("market.haveBeen") }}{{ item.appUser?.expireAt
                  }}{{ $t("market.expiredOn") }}
                </div>
              </div>
            </el-card>
          </div>
        </div>
      </div>
    </main>
    <customerService v-if="isZH" />
    <FooterNavZH class="mobile_footer" v-if="isZH"/>
    <FooterNav class="mobile_footer" v-if="!isZH"/>
    <el-dialog v-model="payShow" v-if="payShow" class="payPC" :show-close="false">
      <client-only>
        <pay :userInfo="userInfo" :subStatusDetail="subStatusDetail" :appTypes="appTypes" :currentItem="currentItem" @toAgreement="toAgreement"
        @close="close" @subscribe="subscribe" />
      </client-only>
    </el-dialog>
    <van-popup v-model:show="payShow"  round closeable class="payMobile" position="bottom" :style="{ height: '90%' }">
      <client-only>
        <payMobile :userInfo="userInfo" :subStatusDetail="subStatusDetail" :appTypes="appTypes" :currentItem="currentItem" @toAgreement="toAgreement"
          @close="close" />
      </client-only>
    </van-popup>
  </div>
</template>

<script setup>
import cookie from "js-cookie";
import { DArrowRight } from "@element-plus/icons-vue";
import customerService from "@/components/customerService/index.vue";
import pay from "@/components/pay/index.vue";
import payMobile from "@/components/payMobile/index.vue";
import {Cookies} from '@/utils/cookieHandler'
import { Popup as VanPopup , showDialog } from "vant";
import {
  getAppList,
  mainLogin,
  getAppTypes,
  getAppClickNum,
  createSubscription,
  getPackageByKey
} from "@/api/base";
import { getAssetsFile } from "@/utils/index";
import { home } from "@/langs/lang.js"
import {
  appTypes,
  defaultLanguageName,
  languages,
  getDefaultLanguageCode,
} from "@/common/commonJs";
import { showConfirmDialog } from 'vant';
import { ElMessage } from "element-plus";
import getTools from "@/utils/setCookie";
import axios from "axios";
import { useI18n } from "vue-i18n";
import { ref } from "vue";
const { t , locale} = useI18n();
const ai_apps_lang =  useCookie("ai_apps_lang",{domain:import.meta.env.VITE_MODE === "development" ?'localhost':import.meta.env.VITE_MODE === "test" ?".medon.com.cn" : ".medsci.cn",maxAge: 30 * 24 * 60 * 60 * 12});

// 设置页面标题和其他meta信息

const isUp =
  location?.origin.includes("medon.com.cn") ||
  location?.origin.includes("medsci.cn");
const TITLE = "faq.xAI";
const bg = getAssetsFile("基于AI的写作文本加工.png");
const bg1 = getAssetsFile("基于AI的写作文本加工In.png");
// const dialogImg = {
//   '包月':getAssetsFile("包月.png"),
//   '包季':getAssetsFile("包季.png"),
//   '包年':getAssetsFile("包年.png"),
// }
const route = useRoute();
const router = useRouter();
// 搜索框值
const inputValue = ref("");
// 首页分类切换数据
const typeList = ref([
  {
    value: "我的应用",
    remark: "我的应用",
  },
  {
    value: "",
    remark: "全部",
  },
]);
// 是否是中文
const isZH = ref(false);
const typeActive = ref(1);
const bgImg = ref(null);
// const sortValue = ref('2')
// 我的应用列表
// const mineSortValue = ref('1')
const userInfo = ref(null);
const activeTabName = ref("first");
// 订阅事件
const currentItem = ref({});
const payShow = ref(false);
const defaultA =async (item) => {
  if (!item?.dAppUuid) {
    ElMessage({
      message: "请先至后台绑定应用实例",
      type: "warning",
    });
    return;
  }
  await getAppClickNum(item.appUuid, localStorage.getItem("openid"));
  // let node = allData.find((n) => n.name == item.name);
  sessionStorage.setItem("nodeInfo", JSON.stringify(item));
  if(isMobile()&&item.appType=="写作"){
    showDialog({ title: '提示',message: '写作类智能体请使用电脑web端打开，获取最佳使用体验。' ,confirmButtonColor:'#D7813F'});

  }else{
    if(item.appType=="写作"){
      localStorage.setItem(
      "appWrite" + '-' + item.appUuid,
      JSON.stringify({
        appUuid: item.appUuid,
        directoryMd: item.directoryMd,
      })
    );
    }
    window.open(item.url) 
  }
}

// 请求参数
const params = ref({
  appType: "",
  socialUserId: "", //三方用户ID，如用户主站ID,登录时传
  appLang: "", //语言
  order: 2, //1使用频率/订阅热度，2订阅到期/点击热度
  isMine: 2, // 1我的，2其他
});
// const handleSortChange = () => {
//   params.value.order = sortValue.value
// getAppListData()
// }
// const handleMineSortChange = () => {
//   params.value.order = mineSortValue.value
// getAppListData()
// }
// 方法封装（返回true则为移动端）
const isMobile = () => {
  return /(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry)/i
    .test(navigator.userAgent);
}

const close = () => {
  payShow.value = false;
};
// 跳转到协议
// const toAgreement = ()=>{
//   window.open("https://www.medsci.cn/about/index.do?id=27")
// }
// 打点
const insert = async () => {
  let data = [
    {
      userRandomId:
        Math.random().toString(36).substr(2, 9) + Date.now().toString(36),
      title: '',
      refer: "",
      userAgen: '',
      time: new Date().getTime(),
      url: '',
      actionValue: "",
      userAction: "Exposure",
      actionCode: null,
      userId: userInfo.value?.userId,
      userToken: "",
      channel: "MedSci_xAI",
      appId: "",
      userUuid: userInfo.value?.openid,
    },
  ];
  // 使用 axios 发送 POST 请求
  await axios.post(
    "https://app-trace.medsci.cn/api/points/v1/user-action-batch",
    data
  );
};
const subScript = async () => {
  if(locale.value == "zh"){
    subStatusDetail.value = await getPackageByKey();
    payShow.value = true;
  }else{
    payShow.value = true;
  }
}
// 支付
const subscribe = async (item, appUuid) => {
  let language = getDefaultLanguageCode(locale.value);
  if (!userInfo.value?.userId) {
    // country='xxxx' // 模拟国外
    // 判断是否是国内用户访问
    if (!language || language == "zh") {
      window.addLoginDom();
    } else {
      // 跳转到获取授权页
      location.href = location.origin+ '/' +locale.value + "/login";
    }
  } else {
    const subscriptionParams = {
      appUuid: appUuid,
      priceId: item.priceId,
      monthNum: item.monthNum,
    };
    let res = await createSubscription(subscriptionParams);
    if (res) {
      ElMessage({
        type: "success",
        message: t("tool.sS"),
      });
      setTimeout(() => {
        location.href = res;
      }, 1000);
    }
  }
};
const handleClick = (item) => {
  if (item.appUser?.status == 1) {
    goToPage(item);
  } else {
    handleOrder(item);
  }
};
// 输入框变化时
// 输入框变化时
const handleInputChange = (val) => {
  // 如果搜索值为空，恢复原始列表
  if (!val) {
    menuList.value = JSON.parse(JSON.stringify(allData));
    return;
  }

  let list = [];
  
  // 根据当前选中的分类过滤数据
  if (typeActive.value == 1) { // "全部"分类
    list = JSON.parse(JSON.stringify(allData));
  } else if (typeActive.value != 0) { // 其他具体分类
    list = JSON.parse(JSON.stringify(allData)).filter(
      (item) => item.appType === typeList.value[typeActive.value].value
    );
  } else if (typeActive.value == 0) { // 默认情况
    list = JSON.parse(JSON.stringify(menuList.value));
  }

  // 创建忽略大小写的正则表达式，并转义特殊字符
  const escapedVal = val.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  const reg = new RegExp(escapedVal, "gi");

  // 模糊匹配：检查标题、描述、分类是否包含搜索词
  const highlightList = list.filter((item) => {
    return (
      reg.test(item.appName) ||
      reg.test(item.appDescription) ||
      reg.test(item.mapType)
    );
  });

  // 高亮处理函数
  const highlightText = (text) => {
    if (!text) return text;
    return text.replace(reg, (match) => 
      `<span style="color: #409eff">${match}</span>`
    );
  };

  // 更新菜单列表并添加高亮
  menuList.value = highlightList.map((item) => ({
    ...item,
    appName: highlightText(item.appName),
    appDescription: highlightText(item.appDescription),
    mapType: highlightText(item.mapType)
  }));
};
const checkCookie = () => {
  const newVal = cookie.get('userInfo');
  if (newVal !== userInfo.value) userInfo.value = newVal ? JSON.parse(newVal) : '';
  if (userInfo.value) {
    if (cookie.get('yudaoToken')) {
      clearInterval(intervalId);
    }
    loginMain();
  }
};

let intervalId;
onMounted(() => {
  intervalId = setInterval(checkCookie, 1000);
});

onBeforeUnmount(() => {
  if (intervalId) clearInterval(intervalId);
});
// 切换分类
const changeType = async (index, item) => {
  let language = await getDefaultLanguageCode(locale.value);
  typeActive.value = index;
  // 输入框有值时，需要再次筛选
  inputValue.value = "";
  if (inputValue.value) {
    handleInputChange(inputValue.value);
  }
  // 我的应用tab 判断用户是否登录
  if (!userInfo.value?.userId && typeActive.value == 0) {
    menuList.value = [];
    if (!language || language == "zh") {
      window.addLoginDom();
    } else {
      // 跳转到获取授权页
      location.href = location.origin+ '/' +locale.value + "/login";
    }
    return;
  } else {
    if (typeActive.value != 0) {
      params.value.isMine = 2;
      params.value.order = 2;
      if (item.remark == "全部") {
        params.value.appType = "";
      } else {
        params.value.appType = item.value;
      }
      params.value.socialUserId = userInfo.value.plaintextUserId;
    } else {
      activeTabName.value = "first";
      params.value.appType = "";
      params.value.isMine = 1;
      params.value.order = 1;
      params.value.socialUserId = userInfo.value.plaintextUserId;
    }
    getAppListData();
  }
};

const goToPage = async (item) => {
  if (!item?.dAppUuid) {
    ElMessage({
      message: "请先至后台绑定应用实例",
      type: "warning",
    });
    return;
  }
  getAppClickNum(item.appUuid, localStorage.getItem("openid"));
  // let node = allData.find((n) => n.name == item.name);
  sessionStorage.setItem("nodeInfo", JSON.stringify(item));
  const currentUrl = window.location.origin.includes("medsci.cn") || window.location.origin.includes("medon.com.cn") ? window.location.origin : window.location.origin;
  if (item.appType === "工具") {
    window.open(
      `${currentUrl}${locale.value=='zh'?'':('/'+locale.value)}/tool/${item?.appNameEn}`,
      "_blank"
    );
    return;
  } else if (item.appType === "问答") {
    window.open(
      `${currentUrl}${locale.value=='zh'?'':('/'+locale.value)}/chat/${item?.appNameEn}`,
      "_blank"
    );
    return;
  } else if (item.appType === "写作") {
    if(isMobile()){
      showDialog({ title: '提示',message: '写作类智能体请使用电脑web端打开，获取最佳使用体验。',confirmButtonColor:'#D7813F',});
      return;
    }
    await getTools();
    localStorage.setItem(
      "appWrite" + '-' + item.appUuid,
      JSON.stringify({
        appUuid: item.appUuid,
        directoryMd: item.directoryMd,
      })
    );
    window.open(
      `${window.location.origin}${locale.value=='zh'?'':('/'+locale.value)}/write/${item.appNameEn}`
    );
  }
};
let allData = [];
const subStatusDetail = ref()

const menuList = ref(null);
const bgImgs = ref([
  "https://img.medsci.cn/medsci-ai/bg1.png?imageMogr2/format/webp",
  "https://img.medsci.cn/medsci-ai/bg2.png?imageMogr2/format/webp",
  "https://img.medsci.cn/medsci-ai/bg3.png?imageMogr2/format/webp",
  "https://img.medsci.cn/medsci-ai/bg4.png?imageMogr2/format/webp",
  "https://img.medsci.cn/medsci-ai/bg5.png?imageMogr2/format/webp",
  "https://img.medsci.cn/medsci-ai/bg6.png?imageMogr2/format/webp",
]);
const { data: appListData } = await useAsyncData('getAppList', async () => {
  const event = process.server ? useRequestEvent() : null;
  const userInfo = useCookie("userInfo");
  if (userInfo.value?.userId) {
    params.value.socialUserId = userInfo.value.plaintextUserId;
  }
  // const header = useRequestHeaders();
  // header['accept-language'] = header['accept-language'] ? header['accept-language']:'zh,zh;q=0.9,en;q=0.8,zh-HK;q=0.7'
  // // ai_apps_lang.value = ai_apps_lang.value ? ai_apps_lang.value: (header['accept-language'].split(',')[0]=='zh'||header['accept-language'].split(',')[0]=='zh'?'zh':header['accept-language'].split(',')[0].split('-')[0].toLowerCase());
  ai_apps_lang.value = locale.value;
  params.value.appLang = defaultLanguageName(locale.value);
  params.value.languages = locale.value;
  let PackageByKey
  if(locale.value == "zh"){
    PackageByKey = await getPackageByKey(event);
  }
  const res = await getAppList(params.value,event)
  
  const res1 = await  getAppTypes(ai_apps_lang.value,event)
  let index = Math.floor(Math.random() * 6);
  if (res) {
    res.forEach((item) => {
      item.mapType = appTypes[item.appType];
    });
    res.forEach((item) => {
    if (item.appType === "工具") {
      item.url = import.meta.env.VITE_MODE=="development"?('http://localhost:3000'+`${locale.value=='zh'?'':('/'+locale.value)}/tool/${item?.appNameEn}`):import.meta.env.VITE_MODE=="test"?("https://ai.medon.com.cn"+`${locale.value=='zh'?'':('/'+locale.value)}/tool/${item?.appNameEn}`):import.meta.env.VITE_MODE=="prd"?("https://ai.medsci.cn"+`${locale.value=='zh'?'':('/'+locale.value)}/tool/${item?.appNameEn}`):""
    } else if (item.appType === "问答") {
      item.url = import.meta.env.VITE_MODE=="development"?('http://localhost:3000'+`${locale.value=='zh'?'':('/'+locale.value)}/chat/${item?.appNameEn}`):import.meta.env.VITE_MODE=="test"?("https://ai.medon.com.cn"+`${locale.value=='zh'?'':('/'+locale.value)}/chat/${item?.appNameEn}`):import.meta.env.VITE_MODE=="prd"?("https://ai.medsci.cn"+`${locale.value=='zh'?'':('/'+locale.value)}/chat/${item?.appNameEn}`):""
    } else if (item.appType === "写作") {
      item.url = import.meta.env.VITE_MODE=="development"?('http://localhost:3000'+`${locale.value=='zh'?'':('/'+locale.value)}/write/${item?.appNameEn}`):import.meta.env.VITE_MODE=="test"?("https://ai.medon.com.cn"+`${locale.value=='zh'?'':('/'+locale.value)}/write/${item?.appNameEn}`):import.meta.env.VITE_MODE=="prd"?("https://ai.medsci.cn"+`${locale.value=='zh'?'':('/'+locale.value)}/write/${item?.appNameEn}`):""
    }
    });
    console.log("appListData", 11);
    // 初始化时先获取一次数据
    return [res,res1,index,PackageByKey]; // 确保返回数据
  }
  return null; // 确保总是有返回值
}, { server: true });
console.log("appListData", 2222);
allData = JSON.parse(JSON.stringify(appListData?.value&&appListData?.value[0]));
menuList.value = appListData?.value&&appListData?.value[0];
appListData?.value&&typeList.value.push(...appListData?.value[1]);
bgImg.value = bgImgs.value[1];
subStatusDetail.value = appListData?.value&&appListData?.value[3]
useHead({
  title:  `${home['title'][ai_apps_lang.value]}`,
  meta: [
    {
      name: 'keywords',
      content: `${home['keywords'][ai_apps_lang.value]}` ,
    },
    {
      name: 'description',
      content: `${home['description'][ai_apps_lang.value]}`
    },
    { property: 'og:type', content: 'website' },
    { property: 'og:title', content: `${home['title'][ai_apps_lang.value]}` },
    { property: 'og:description', content: `${home['description'][ai_apps_lang.value]}` },
    { property: 'og:image', content: 'https://img.medsci.cn/202412/8a5663cb16a84c45b9c8c5db4eda0075-6pyGtTcj0asd.png' },
    { name: 'twitter:card', content: 'summary_large_image' }, // 注意：根据常见用法推断 content 为 'summary_large_image'
    { name: 'twitter:title', content: `${home['title'][ai_apps_lang.value]}` },
    { name: 'twitter:description', content: `${home['description'][ai_apps_lang.value]}` },
    { name: 'twitter:image', content: 'https://img.medsci.cn/202412/8a5663cb16a84c45b9c8c5db4eda0075-6pyGtTcj0asd.png' }
  ]
});

onMounted(async () => {
  const windowVH = window.innerHeight / 100;
  document.documentElement.style.setProperty("--vh", `${windowVH}px`);
  let arr = [
    "zh",
    "en",
    "es",
    "ja",
    "tw",
    "vi",
    "ko",
    "pt",
    "ar",
    "id",
    "ms",
  ];

  // if (arr.includes(route.params?.lang)) {
  //   ai_apps_lang.value =  route.params?.lang;
  // } else {
  //   if(ai_apps_lang.value) {
  //     ai_apps_lang.value= ai_apps_lang.value ? ai_apps_lang.value
  //     : navigator.browserLanguage || navigator.language ||'zh';
  //   }
  // }

  if (ai_apps_lang.value == "zh") {
    isZH.value = true;
  }
  userInfo.value = cookie.get("userInfo")
    ? JSON.parse(cookie.get("userInfo"))
    : null;
  if (!userInfo.value) {
    cookie.remove("yudaoToken",{ domain: "ai.medsci.cn" });
    cookie.remove("yudaoToken",{ domain: "ai.medon.com.cn" });
    cookie.remove("yudaoToken",{ domain: ".medsci.cn" });
    cookie.remove("yudaoToken",{ domain: ".medon.com.cn" });
    cookie.remove("yudaoToken",{ domain: "localhost" });
    localStorage.removeItem("hasuraToken");
  }
  if (!route.query.lang) {
    params.value.appLang = defaultLanguageName(locale.value);
  } else {
    params.value.appLang = languages[route.query.lang];
  }

  if (userInfo.value?.userId) {
    // loginMain();
  } else {
    params.value.socialUserId = 0;
    if (defaultLanguageName(locale.value)) {
      params.value.appLang = defaultLanguageName(locale.value);
    } else {
      getAppLang(location.pathname.replaceAll("/", ""));
    }
  }
  insert();
});
// 获取tab
const getTabList = () => {
  getAppTypes()
    .then((res) => {
      typeList.value.push(...res);
    })
    .catch();
};
// 重置小助手显示
const isZHChange = (val) => {
  isZH.value = val;
};
// 获取语言
const getAppLang = (val) => {
  params.value.appLang = languages[val];
  getAppListData();
};
// 获取应用list
const getAppListData = () => {
  getAppList(params.value)
    .then((res) => {
      res.forEach((item) => {
        if (item.appType === "工具") {
          item.url = (process.env.VITE_MODE=="development"?'http://localhost:3000':process.env.VITE_MODE=="test"?"https://ai.medon.com.cn":process.env.VITE_MODE=="prd"?"https://ai.medsci.cn":'')+`${locale.value=='zh'?'':('/'+locale.value)}/tool/${item?.appNameEn}`
        } else if (item.appType === "问答") {
          item.url = (process.env.VITE_MODE=="development"?'http://localhost:3000':process.env.VITE_MODE=="test"?"https://ai.medon.com.cn":process.env.VITE_MODE=="prd"?"https://ai.medsci.cn":'')+`${locale.value=='zh'?'':('/'+locale.value)}/chat/${item?.appNameEn}`
        } else if (item.appType === "写作") {
          item.url = (process.env.VITE_MODE=="development"?'http://localhost:3000':process.env.VITE_MODE=="test"?"https://ai.medon.com.cn":process.env.VITE_MODE=="prd"?"https://ai.medsci.cn":'')+`${locale.value=='zh'?'':('/'+locale.value)}/write/${item?.appNameEn}`
        }
      });
      menuList.value = res?.map((item) => {
        return {
          ...item,
          mapType: appTypes[item.appType],
        };
      });
      if (params.value.appType == "") {
        allData = [...menuList.value];
      }
      if (params.value.isMine == 1) {
        if (activeTabName.value == "first") {
          menuList.value = menuList.value?.filter(
            (item) => item.appUser?.status == 1
          );
        }
        if (activeTabName.value == "second") {
          menuList.value = menuList.value?.filter(
            (item) => item.appUser?.status == 2
          );
        }
      }
    })
    .catch((err) => {
      console.log(err);
    });
};
// 获取芋道token
const loginMain = () => {
  const token = Cookies.get("yudaoToken");
  if (token) {
    // getAppListData();
    return;
  }
  const userInfoStr = cookie.get("userInfo");
  if (userInfoStr) {
    const userInfo = JSON.parse(userInfoStr);
    try {
      mainLogin({
        userId: userInfo.userId,
        userName: userInfo.userName,
        realName: userInfo.realName,
        avatar: userInfo.avatar,
        plaintextUserId: userInfo.plaintextUserId,
        mobile: userInfo.mobile,
        email: userInfo.email,
      }).then((res) => {
        if (res?.token) {
          Cookies.set("yudaoToken", res.token);
          localStorage.setItem("hasuraToken", res.htoken);
          localStorage.setItem("openid", res.openid);
          localStorage.setItem("socialUserId", res.socialUserId);
          localStorage.setItem("socialType", res.socialType);
          // getAppListData();
        } else {
          console.error("登录失败: 未返回 token");
        }
      });
    } catch (error) {
      console.log(error, "登录失败pc");
    }
  }
};
// 处理订阅事件
const handleOrder = async (item) => {
  // router.push('/payType?appUuid='+item.appUuid)
  currentItem.value = item;
  payShow.value = true;
};
const handleTabChange = () => {
  getAppListData();
};
</script>

<style lang="scss" scoped>
:deep(.el-dialog) {
  padding: 0px !important;
  width: 679px;
  border-radius: 20px;

  .el-dialog__header {
    padding: 0px !important;
  }
}

:deep() {
  .van-popup__close-icon {
    top: 7px !important;
  }

  .payMobile {
    display: none;
  }

  .payPC {
    display: block;
  }
}

.content {
  min-height: calc(var(--vh) * 100 - 56px - 246px - 60px - 2rem - 1px);
  max-width: 1140px;
  min-width: 980px;
  margin: 0 auto;
}

.dialog_container {
  background: #f8f9fa;
  border-radius: 20px;

  .dialog_top {
    height: 52px;
    background: url("@/assets/imgs/Bitmap.png") no-repeat;
    background-size: 100% 100%;
    display: flex;
    justify-items: center;
    align-items: center;
    justify-content: space-between;

    .close {
      margin-right: 29px;
      opacity: 1;
      width: 27px;
      height: 27px;
    }

    .img {
      width: 27px;
      height: 27px;
      border-radius: 50%;
      margin-left: 44px;
      margin-right: 7px;
    }

    .name {
      font-family: PingFangSC-Medium;
      font-weight: 500;
      font-size: 16px;
      color: #58342c;
    }
  }

  .dialog_detail {
    width: 621px;
    height: 54px;
    background: #ffffff;
    border: 1px solid #f6d278;
    border-radius: 12px;
    margin: 19px auto;
    margin-bottom: 13px;
    text-align: center;

    .type {
      margin: 6px auto;
      margin-bottom: 0px;
      background: url("@/assets/imgs/typeBg.png") center bottom no-repeat;
      background-size: 81px 11px;
      font-family: PingFangSC-Medium;
      font-weight: 500;
      font-size: 16px;
      color: #6b462a;
    }

    .title {
      font-family: PingFangSC-Regular;
      font-weight: 400;
      font-size: 12px;
      color: #7a5f4a;
    }
  }

  .dialog_pay {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 621px;
    background: #ffffff;
    margin: auto;
    padding: 18px 20px 0px;

    .pay_item {
      width: 180px;
      height: 175px;
      border: 1px solid #fbd888;
      border-radius: 7px;
      text-align: center;
      margin-left: 20px;

      .type {
        font-family: PingFangSC-SNaNpxibold;
        font-weight: 600;
        font-size: 16px;
        color: #745137;
        margin-top: 59px;
      }

      .price {
        font-family: NotoSansKannada-Bold;
        font-weight: 700;
        font-size: 28px;
        color: #efa439;
        margin-bottom: 7px;

        span {
          font-family: PingFangSC-SNaNpxibold;
          font-weight: 600;
          font-size: 12px;
          color: #dfb34a;
          margin-right: 3px;
        }
      }

      .btn {
        height: 28px;
        background-image: linear-gradient(270deg, #ffc85e 9%, #fca315 93%);
        border-radius: 18px;
        color: #fff;
        text-align: center;
        line-height: 13px;
      }
    }
  }

  .dialog_desc {
    width: 621px;
    background: #ffffff;
    padding: 16px 20px 18px;
    margin: auto;

    .title {
      font-family: PingFangSC-Medium;
      font-weight: 500;
      font-size: 14px;
      color: #333333;
      margin-bottom: 6px;
    }

    .content1 {
      width: 580px !important;
      font-family: PingFangSC-Regular;
      font-weight: 400;
      font-size: 12px;
      color: #666666;
    }
  }

  .dialog_proto {
    width: 100%;
    height: 52px;
    font-family: PingFangSC-Regular;
    font-weight: 400;
    font-size: 12px;
    color: #999999;
    padding-right: 28px;
    display: flex;
    align-items: center;
    justify-content: end;

    .icon:hover {
      cursor: pointer;
    }
  }
}

:deep(.el-input__wrapper) {
  box-shadow: none;
  padding: 1px 21px;
}

.all_search {
  margin-bottom: 16px;

  .label_w {
    // width: 40px;
    margin-right: 5px;
  }
}

.one_line {
  white-space: nowrap;
  /* 防止元素换行 */
  overflow: hidden;
  /* 超出部分隐藏 */
  text-overflow: ellipsis;
  /* 超出部分显示省略号 */
}

.two_lines {
  display: -webkit-box;
  /* 使用弹性伸缩盒子模型 */
  -webkit-line-clamp: 2;
  /* 限制在一个块元素显示的文本的行数 */
  -webkit-box-orient: vertical;
  /* 设置或检索伸缩盒对象的子元素的排列方式 */
  white-space: normal;
  /* 允许文本换行 */
  overflow: hidden;
  /* 超出部分隐藏 */
  text-overflow: ellipsis;
  /* 超出部分显示省略号 */
}

.menu-box:after {
  content: "";
  display: block;
  width: 369px;
}

.card-item {
  justify-content: space-between;
  align-items: center;
}

.during_order {
  font-size: 14px;
  color: #2f92ee;
}

.delay_order {
  font-size: 14px;
  color: #ff9a45;
}

.textOverflowFour {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 4;
  line-clamp: 4;
  -webkit-box-orient: vertical;
}

@media screen and (max-width: 768px) {
  :deep() {
    .el-overlay {
      display: none;
    }

    .payMobile {
      display: block;
    }

    .payPC {
      display: none;
    }
  }

  .overflow-auto {
    width: 100%;
    overflow-x: hidden;

    .payPC {
      display: none !important;
    }

    .payMobile {
      display: block;
    }

    .\!w-\[888px\] {
      width: 333.5px !important;
    }

    .\!h-\[54px\] {
      height: 33.5px !important;
    }

    .min-w-\[980px\] {
      min-width: 100%;
    }

    .h-\[246px\] {
      height: 111.5px;
    }

    .pt-\[75px\] {
      padding-top: 0;
    }

    .mb-\[30px\] {
      margin: 14px auto;
    }

    .w-\[40px\] {
      width: 20px;
    }

    .h-\[40px\] {
      height: 20px;
    }

    .two_lines {
      font-size: 14px;
    }

    .textOverflowFour {
      white-space: break-spaces;
      height: 42px;
      -webkit-line-clamp: 3;
      margin-top: 8px;
      color: #838383;
      font-size: 12px;
      line-height: 14px;
    }

    .during_order {
      font-size: 12px;
    }

    .text-\[\#B0B0B0\] {
      font-size: 12px;
    }

    .mb-\[12px\] {
      margin-bottom: 2px;
    }

    .mobile_footer {
      display: none;
    }

    :deep() {
      .el-dialog {
        width: 100%;
      }

      .el-input__inner {
        font-size: 12px;
      }

      .el-tabs__item {
        font-size: 12px;
      }

      .current_lang {
        font-size: 14px;
      }

      .ms-link {
        font-size: 12px;
      }

      .assistant-icon {
        width: 45px;
      }
    }

    .content {
      min-width: calc(100% - 28px);

      .items-center {
        overflow-x: auto;
        padding-top: 12px;
      }

      .items-center::-webkit-scrollbar {
        display: none;
      }

      .my-8 {
        margin-top: 0;
        margin-bottom: 0;

        .items-center {
          overflow-x: auto;
          padding-top: 12px;
          padding-bottom: 12px;
        }
      }

      .cursor-pointer {
        white-space: nowrap;
      }

      .m_font {
        font-size: 12px;
      }

      .cursor-item {
        width: calc(100% - 28px) !important;
      }

      .menu-box {
        justify-content: center;
        height: calc(var(--vh) * 100 - 218.5px);
        width: 100%;
        overflow-y: auto;
        align-items: flex-start;
        align-content: flex-start;
      }

      .tab_box {
        .menu-box {
          justify-content: center;
          height: calc(var(--vh) * 100 - 218.5px - 55px);
          width: 100%;
          overflow-y: auto;
        }
      }

      :deep() {
        .el-input__inner {
          font-size: 12px;
        }

        .el-card__body {
          padding: 10px;
        }

        .el-tabs__nav-scroll {
          width: calc(100% - 28px);
          margin: 0 auto;
        }

        .el-tabs__nav-wrap::after {
          width: calc(100% - 28px);
          left: 14px;
        }
      }
    }
  }
}
</style>

<style></style>